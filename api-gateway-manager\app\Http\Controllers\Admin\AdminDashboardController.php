<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\RequestLog;

class AdminDashboardController extends Controller
{
    /**
     * Show the admin dashboard
     */
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_developers' => User::where('role', 'developer')->count(),
            'total_apps' => App::count(),
            'total_api_keys' => ApiKey::count(),
            'active_api_keys' => ApiKey::where('is_active', true)->count(),
            'requests_today' => RequestLog::whereDate('requested_at', today())->count(),
            'requests_this_week' => RequestLog::where('requested_at', '>=', now()->startOfWeek())->count(),
        ];

        $recentUsers = User::latest()->take(5)->get();
        $recentApps = App::with('user')->latest()->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'recentApps'));
    }
}
