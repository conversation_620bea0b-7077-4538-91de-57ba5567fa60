<?php

namespace App\Http\Controllers\Developer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RequestLog;
use Illuminate\Support\Facades\Auth;

class DeveloperDashboardController extends Controller
{
    /**
     * Show the developer dashboard
     */
    public function index()
    {
        $user = Auth::user();

        $apps = $user->apps()->with(['apiKeys' => function($query) {
            $query->where('is_active', true);
        }])->get();

        $totalApiKeys = $user->apps()->withCount('apiKeys')->get()->sum('api_keys_count');
        $activeApiKeys = $user->apps()->withCount(['apiKeys' => function($query) {
            $query->where('is_active', true);
        }])->get()->sum('api_keys_count');

        // Get API key IDs for this user's apps
        $apiKeyIds = $user->apps()->with('apiKeys')->get()
            ->pluck('apiKeys')->flatten()->pluck('id');

        $stats = [
            'total_apps' => $apps->count(),
            'total_api_keys' => $totalApiKeys,
            'active_api_keys' => $activeApiKeys,
            'requests_today' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->whereDate('requested_at', today())->count(),
            'requests_this_week' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', now()->startOfWeek())->count(),
        ];

        return view('developer.dashboard', compact('apps', 'stats'));
    }
}
