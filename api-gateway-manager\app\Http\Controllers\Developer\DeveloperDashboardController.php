<?php

namespace App\Http\Controllers\Developer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RequestLog;
use Illuminate\Support\Facades\Auth;

class DeveloperDashboardController extends Controller
{
    /**
     * Show the developer dashboard
     */
    public function index()
    {
        $user = Auth::user();

        $apps = $user->apps()->with(['apiKeys' => function($query) {
            $query->where('is_active', true);
        }])->get();

        $totalApiKeys = $user->apps()->withCount('apiKeys')->get()->sum('api_keys_count');
        $activeApiKeys = $user->apps()->withCount(['apiKeys' => function($query) {
            $query->where('is_active', true);
        }])->get()->sum('api_keys_count');

        // Get API key IDs for this user's apps
        $apiKeyIds = $user->apps()->with('apiKeys')->get()
            ->pluck('apiKeys')->flatten()->pluck('id');

        // Calculate success rate
        $totalRequests = RequestLog::whereIn('api_key_id', $apiKeyIds)->count();
        $successfulRequests = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->whereBetween('status_code', [200, 299])->count();
        $successRate = $totalRequests > 0 ? ($successfulRequests / $totalRequests) * 100 : 100;

        $stats = [
            'total_apps' => $apps->count(),
            'active_keys' => $activeApiKeys,
            'calls_today' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->whereDate('requested_at', today())->count(),
            'success_rate' => $successRate,
        ];

        // Get recent apps (limit to 5)
        $recent_apps = $user->apps()->withCount('apiKeys')
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent activity logs (limit to 10)
        $recent_logs = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->orderBy('requested_at', 'desc')
            ->limit(10)
            ->get();

        return view('developer.dashboard', compact('stats', 'recent_apps', 'recent_logs'));
    }
}
