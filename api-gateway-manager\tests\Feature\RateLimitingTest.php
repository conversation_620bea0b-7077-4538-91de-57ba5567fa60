<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\ApiProxy;
use App\Models\RateLimit;
use Carbon\Carbon;

class RateLimitingTest extends TestCase
{
    use RefreshDatabase;

    public function test_rate_limit_creation()
    {
        $apiKey = ApiKey::factory()->create();
        $apiProxy = ApiProxy::factory()->create();

        $rateLimit = RateLimit::getOrCreate($apiKey->id, $apiProxy->id);

        $this->assertInstanceOf(RateLimit::class, $rateLimit);
        $this->assertEquals($apiKey->id, $rateLimit->api_key_id);
        $this->assertEquals($apiProxy->id, $rateLimit->api_proxy_id);
        $this->assertEquals(60, $rateLimit->requests_per_minute);
        $this->assertEquals(1000, $rateLimit->requests_per_hour);
        $this->assertEquals(10000, $rateLimit->requests_per_day);
    }

    public function test_rate_limit_counter_increment()
    {
        $rateLimit = RateLimit::factory()->create([
            'current_minute_count' => 0,
            'current_hour_count' => 0,
            'current_day_count' => 0,
        ]);

        $rateLimit->incrementCounters();

        $this->assertEquals(1, $rateLimit->current_minute_count);
        $this->assertEquals(1, $rateLimit->current_hour_count);
        $this->assertEquals(1, $rateLimit->current_day_count);
    }

    public function test_rate_limit_exceeded_detection()
    {
        $rateLimit = RateLimit::factory()->create([
            'requests_per_minute' => 5,
            'current_minute_count' => 5,
            'minute_reset_at' => Carbon::now()->addMinute(),
        ]);

        $this->assertTrue($rateLimit->isExceeded());

        $rateLimit->current_minute_count = 4;
        $rateLimit->save();

        $this->assertFalse($rateLimit->isExceeded());
    }

    public function test_rate_limit_counter_reset()
    {
        $rateLimit = RateLimit::factory()->create([
            'current_minute_count' => 10,
            'minute_reset_at' => Carbon::now()->subMinute(), // Past reset time
        ]);

        // This should trigger a reset when checked
        $rateLimit->isExceeded();

        $this->assertEquals(0, $rateLimit->current_minute_count);
        $this->assertTrue($rateLimit->minute_reset_at->isFuture());
    }

    public function test_admin_can_create_custom_rate_limit()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $apiKey = ApiKey::factory()->create();
        $apiProxy = ApiProxy::factory()->create();

        $response = $this->actingAs($admin)->post('/admin/rate-limits', [
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requests_per_minute' => 100,
            'requests_per_hour' => 5000,
            'requests_per_day' => 50000,
        ]);

        $response->assertRedirect('/admin/rate-limits');
        $this->assertDatabaseHas('rate_limits', [
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requests_per_minute' => 100,
            'requests_per_hour' => 5000,
            'requests_per_day' => 50000,
        ]);
    }

    public function test_admin_can_reset_rate_limit_counters()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $rateLimit = RateLimit::factory()->create([
            'current_minute_count' => 50,
            'current_hour_count' => 500,
            'current_day_count' => 5000,
        ]);

        $response = $this->actingAs($admin)->post("/admin/rate-limits/{$rateLimit->id}/reset");

        $response->assertRedirect();
        $rateLimit->refresh();

        $this->assertEquals(0, $rateLimit->current_minute_count);
        $this->assertEquals(0, $rateLimit->current_hour_count);
        $this->assertEquals(0, $rateLimit->current_day_count);
    }

    public function test_developer_cannot_access_rate_limit_management()
    {
        $developer = User::factory()->create(['role' => 'developer']);
        $rateLimit = RateLimit::factory()->create();

        $response = $this->actingAs($developer)->get('/admin/rate-limits');
        $response->assertStatus(403);

        $response = $this->actingAs($developer)->post("/admin/rate-limits/{$rateLimit->id}/reset");
        $response->assertStatus(403);
    }

    public function test_rate_limit_prevents_duplicate_creation()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $apiKey = ApiKey::factory()->create();
        $apiProxy = ApiProxy::factory()->create();

        // Create first rate limit
        $this->actingAs($admin)->post('/admin/rate-limits', [
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requests_per_minute' => 100,
            'requests_per_hour' => 5000,
            'requests_per_day' => 50000,
        ]);

        // Try to create duplicate
        $response = $this->actingAs($admin)->post('/admin/rate-limits', [
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requests_per_minute' => 200,
            'requests_per_hour' => 10000,
            'requests_per_day' => 100000,
        ]);

        $response->assertSessionHasErrors(['api_key_id']);
    }

    public function test_rate_limit_validation()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->post('/admin/rate-limits', [
            'api_key_id' => 999999, // Non-existent
            'api_proxy_id' => 999999, // Non-existent
            'requests_per_minute' => 0, // Invalid
            'requests_per_hour' => -1, // Invalid
            'requests_per_day' => 'invalid', // Invalid
        ]);

        $response->assertSessionHasErrors([
            'api_key_id',
            'api_proxy_id',
            'requests_per_minute',
            'requests_per_hour',
            'requests_per_day',
        ]);
    }
}
