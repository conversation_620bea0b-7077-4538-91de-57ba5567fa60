@extends('layouts.developer')

@php
    $pageTitle = 'Edit API Key';
@endphp

@section('title', 'Edit API Key - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Edit API Key</h1>
                <p class="mt-2 text-secondary-600">Update settings for {{ $apiKey->name }}</p>
            </div>
            <a href="{{ route('developer.api-keys.show', $apiKey) }}" class="btn-outline">
                Back to Details
            </a>
        </div>
    </div>

    <div class="max-w-3xl">

    <div class="card">
        <div class="card-body">
            <form action="{{ route('developer.api-keys.update', $apiKey) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="form-group">
                    <label for="name" class="form-label">API Key Name *</label>
                    <input type="text" name="name" id="name" class="form-input" value="{{ old('name', $apiKey->name) }}" required>
                    <p class="form-help">A descriptive name to help you identify this API key</p>
                    @error('name')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="form-group">
                    <label class="form-label">Application</label>
                    <div class="flex items-center p-3 bg-secondary-50 rounded-lg">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">{{ $apiKey->app->name }}</div>
                            <div class="text-sm text-secondary-500">{{ $apiKey->app->description }}</div>
                        </div>
                    </div>
                    <p class="form-help">The application cannot be changed after creation</p>
                </div>

                <div class="form-group">
                    <label class="form-label">Status</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="1" class="form-radio" {{ old('is_active', $apiKey->is_active) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-secondary-700">Active - API key can be used</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="is_active" value="0" class="form-radio" {{ !old('is_active', $apiKey->is_active) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-secondary-700">Inactive - API key is disabled</span>
                        </label>
                    </div>
                    @error('is_active')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="expires_at" class="form-label">Expiration Date</label>
                    <input type="datetime-local" name="expires_at" id="expires_at" class="form-input" 
                           value="{{ old('expires_at', $apiKey->expires_at ? $apiKey->expires_at->format('Y-m-d\TH:i') : '') }}">
                    <p class="form-help">Leave empty for no expiration</p>
                    @error('expires_at')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-yellow-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-yellow-800">Important Notes</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Disabling this API key will immediately stop all requests using it</li>
                                    <li>Setting an expiration date will automatically disable the key after that time</li>
                                    <li>You cannot change the actual API key value - create a new one if needed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4">
                    <a href="{{ route('developer.api-keys.show', $apiKey) }}" class="btn-outline">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Update API Key
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mt-8">
        <div class="card border-danger-200">
            <div class="card-header bg-danger-50">
                <h3 class="text-lg font-medium text-danger-900">Danger Zone</h3>
            </div>
            <div class="card-body">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="text-sm font-medium text-secondary-900">Delete API Key</h4>
                        <p class="text-sm text-secondary-600 mt-1">
                            Permanently delete this API key. This action cannot be undone and will immediately stop all requests using this key.
                        </p>
                    </div>
                    <form action="{{ route('developer.api-keys.destroy', $apiKey) }}" method="POST" class="ml-4">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-danger" onclick="return confirm('Are you sure you want to delete this API key? This action cannot be undone.')">
                            Delete API Key
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>
</div>
@endsection
