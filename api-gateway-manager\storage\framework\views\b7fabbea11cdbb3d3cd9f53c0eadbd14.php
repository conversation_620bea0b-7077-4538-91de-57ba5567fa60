<?php
    $pageTitle = 'Admin Dashboard';
?>

<?php $__env->startSection('title', 'Admin Dashboard - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Admin Dashboard
        </h1>
        <p class="mt-2 text-secondary-600">
            System overview and management tools for <?php echo e(config('app.name')); ?>.
        </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- Total Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Total Users</p>
                        <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['total_users'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Apps -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Active Apps</p>
                        <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['active_apps'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Proxies -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">API Proxies</p>
                        <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['api_proxies'] ?? 0); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests Today -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-danger-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Requests Today</p>
                        <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['requests_today'] ?? 0)); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
        <!-- Recent Users -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-secondary-900">Recent Users</h3>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="text-sm text-danger-600 hover:text-danger-500">
                        View all
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if(isset($recent_users) && $recent_users->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $recent_users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <span class="text-primary-600 font-semibold text-sm">
                                        <?php echo e(substr($user->name, 0, 2)); ?>

                                    </span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-secondary-900"><?php echo e($user->name); ?></p>
                                    <p class="text-xs text-secondary-500"><?php echo e($user->email); ?></p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="badge-<?php echo e($user->role === 'admin' ? 'danger' : 'primary'); ?>">
                                    <?php echo e(ucfirst($user->role)); ?>

                                </span>
                                <span class="badge-<?php echo e($user->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($user->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No users yet</h3>
                        <p class="text-sm text-secondary-500">Users will appear here as they register</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- System Status -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-secondary-900">System Status</h3>
                    <span class="badge-success">Operational</span>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full bg-success-500 mr-3"></div>
                            <span class="text-sm text-secondary-700">API Gateway</span>
                        </div>
                        <span class="text-sm font-medium text-success-600">Online</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full bg-success-500 mr-3"></div>
                            <span class="text-sm text-secondary-700">Database</span>
                        </div>
                        <span class="text-sm font-medium text-success-600">Connected</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full bg-success-500 mr-3"></div>
                            <span class="text-sm text-secondary-700">Rate Limiting</span>
                        </div>
                        <span class="text-sm font-medium text-success-600">Active</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full bg-success-500 mr-3"></div>
                            <span class="text-sm text-secondary-700">Request Logging</span>
                        </div>
                        <span class="text-sm font-medium text-success-600">Enabled</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full bg-warning-500 mr-3"></div>
                            <span class="text-sm text-secondary-700">Cache</span>
                        </div>
                        <span class="text-sm font-medium text-warning-600">File-based</span>
                    </div>
                </div>
                
                <div class="mt-6 pt-4 border-t border-secondary-200">
                    <div class="text-sm text-secondary-600">
                        <div class="flex justify-between mb-1">
                            <span>Uptime</span>
                            <span class="font-medium">99.9%</span>
                        </div>
                        <div class="flex justify-between mb-1">
                            <span>Avg Response Time</span>
                            <span class="font-medium"><?php echo e($stats['avg_response_time'] ?? '150'); ?>ms</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Error Rate</span>
                            <span class="font-medium"><?php echo e(number_format($stats['error_rate'] ?? 0.1, 1)); ?>%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="card mb-8">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-secondary-900">Recent API Activity</h3>
                <a href="<?php echo e(route('admin.request-logs.index')); ?>" class="text-sm text-danger-600 hover:text-danger-500">
                    View all logs
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if(isset($recent_logs) && $recent_logs->count() > 0): ?>
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead class="table-header">
                            <tr>
                                <th class="table-header-cell">Time</th>
                                <th class="table-header-cell">Method</th>
                                <th class="table-header-cell">Endpoint</th>
                                <th class="table-header-cell">Status</th>
                                <th class="table-header-cell">Response Time</th>
                                <th class="table-header-cell">User</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            <?php $__currentLoopData = $recent_logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="table-cell">
                                    <span class="text-xs text-secondary-500">
                                        <?php echo e($log->requested_at->format('H:i:s')); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-<?php echo e($log->method === 'GET' ? 'success' : ($log->method === 'POST' ? 'primary' : 'warning')); ?> text-xs">
                                        <?php echo e($log->method); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <code class="text-xs"><?php echo e($log->path); ?></code>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-<?php echo e($log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning')); ?> text-xs">
                                        <?php echo e($log->response_status); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="text-sm"><?php echo e($log->response_time_ms); ?>ms</span>
                                </td>
                                <td class="table-cell">
                                    <span class="text-sm text-secondary-600">
                                        <?php echo e($log->apiKey->app->user->name ?? 'Unknown'); ?>

                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="text-sm font-medium text-secondary-900 mb-2">No activity yet</h3>
                    <p class="text-sm text-secondary-500">API requests will appear here once users start making calls</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Management Actions -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <a href="<?php echo e(route('admin.users.create')); ?>" class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                </div>
                <h4 class="font-medium text-secondary-900 mb-1">Add User</h4>
                <p class="text-sm text-secondary-600">Create new admin or developer account</p>
            </div>
        </a>

        <a href="<?php echo e(route('admin.api-proxies.create')); ?>" class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                </div>
                <h4 class="font-medium text-secondary-900 mb-1">New API Proxy</h4>
                <p class="text-sm text-secondary-600">Configure new backend service</p>
            </div>
        </a>

        <a href="<?php echo e(route('admin.analytics.index')); ?>" class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <h4 class="font-medium text-secondary-900 mb-1">View Analytics</h4>
                <p class="text-sm text-secondary-600">System usage and performance</p>
            </div>
        </a>

        <a href="<?php echo e(route('admin.request-logs.index')); ?>" class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-danger-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="h-6 w-6 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h4 class="font-medium text-secondary-900 mb-1">Request Logs</h4>
                <p class="text-sm text-secondary-600">Monitor API usage and errors</p>
            </div>
        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>