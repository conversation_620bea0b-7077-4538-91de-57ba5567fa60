<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\App;
use Illuminate\Support\Facades\Auth;

class AppController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->apps()->withCount('apiKeys');

        // Search functionality
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        $apps = $query->orderBy('updated_at', 'desc')->paginate(12);

        return view('developer.apps.index', compact('apps'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('developer.apps.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'callback_url' => 'nullable|url|max:255',
            'base_url' => 'nullable|url|max:255',
            'authorization_token' => 'nullable|string|max:500',
            'custom_headers' => 'nullable|array',
            'custom_headers.*.name' => 'required_with:custom_headers|string|max:100',
            'custom_headers.*.value' => 'required_with:custom_headers|string|max:500',
        ]);

        // Process custom headers
        $customHeaders = [];
        if ($request->has('custom_headers')) {
            foreach ($request->custom_headers as $header) {
                if (!empty($header['name']) && !empty($header['value'])) {
                    $customHeaders[$header['name']] = $header['value'];
                }
            }
        }

        $app = Auth::user()->apps()->create([
            'name' => $request->name,
            'description' => $request->description,
            'callback_url' => $request->callback_url,
            'base_url' => $request->base_url,
            'authorization_token' => $request->authorization_token,
            'custom_headers' => !empty($customHeaders) ? $customHeaders : null,
            'is_active' => true,
        ]);

        return redirect()->route('developer.apps.show', $app)
            ->with('success', 'Application created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(App $app)
    {
        $this->authorize('view', $app);

        $app->load(['apiKeys' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return view('developer.apps.show', compact('app'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(App $app)
    {
        $this->authorize('update', $app);
        return view('developer.apps.edit', compact('app'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, App $app)
    {
        $this->authorize('update', $app);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'callback_url' => 'nullable|url|max:255',
            'base_url' => 'nullable|url|max:255',
            'authorization_token' => 'nullable|string|max:500',
            'custom_headers' => 'nullable|array',
            'custom_headers.*.name' => 'required_with:custom_headers|string|max:100',
            'custom_headers.*.value' => 'required_with:custom_headers|string|max:500',
            'is_active' => 'boolean',
        ]);

        // Process custom headers
        $customHeaders = [];
        if ($request->has('custom_headers')) {
            foreach ($request->custom_headers as $header) {
                if (!empty($header['name']) && !empty($header['value'])) {
                    $customHeaders[$header['name']] = $header['value'];
                }
            }
        }

        $app->update([
            'name' => $request->name,
            'description' => $request->description,
            'callback_url' => $request->callback_url,
            'base_url' => $request->base_url,
            'authorization_token' => $request->authorization_token,
            'custom_headers' => !empty($customHeaders) ? $customHeaders : null,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('developer.apps.show', $app)
            ->with('success', 'Application updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(App $app)
    {
        $this->authorize('delete', $app);

        $app->delete();

        return redirect()->route('developer.apps.index')
            ->with('success', 'Application deleted successfully!');
    }
}
