<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\App;
use Illuminate\Support\Facades\Auth;

class AppController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $apps = Auth::user()->apps()->with('apiKeys')->paginate(10);
        return view('apps.index', compact('apps'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('apps.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'callback_url' => 'nullable|url|max:255',
        ]);

        $app = Auth::user()->apps()->create([
            'name' => $request->name,
            'description' => $request->description,
            'callback_url' => $request->callback_url,
            'is_active' => true,
        ]);

        return redirect()->route('apps.show', $app)
            ->with('success', 'Application created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(App $app)
    {
        $this->authorize('view', $app);

        $app->load(['apiKeys' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return view('apps.show', compact('app'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(App $app)
    {
        $this->authorize('update', $app);
        return view('apps.edit', compact('app'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, App $app)
    {
        $this->authorize('update', $app);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'callback_url' => 'nullable|url|max:255',
            'is_active' => 'boolean',
        ]);

        $app->update($request->only(['name', 'description', 'callback_url', 'is_active']));

        return redirect()->route('apps.show', $app)
            ->with('success', 'Application updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(App $app)
    {
        $this->authorize('delete', $app);

        $app->delete();

        return redirect()->route('apps.index')
            ->with('success', 'Application deleted successfully!');
    }
}
