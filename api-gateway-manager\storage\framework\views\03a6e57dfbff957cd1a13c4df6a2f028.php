<?php $__env->startSection('title', 'Pricing - ' . config('app.name')); ?>
<?php $__env->startSection('meta_description', 'Simple, transparent pricing for API Gateway Manager. Start free and scale as you grow.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="pricing-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#pricing-grid)" />
        </svg>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                14-day free trial • No credit card required
            </div>

            <h1 class="text-4xl font-bold sm:text-5xl lg:text-6xl mb-6">
                Simple, transparent
                <span class="text-primary-200">pricing</span>
            </h1>
            <p class="text-xl text-primary-100 max-w-3xl mx-auto mb-8">
                Start free and scale as you grow. No hidden fees, no surprises.
                Choose the plan that fits your needs and upgrade anytime.
            </p>

            <!-- Billing Toggle -->
            <div class="flex items-center justify-center space-x-4 mb-8">
                <span class="text-primary-200">Monthly</span>
                <div class="relative">
                    <input type="checkbox" id="billing-toggle" class="sr-only" onchange="toggleBilling()">
                    <label for="billing-toggle" class="flex items-center cursor-pointer">
                        <div class="relative">
                            <div class="block bg-white bg-opacity-20 w-14 h-8 rounded-full"></div>
                            <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition transform"></div>
                        </div>
                    </label>
                </div>
                <span class="text-primary-200">
                    Annual
                    <span class="inline-flex items-center px-2 py-1 bg-success-500 text-white text-xs font-medium rounded-full ml-2">
                        Save 20%
                    </span>
                </span>
            </div>

            <!-- Trust Indicators -->
            <div class="flex flex-wrap justify-center gap-6 text-primary-200 text-sm">
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Cancel anytime</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>30-day money back</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Instant setup</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Cards Section -->
<div class="bg-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-6">
            <!-- Free Plan -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-shadow duration-300">
                <div class="p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900 mb-2">Starter</h3>
                        <p class="text-secondary-600 mb-6">Perfect for getting started and small projects</p>
                        <div class="mb-8">
                            <div class="monthly-price">
                                <span class="text-5xl font-bold text-secondary-900">$0</span>
                                <span class="text-secondary-600 text-lg">/month</span>
                            </div>
                            <div class="annual-price" style="display: none;">
                                <span class="text-5xl font-bold text-secondary-900">$0</span>
                                <span class="text-secondary-600 text-lg">/year</span>
                            </div>
                        </div>
                        <div class="text-sm text-secondary-500 mb-6">Forever free</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Up to 3 APIs</span>
                                <div class="text-sm text-secondary-600">Manage up to 3 different APIs</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">10,000 requests/month</span>
                                <div class="text-sm text-secondary-600">Perfect for development and testing</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Basic analytics</span>
                                <div class="text-sm text-secondary-600">Request counts and response times</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Rate limiting</span>
                                <div class="text-sm text-secondary-600">Basic protection for your APIs</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Community support</span>
                                <div class="text-sm text-secondary-600">Access to community forums</div>
                            </div>
                        </li>
                    </ul>

                    <a href="<?php echo e(route('register')); ?>" class="w-full inline-flex items-center justify-center px-6 py-3 border border-secondary-300 text-base font-medium rounded-lg text-secondary-700 bg-white hover:bg-secondary-50 transition-colors duration-200">
                        Get Started Free
                    </a>
                </div>
            </div>

            <!-- Pro Plan -->
            <div class="relative bg-white rounded-2xl shadow-xl border-2 border-primary-500 hover:shadow-2xl transition-shadow duration-300 transform scale-105">
                <!-- Popular Badge -->
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-full shadow-lg">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        Most Popular
                    </span>
                </div>

                <div class="p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900 mb-2">Professional</h3>
                        <p class="text-secondary-600 mb-6">Perfect for growing businesses and production APIs</p>
                        <div class="mb-8">
                            <div class="monthly-price">
                                <span class="text-5xl font-bold text-primary-600">$29</span>
                                <span class="text-secondary-600 text-lg">/month</span>
                            </div>
                            <div class="annual-price" style="display: none;">
                                <span class="text-5xl font-bold text-primary-600">$23</span>
                                <span class="text-secondary-600 text-lg">/month</span>
                                <div class="text-sm text-secondary-500 mt-1">Billed annually ($279/year)</div>
                            </div>
                        </div>
                        <div class="monthly-price">
                            <div class="text-sm text-secondary-500 mb-6">14-day free trial</div>
                        </div>
                        <div class="annual-price" style="display: none;">
                            <div class="text-sm text-success-600 font-medium mb-6">
                                <span class="line-through text-secondary-400">$348/year</span>
                                <span class="ml-2">Save $69 annually</span>
                            </div>
                        </div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Unlimited APIs</span>
                                <div class="text-sm text-secondary-600">No limits on the number of APIs</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">1M requests/month</span>
                                <div class="text-sm text-secondary-600">High-volume API traffic support</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Advanced analytics</span>
                                <div class="text-sm text-secondary-600">Detailed insights and custom dashboards</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Priority support</span>
                                <div class="text-sm text-secondary-600">Email support with 4-hour response</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Custom rate limits</span>
                                <div class="text-sm text-secondary-600">Fine-grained control over API access</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Webhooks & alerts</span>
                                <div class="text-sm text-secondary-600">Real-time notifications and integrations</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Team collaboration</span>
                                <div class="text-sm text-secondary-600">Up to 10 team members</div>
                            </div>
                        </li>
                    </ul>

                    <div class="monthly-price">
                        <a href="<?php echo e(route('register')); ?>" class="w-full inline-flex items-center justify-center px-6 py-3 bg-primary-600 text-white text-base font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Start 14-Day Free Trial
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </a>
                    </div>
                    <div class="annual-price" style="display: none;">
                        <a href="<?php echo e(route('register')); ?>" class="w-full inline-flex items-center justify-center px-6 py-3 bg-primary-600 text-white text-base font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Start Annual Plan
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Enterprise Plan -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-shadow duration-300">
                <div class="p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900 mb-2">Enterprise</h3>
                        <p class="text-secondary-600 mb-6">For large organizations with custom needs</p>
                        <div class="mb-8">
                            <div class="monthly-price">
                                <span class="text-5xl font-bold text-secondary-900">Custom</span>
                            </div>
                            <div class="annual-price" style="display: none;">
                                <span class="text-5xl font-bold text-secondary-900">Custom</span>
                            </div>
                        </div>
                        <div class="text-sm text-secondary-500 mb-6">Contact us for pricing</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Unlimited everything</span>
                                <div class="text-sm text-secondary-600">No limits on APIs, requests, or features</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Custom volume pricing</span>
                                <div class="text-sm text-secondary-600">Tailored pricing for your usage</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">White-label solution</span>
                                <div class="text-sm text-secondary-600">Fully branded for your organization</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">24/7 dedicated support</span>
                                <div class="text-sm text-secondary-600">Phone, email, and chat support</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">99.99% SLA guarantee</span>
                                <div class="text-sm text-secondary-600">Enterprise-grade reliability</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Custom integrations</span>
                                <div class="text-sm text-secondary-600">Tailored solutions for your stack</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">On-premise deployment</span>
                                <div class="text-sm text-secondary-600">Deploy in your own infrastructure</div>
                            </div>
                        </li>
                    </ul>

                    <a href="<?php echo e(route('portal.support')); ?>" class="w-full inline-flex items-center justify-center px-6 py-3 border border-secondary-300 text-base font-medium rounded-lg text-secondary-700 bg-white hover:bg-secondary-50 transition-colors duration-200">
                        Contact Sales Team
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feature Comparison Section -->
    <div class="bg-secondary-50 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                    Compare all features
                </h2>
                <p class="text-lg text-secondary-600">
                    See exactly what's included in each plan
                </p>
            </div>

            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-secondary-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-sm font-medium text-secondary-900">Features</th>
                                <th class="px-6 py-4 text-center text-sm font-medium text-secondary-900">Starter</th>
                                <th class="px-6 py-4 text-center text-sm font-medium text-secondary-900">Professional</th>
                                <th class="px-6 py-4 text-center text-sm font-medium text-secondary-900">Enterprise</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-secondary-200">
                            <tr>
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">APIs</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Up to 3</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Unlimited</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Unlimited</td>
                            </tr>
                            <tr class="bg-secondary-25">
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">Monthly Requests</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">10,000</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">1,000,000</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Custom</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">Analytics</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Basic</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Advanced</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Enterprise</td>
                            </tr>
                            <tr class="bg-secondary-25">
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">Support</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Community</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Priority Email</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">24/7 Dedicated</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">Team Members</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">1</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">10</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">Unlimited</td>
                            </tr>
                            <tr class="bg-secondary-25">
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">SLA</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">99%</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">99.9%</td>
                                <td class="px-6 py-4 text-center text-sm text-secondary-600">99.99%</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">Custom Integrations</td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-secondary-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-secondary-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-success-600 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                            </tr>
                            <tr class="bg-secondary-25">
                                <td class="px-6 py-4 text-sm text-secondary-900 font-medium">White-label</td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-secondary-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-secondary-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                                <td class="px-6 py-4 text-center">
                                    <svg class="w-5 h-5 text-success-600 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="bg-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                    Frequently Asked Questions
                </h2>
                <p class="text-lg text-secondary-600">
                    Everything you need to know about our pricing and plans
                </p>
            </div>

            <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        Can I change plans anytime?
                    </h3>
                    <p class="text-secondary-600">
                        Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.
                    </p>
                </div>

                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        What happens if I exceed my API call limit?
                    </h3>
                    <p class="text-secondary-600">
                        Your API calls will be rate limited once you reach your monthly quota. You can upgrade your plan instantly or wait for the next billing cycle to reset.
                    </p>
                </div>

                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        Is there a free trial for paid plans?
                    </h3>
                    <p class="text-secondary-600">
                        Yes, all paid plans come with a 14-day free trial. No credit card required to start, and you can cancel anytime during the trial period.
                    </p>
                </div>

                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        Do you offer refunds?
                    </h3>
                    <p class="text-secondary-600">
                        We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact us for a full refund.
                    </p>
                </div>

                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        How does billing work?
                    </h3>
                    <p class="text-secondary-600">
                        We bill monthly or annually based on your preference. Annual plans save 20%. All billing is handled securely through Stripe.
                    </p>
                </div>

                <div class="bg-secondary-50 rounded-lg p-6">
                    <h3 class="font-semibold text-secondary-900 mb-3 text-lg">
                        Can I get a custom plan?
                    </h3>
                    <p class="text-secondary-600">
                        Absolutely! If our standard plans don't fit your needs, contact our sales team for a custom solution tailored to your requirements.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-primary-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white sm:text-4xl mb-4">
                Ready to get started?
            </h2>
            <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
                Join thousands of developers who trust our platform to manage their APIs.
                Start your free trial today.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo e(route('register')); ?>" class="inline-flex items-center justify-center px-8 py-3 bg-white text-primary-600 text-lg font-semibold rounded-lg hover:bg-primary-50 transition-colors duration-200 shadow-lg">
                    Start Free Trial
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
                <a href="<?php echo e(route('portal.support')); ?>" class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-white text-lg font-semibold rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200">
                    Contact Sales
                </a>
            </div>
        </div>
    </div>

<script>
function toggleBilling() {
    const toggle = document.getElementById('billing-toggle');
    const dot = document.querySelector('.dot');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const annualPrices = document.querySelectorAll('.annual-price');

    if (toggle.checked) {
        // Annual billing
        dot.style.transform = 'translateX(24px)';
        monthlyPrices.forEach(price => price.style.display = 'none');
        annualPrices.forEach(price => price.style.display = 'block');
    } else {
        // Monthly billing
        dot.style.transform = 'translateX(0)';
        monthlyPrices.forEach(price => price.style.display = 'block');
        annualPrices.forEach(price => price.style.display = 'none');
    }
}

// Initialize pricing display
document.addEventListener('DOMContentLoaded', function() {
    toggleBilling();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/portal/pricing.blade.php ENDPATH**/ ?>