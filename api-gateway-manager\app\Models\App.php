<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class App extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'callback_url',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user that owns the app
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the API keys for this app
     */
    public function apiKeys()
    {
        return $this->hasMany(ApiKey::class);
    }

    /**
     * Get active API keys for this app
     */
    public function activeApiKeys()
    {
        return $this->hasMany(ApiKey::class)->where('is_active', true);
    }
}
