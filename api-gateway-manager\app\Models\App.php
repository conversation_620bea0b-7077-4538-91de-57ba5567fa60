<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class App extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'callback_url',
        'is_active',
        'custom_headers',
        'authorization_token',
        'base_url',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'custom_headers' => 'array',
        ];
    }

    /**
     * Get the user that owns the app
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the API keys for this app
     */
    public function apiKeys()
    {
        return $this->hasMany(ApiKey::class);
    }

    /**
     * Get the API proxies for this app
     */
    public function apiProxies()
    {
        return $this->hasMany(ApiProxy::class);
    }

    /**
     * Scope to get active apps
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive apps
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Get active API keys for this app
     */
    public function activeApiKeys()
    {
        return $this->hasMany(ApiKey::class)->where('is_active', true);
    }
}
