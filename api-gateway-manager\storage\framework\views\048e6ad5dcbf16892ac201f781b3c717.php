<?php
    $pageTitle = 'API Keys';
?>

<?php $__env->startSection('title', 'API Keys - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">API Keys</h1>
                <p class="mt-2 text-secondary-600">Manage your API keys across all applications</p>
            </div>
            <a href="<?php echo e(route('developer.api-keys.create')); ?>" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create API Key
            </a>
        </div>
    </div>

    <?php if($apiKeys->count() > 0): ?>
        <div class="card">
            <div class="card-body p-0">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Application</th>
                                <th>Key Prefix</th>
                                <th>Status</th>
                                <th>Expires</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $apiKeys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $apiKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td class="table-cell">
                                        <div class="font-medium text-secondary-900"><?php echo e($apiKey->name); ?></div>
                                    </td>
                                    <td class="table-cell">
                                        <div class="flex items-center">
                                            <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                                                <svg class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="font-medium text-secondary-900"><?php echo e($apiKey->app->name); ?></div>
                                                <div class="text-sm text-secondary-500"><?php echo e($apiKey->app->description); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="table-cell">
                                        <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($apiKey->key_prefix); ?>...</code>
                                    </td>
                                    <td class="table-cell">
                                        <?php if($apiKey->is_active && (!$apiKey->expires_at || $apiKey->expires_at->isFuture())): ?>
                                            <span class="badge-success">Active</span>
                                        <?php elseif($apiKey->expires_at && $apiKey->expires_at->isPast()): ?>
                                            <span class="badge-warning">Expired</span>
                                        <?php else: ?>
                                            <span class="badge-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="table-cell">
                                        <?php if($apiKey->expires_at): ?>
                                            <span class="text-sm text-secondary-600"><?php echo e($apiKey->expires_at->format('M j, Y')); ?></span>
                                        <?php else: ?>
                                            <span class="text-sm text-secondary-500">Never</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="table-cell">
                                        <span class="text-sm text-secondary-600"><?php echo e($apiKey->created_at->format('M j, Y')); ?></span>
                                    </td>
                                    <td class="table-cell">
                                        <div class="flex items-center space-x-2">
                                            <a href="<?php echo e(route('developer.api-keys.show', $apiKey)); ?>" class="btn-sm btn-outline">
                                                View
                                            </a>
                                            <a href="<?php echo e(route('developer.api-keys.edit', $apiKey)); ?>" class="btn-sm btn-secondary">
                                                Edit
                                            </a>
                                            <form action="<?php echo e(route('developer.api-keys.destroy', $apiKey)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this API key?')">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="mt-6">
            <?php echo e($apiKeys->links()); ?>

        </div>
    <?php else: ?>
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-secondary-900">No API keys</h3>
            <p class="mt-1 text-sm text-secondary-500">Get started by creating your first API key.</p>
            <div class="mt-6">
                <a href="<?php echo e(route('developer.api-keys.create')); ?>" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create API Key
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/api-keys/index.blade.php ENDPATH**/ ?>