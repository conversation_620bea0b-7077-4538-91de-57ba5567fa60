<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RequestLog;
use App\Models\ApiProxy;
use App\Models\ApiKey;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{


    /**
     * Show analytics dashboard
     */
    public function index(Request $request)
    {
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        // Overall statistics
        $stats = [
            'total_requests' => RequestLog::where('requested_at', '>=', $startDate)->count(),
            'successful_requests' => RequestLog::where('requested_at', '>=', $startDate)->where('response_status', '<', 400)->count(),
            'error_requests' => RequestLog::where('requested_at', '>=', $startDate)->where('response_status', '>=', 400)->count(),
            'avg_response_time' => RequestLog::where('requested_at', '>=', $startDate)->avg('response_time_ms'),
            'unique_api_keys' => RequestLog::where('requested_at', '>=', $startDate)->distinct('api_key_id')->count('api_key_id'),
        ];

        // Requests by day
        $requestsByDay = RequestLog::where('requested_at', '>=', $startDate)
            ->selectRaw('DATE(requested_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top API proxies
        $topProxies = RequestLog::where('requested_at', '>=', $startDate)
            ->select('api_proxy_id')
            ->selectRaw('COUNT(*) as request_count')
            ->selectRaw('AVG(response_time_ms) as avg_response_time')
            ->with('apiProxy')
            ->groupBy('api_proxy_id')
            ->orderBy('request_count', 'desc')
            ->take(10)
            ->get();

        // Status code distribution
        $statusCodes = RequestLog::where('requested_at', '>=', $startDate)
            ->selectRaw('
                CASE
                    WHEN response_status < 300 THEN "2xx Success"
                    WHEN response_status < 400 THEN "3xx Redirect"
                    WHEN response_status < 500 THEN "4xx Client Error"
                    ELSE "5xx Server Error"
                END as status_group,
                COUNT(*) as count
            ')
            ->groupBy('status_group')
            ->get();

        // Response time distribution
        $responseTimeDistribution = RequestLog::where('requested_at', '>=', $startDate)
            ->selectRaw('
                CASE
                    WHEN response_time_ms < 100 THEN "< 100ms"
                    WHEN response_time_ms < 500 THEN "100-500ms"
                    WHEN response_time_ms < 1000 THEN "500ms-1s"
                    WHEN response_time_ms < 5000 THEN "1-5s"
                    ELSE "> 5s"
                END as time_range,
                COUNT(*) as count
            ')
            ->groupBy('time_range')
            ->get();

        return view('admin.analytics.index', compact(
            'stats',
            'requestsByDay',
            'topProxies',
            'statusCodes',
            'responseTimeDistribution',
            'days'
        ));
    }

    /**
     * Get analytics data as JSON for charts
     */
    public function data(Request $request)
    {
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $data = [
            'requests_by_hour' => $this->getRequestsByHour($startDate),
            'top_endpoints' => $this->getTopEndpoints($startDate),
            'error_rates' => $this->getErrorRates($startDate),
        ];

        return response()->json($data);
    }

    /**
     * Get requests grouped by hour
     */
    private function getRequestsByHour(Carbon $startDate)
    {
        return RequestLog::where('requested_at', '>=', $startDate)
            ->selectRaw('HOUR(requested_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
    }

    /**
     * Get top endpoints by request count
     */
    private function getTopEndpoints(Carbon $startDate)
    {
        return RequestLog::where('requested_at', '>=', $startDate)
            ->select('path')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('AVG(response_time_ms) as avg_response_time')
            ->groupBy('path')
            ->orderBy('count', 'desc')
            ->take(10)
            ->get();
    }

    /**
     * Get error rates by API proxy
     */
    private function getErrorRates(Carbon $startDate)
    {
        return RequestLog::where('requested_at', '>=', $startDate)
            ->select('api_proxy_id')
            ->selectRaw('
                COUNT(*) as total_requests,
                SUM(CASE WHEN response_status >= 400 THEN 1 ELSE 0 END) as error_requests,
                (SUM(CASE WHEN response_status >= 400 THEN 1 ELSE 0 END) / COUNT(*) * 100) as error_rate
            ')
            ->with('apiProxy')
            ->groupBy('api_proxy_id')
            ->having('total_requests', '>', 10) // Only include proxies with significant traffic
            ->orderBy('error_rate', 'desc')
            ->get();
    }
}
