<?php
    $pageTitle = 'Analytics';
?>

<?php $__env->startSection('title', 'Analytics - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Analytics Dashboard
        </h1>
        <p class="mt-2 text-secondary-600">
            Monitor your API usage, performance, and trends.
        </p>
    </div>

    <!-- Time Range Filter -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="flex flex-col sm:flex-row sm:items-end sm:space-x-4 space-y-4 sm:space-y-0">
                <div class="flex-1">
                    <label for="date_range" class="block text-sm font-medium text-secondary-700 mb-1">Time Range</label>
                    <select name="date_range" id="date_range" class="form-select" onchange="this.form.submit()">
                        <option value="7d" <?php echo e(request('date_range', '7d') === '7d' ? 'selected' : ''); ?>>Last 7 days</option>
                        <option value="30d" <?php echo e(request('date_range') === '30d' ? 'selected' : ''); ?>>Last 30 days</option>
                        <option value="90d" <?php echo e(request('date_range') === '90d' ? 'selected' : ''); ?>>Last 90 days</option>
                        <option value="1y" <?php echo e(request('date_range') === '1y' ? 'selected' : ''); ?>>Last year</option>
                    </select>
                </div>
                
                <div class="sm:w-48">
                    <label for="app_filter" class="block text-sm font-medium text-secondary-700 mb-1">Application</label>
                    <select name="app_filter" id="app_filter" class="form-select" onchange="this.form.submit()">
                        <option value="">All Apps</option>
                        <?php if(isset($userApps)): ?>
                            <?php $__currentLoopData = $userApps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $app): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($app->id); ?>" <?php echo e(request('app_filter') == $app->id ? 'selected' : ''); ?>>
                                    <?php echo e($app->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-2 gap-4 sm:grid-cols-4 mb-8">
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-primary-600"><?php echo e(number_format($metrics['total_requests'] ?? 0)); ?></div>
                <div class="text-sm text-secondary-600 mt-1">Total Requests</div>
                <?php if(isset($metrics['requests_change'])): ?>
                <div class="text-xs mt-1 <?php echo e($metrics['requests_change'] >= 0 ? 'text-success-600' : 'text-danger-600'); ?>">
                    <?php echo e($metrics['requests_change'] >= 0 ? '+' : ''); ?><?php echo e(number_format($metrics['requests_change'], 1)); ?>%
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-success-600"><?php echo e(number_format($metrics['success_rate'] ?? 0, 1)); ?>%</div>
                <div class="text-sm text-secondary-600 mt-1">Success Rate</div>
                <?php if(isset($metrics['success_change'])): ?>
                <div class="text-xs mt-1 <?php echo e($metrics['success_change'] >= 0 ? 'text-success-600' : 'text-danger-600'); ?>">
                    <?php echo e($metrics['success_change'] >= 0 ? '+' : ''); ?><?php echo e(number_format($metrics['success_change'], 1)); ?>%
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-warning-600"><?php echo e(number_format($metrics['avg_response_time'] ?? 0)); ?>ms</div>
                <div class="text-sm text-secondary-600 mt-1">Avg Response</div>
                <?php if(isset($metrics['response_change'])): ?>
                <div class="text-xs mt-1 <?php echo e($metrics['response_change'] <= 0 ? 'text-success-600' : 'text-danger-600'); ?>">
                    <?php echo e($metrics['response_change'] >= 0 ? '+' : ''); ?><?php echo e(number_format($metrics['response_change'], 1)); ?>%
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-danger-600"><?php echo e(number_format($metrics['error_count'] ?? 0)); ?></div>
                <div class="text-sm text-secondary-600 mt-1">Errors</div>
                <?php if(isset($metrics['error_change'])): ?>
                <div class="text-xs mt-1 <?php echo e($metrics['error_change'] <= 0 ? 'text-success-600' : 'text-danger-600'); ?>">
                    <?php echo e($metrics['error_change'] >= 0 ? '+' : ''); ?><?php echo e(number_format($metrics['error_change'], 1)); ?>%
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <!-- Requests Over Time -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Requests Over Time</h3>
            </div>
            <div class="card-body">
                <?php if (isset($component)) { $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.chart','data' => ['type' => 'line','data' => $requestsChart ?? [],'options' => [
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Requests'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ],'height' => '300','id' => 'requests-chart']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('chart'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'line','data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($requestsChart ?? []),'options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Requests'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ]),'height' => '300','id' => 'requests-chart']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $attributes = $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $component = $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Response Time Trend -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Response Time Trend</h3>
            </div>
            <div class="card-body">
                <?php if (isset($component)) { $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.chart','data' => ['type' => 'line','data' => $responseTimeChart ?? [],'options' => [
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Response Time (ms)'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ],'height' => '300','id' => 'response-time-chart']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('chart'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'line','data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($responseTimeChart ?? []),'options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Response Time (ms)'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ]),'height' => '300','id' => 'response-time-chart']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $attributes = $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $component = $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Status Codes Distribution -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Status Codes Distribution</h3>
            </div>
            <div class="card-body">
                <?php if (isset($component)) { $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.chart','data' => ['type' => 'doughnut','data' => $statusCodesChart ?? [],'options' => [
                        'plugins' => [
                            'legend' => [
                                'position' => 'bottom'
                            ]
                        ]
                    ],'height' => '300','id' => 'status-codes-chart']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('chart'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'doughnut','data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($statusCodesChart ?? []),'options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                        'plugins' => [
                            'legend' => [
                                'position' => 'bottom'
                            ]
                        ]
                    ]),'height' => '300','id' => 'status-codes-chart']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $attributes = $__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__attributesOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4)): ?>
<?php $component = $__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4; ?>
<?php unset($__componentOriginalc621b3f3a1dd664e2d7a5c269bd63ea4); ?>
<?php endif; ?>
            </div>
        </div>

        <!-- Top Endpoints -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Top Endpoints</h3>
            </div>
            <div class="card-body">
                <?php if(isset($topEndpoints) && count($topEndpoints) > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $topEndpoints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $endpoint): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-secondary-900 truncate">
                                    <?php echo e($endpoint['method']); ?> <?php echo e($endpoint['path']); ?>

                                </div>
                                <div class="text-xs text-secondary-500">
                                    <?php echo e(number_format($endpoint['requests'])); ?> requests
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <div class="w-20 bg-secondary-200 rounded-full h-2">
                                    <div class="bg-primary-600 h-2 rounded-full" 
                                         style="width: <?php echo e($endpoint['percentage']); ?>%"></div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No data available</h3>
                        <p class="text-sm text-secondary-500">Start making API calls to see analytics</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-secondary-900">Recent Activity</h3>
                <a href="<?php echo e(route('developer.analytics.logs')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                    View all logs
                </a>
            </div>
        </div>
        <div class="card-body">
            <?php if(isset($recentLogs) && $recentLogs->count() > 0): ?>
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead class="table-header">
                            <tr>
                                <th class="table-header-cell">Time</th>
                                <th class="table-header-cell">Method</th>
                                <th class="table-header-cell">Endpoint</th>
                                <th class="table-header-cell">Status</th>
                                <th class="table-header-cell">Response Time</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            <?php $__currentLoopData = $recentLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td class="table-cell">
                                    <span class="text-xs text-secondary-500">
                                        <?php echo e($log->requested_at->format('H:i:s')); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-<?php echo e($log->method === 'GET' ? 'success' : ($log->method === 'POST' ? 'primary' : 'warning')); ?> text-xs">
                                        <?php echo e($log->method); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <code class="text-xs"><?php echo e($log->path); ?></code>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-<?php echo e($log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning')); ?> text-xs">
                                        <?php echo e($log->response_status); ?>

                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="text-sm"><?php echo e($log->response_time_ms); ?>ms</span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="text-sm font-medium text-secondary-900 mb-2">No activity yet</h3>
                    <p class="text-sm text-secondary-500">API requests will appear here once you start making calls</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-refresh analytics data every 30 seconds
let refreshInterval;

function startAutoRefresh() {
    refreshInterval = setInterval(function() {
        // Only refresh if the page is visible
        if (!document.hidden) {
            refreshAnalytics();
        }
    }, 30000);
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

async function refreshAnalytics() {
    try {
        const params = new URLSearchParams(window.location.search);
        const response = await fetch(window.location.pathname + '?' + params.toString(), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Update metrics
            if (data.metrics) {
                updateMetrics(data.metrics);
            }
            
            // Update charts
            if (data.charts) {
                updateCharts(data.charts);
            }
        }
    } catch (error) {
        console.error('Failed to refresh analytics:', error);
    }
}

function updateMetrics(metrics) {
    // Update metric values
    Object.keys(metrics).forEach(key => {
        const element = document.querySelector(`[data-metric="${key}"]`);
        if (element) {
            element.textContent = metrics[key];
        }
    });
}

function updateCharts(charts) {
    // Update chart data
    if (charts.requests && window.charts['requests-chart']) {
        updateChart('requests-chart', charts.requests);
    }
    
    if (charts.responseTime && window.charts['response-time-chart']) {
        updateChart('response-time-chart', charts.responseTime);
    }
    
    if (charts.statusCodes && window.charts['status-codes-chart']) {
        updateChart('status-codes-chart', charts.statusCodes);
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
    }
});

// Stop auto-refresh when leaving page
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/analytics/index.blade.php ENDPATH**/ ?>