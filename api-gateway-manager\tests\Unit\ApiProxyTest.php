<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\ApiProxy;
use App\Models\RateLimit;
use App\Models\RequestLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApiProxyTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_proxy_has_many_rate_limits()
    {
        $apiProxy = ApiProxy::factory()->create();
        $rateLimit = RateLimit::factory()->create(['api_proxy_id' => $apiProxy->id]);

        $this->assertTrue($apiProxy->rateLimits()->exists());
        $this->assertEquals(1, $apiProxy->rateLimits()->count());
        $this->assertEquals($rateLimit->id, $apiProxy->rateLimits()->first()->id);
    }

    public function test_api_proxy_has_many_request_logs()
    {
        $apiProxy = ApiProxy::factory()->create();
        $requestLog = RequestLog::factory()->create(['api_proxy_id' => $apiProxy->id]);

        $this->assertTrue($apiProxy->requestLogs()->exists());
        $this->assertEquals(1, $apiProxy->requestLogs()->count());
        $this->assertEquals($requestLog->id, $apiProxy->requestLogs()->first()->id);
    }

    public function test_api_proxy_is_method_allowed()
    {
        $apiProxy = ApiProxy::factory()->create([
            'allowed_methods' => ['GET', 'POST']
        ]);

        $this->assertTrue($apiProxy->isMethodAllowed('GET'));
        $this->assertTrue($apiProxy->isMethodAllowed('POST'));
        $this->assertTrue($apiProxy->isMethodAllowed('get')); // Case insensitive
        $this->assertFalse($apiProxy->isMethodAllowed('DELETE'));
        $this->assertFalse($apiProxy->isMethodAllowed('PUT'));
    }

    public function test_api_proxy_get_target_url()
    {
        $apiProxy = ApiProxy::factory()->create([
            'target_url' => 'https://api.example.com'
        ]);

        $this->assertEquals('https://api.example.com/', $apiProxy->getTargetUrl());
        $this->assertEquals('https://api.example.com/users', $apiProxy->getTargetUrl('users'));
        $this->assertEquals('https://api.example.com/users', $apiProxy->getTargetUrl('/users'));
    }

    public function test_api_proxy_scope_active()
    {
        ApiProxy::factory()->create(['is_active' => true]);
        ApiProxy::factory()->create(['is_active' => false]);

        $activeProxies = ApiProxy::active()->get();

        $this->assertEquals(1, $activeProxies->count());
        $this->assertTrue($activeProxies->first()->is_active);
    }

    public function test_api_proxy_find_by_path()
    {
        $apiProxy = ApiProxy::factory()->create([
            'proxy_path' => '/test-path',
            'is_active' => true
        ]);
        
        ApiProxy::factory()->create([
            'proxy_path' => '/other-path',
            'is_active' => false
        ]);

        $found = ApiProxy::findByPath('/test-path');
        $notFound = ApiProxy::findByPath('/other-path'); // Inactive
        $missing = ApiProxy::findByPath('/missing-path');

        $this->assertNotNull($found);
        $this->assertEquals($apiProxy->id, $found->id);
        $this->assertNull($notFound);
        $this->assertNull($missing);
    }

    public function test_api_proxy_factory_creates_valid_proxy()
    {
        $apiProxy = ApiProxy::factory()->create();

        $this->assertNotNull($apiProxy->name);
        $this->assertNotNull($apiProxy->slug);
        $this->assertNotNull($apiProxy->description);
        $this->assertNotNull($apiProxy->proxy_path);
        $this->assertNotNull($apiProxy->target_url);
        $this->assertIsArray($apiProxy->allowed_methods);
        $this->assertIsArray($apiProxy->headers_to_add);
        $this->assertIsArray($apiProxy->headers_to_remove);
        $this->assertTrue($apiProxy->requires_auth);
        $this->assertTrue($apiProxy->is_active);
        $this->assertIsInt($apiProxy->timeout);
    }

    public function test_api_proxy_factory_can_create_inactive_proxy()
    {
        $apiProxy = ApiProxy::factory()->inactive()->create();

        $this->assertFalse($apiProxy->is_active);
    }

    public function test_api_proxy_factory_can_create_no_auth_proxy()
    {
        $apiProxy = ApiProxy::factory()->noAuth()->create();

        $this->assertFalse($apiProxy->requires_auth);
    }

    public function test_api_proxy_casts_attributes_correctly()
    {
        $apiProxy = ApiProxy::factory()->create([
            'allowed_methods' => ['GET', 'POST'],
            'headers_to_add' => ['X-Custom' => 'value'],
            'headers_to_remove' => ['X-Remove'],
            'requires_auth' => true,
            'is_active' => true,
            'timeout' => 30,
        ]);

        $this->assertIsArray($apiProxy->allowed_methods);
        $this->assertIsArray($apiProxy->headers_to_add);
        $this->assertIsArray($apiProxy->headers_to_remove);
        $this->assertIsBool($apiProxy->requires_auth);
        $this->assertIsBool($apiProxy->is_active);
        $this->assertIsInt($apiProxy->timeout);
    }

    public function test_api_proxy_fillable_attributes()
    {
        $data = [
            'name' => 'Test Proxy',
            'slug' => 'test-proxy',
            'description' => 'Test Description',
            'proxy_path' => '/test',
            'target_url' => 'https://api.test.com',
            'allowed_methods' => ['GET'],
            'headers_to_add' => ['X-Test' => 'value'],
            'headers_to_remove' => ['X-Remove'],
            'requires_auth' => false,
            'is_active' => false,
            'timeout' => 60,
        ];

        $apiProxy = ApiProxy::create($data);

        $this->assertEquals('Test Proxy', $apiProxy->name);
        $this->assertEquals('test-proxy', $apiProxy->slug);
        $this->assertEquals('Test Description', $apiProxy->description);
        $this->assertEquals('/test', $apiProxy->proxy_path);
        $this->assertEquals('https://api.test.com', $apiProxy->target_url);
        $this->assertEquals(['GET'], $apiProxy->allowed_methods);
        $this->assertEquals(['X-Test' => 'value'], $apiProxy->headers_to_add);
        $this->assertEquals(['X-Remove'], $apiProxy->headers_to_remove);
        $this->assertFalse($apiProxy->requires_auth);
        $this->assertFalse($apiProxy->is_active);
        $this->assertEquals(60, $apiProxy->timeout);
    }
}
