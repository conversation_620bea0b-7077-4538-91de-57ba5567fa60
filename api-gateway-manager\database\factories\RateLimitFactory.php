<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\ApiKey;
use App\Models\ApiProxy;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RateLimit>
 */
class RateLimitFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'api_key_id' => ApiKey::factory(),
            'api_proxy_id' => ApiProxy::factory(),
            'requests_per_minute' => $this->faker->numberBetween(10, 100),
            'requests_per_hour' => $this->faker->numberBetween(100, 5000),
            'requests_per_day' => $this->faker->numberBetween(1000, 50000),
            'current_minute_count' => 0,
            'current_hour_count' => 0,
            'current_day_count' => 0,
            'minute_reset_at' => Carbon::now()->addMinute(),
            'hour_reset_at' => Carbon::now()->addHour(),
            'day_reset_at' => Carbon::now()->addDay(),
        ];
    }

    /**
     * Indicate that the rate limit is at its limit.
     */
    public function atLimit(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'current_minute_count' => $attributes['requests_per_minute'],
                'current_hour_count' => $attributes['requests_per_hour'],
                'current_day_count' => $attributes['requests_per_day'],
            ];
        });
    }

    /**
     * Indicate that the rate limit has high usage.
     */
    public function highUsage(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'current_minute_count' => (int)($attributes['requests_per_minute'] * 0.8),
                'current_hour_count' => (int)($attributes['requests_per_hour'] * 0.8),
                'current_day_count' => (int)($attributes['requests_per_day'] * 0.8),
            ];
        });
    }

    /**
     * Indicate that the rate limit needs reset.
     */
    public function needsReset(): static
    {
        return $this->state(fn (array $attributes) => [
            'minute_reset_at' => Carbon::now()->subMinute(),
            'hour_reset_at' => Carbon::now()->subHour(),
            'day_reset_at' => Carbon::now()->subDay(),
        ]);
    }
}
