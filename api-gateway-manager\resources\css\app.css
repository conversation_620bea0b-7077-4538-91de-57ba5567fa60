@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        @apply w-2;
    }

    ::-webkit-scrollbar-track {
        @apply bg-secondary-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-secondary-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-secondary-400;
    }
}

@layer components {
    /* Button Components */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 border-primary-600 text-white hover:bg-primary-700 hover:border-primary-700 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply btn bg-secondary-100 border-secondary-300 text-secondary-700 hover:bg-secondary-200 hover:border-secondary-400 focus:ring-secondary-500;
    }

    .btn-success {
        @apply btn bg-success-600 border-success-600 text-white hover:bg-success-700 hover:border-success-700 focus:ring-success-500;
    }

    .btn-warning {
        @apply btn bg-warning-600 border-warning-600 text-white hover:bg-warning-700 hover:border-warning-700 focus:ring-warning-500;
    }

    .btn-danger {
        @apply btn bg-danger-600 border-danger-600 text-white hover:bg-danger-700 hover:border-danger-700 focus:ring-danger-500;
    }

    .btn-outline {
        @apply btn bg-transparent border-secondary-300 text-secondary-700 hover:bg-secondary-50 hover:border-secondary-400 focus:ring-secondary-500;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    /* Form Components */
    .form-input {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-select {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-textarea {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-checkbox {
        @apply h-4 w-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500;
    }

    .form-radio {
        @apply h-4 w-4 text-primary-600 border-secondary-300 focus:ring-primary-500;
    }

    /* Card Components */
    .card {
        @apply bg-white rounded-lg shadow-sm border border-secondary-200;
    }

    .card-header {
        @apply px-4 py-3 border-b border-secondary-200 sm:px-6;
    }

    .card-body {
        @apply px-4 py-4 sm:px-6;
    }

    .card-footer {
        @apply px-4 py-3 border-t border-secondary-200 sm:px-6;
    }

    /* Badge Components */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-secondary {
        @apply badge bg-secondary-100 text-secondary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-danger {
        @apply badge bg-danger-100 text-danger-800;
    }

    /* Alert Components */
    .alert {
        @apply p-4 rounded-lg border;
    }

    .alert-success {
        @apply alert bg-success-50 border-success-200 text-success-800;
    }

    .alert-warning {
        @apply alert bg-warning-50 border-warning-200 text-warning-800;
    }

    .alert-danger {
        @apply alert bg-danger-50 border-danger-200 text-danger-800;
    }

    .alert-info {
        @apply alert bg-primary-50 border-primary-200 text-primary-800;
    }

    /* Table Components */
    .table {
        @apply min-w-full divide-y divide-secondary-200;
    }

    .table-header {
        @apply bg-secondary-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-secondary-200;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900;
    }

    /* Mobile Navigation */
    .mobile-menu-button {
        @apply inline-flex items-center justify-center p-2 rounded-md text-secondary-400 hover:text-secondary-500 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500;
    }

    /* Loading Spinner */
    .spinner {
        @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
    }

    /* Responsive Data Table */
    .data-table-responsive {
        @apply overflow-x-auto;
    }

    .data-table-responsive table {
        @apply min-w-full;
    }

    @media (max-width: 768px) {
        .data-table-responsive {
            @apply -mx-4;
        }

        .data-table-responsive table {
            @apply text-sm;
        }

        .data-table-responsive .table-header-cell {
            @apply px-3 py-2 text-xs;
        }

        .data-table-responsive .table-cell {
            @apply px-3 py-2 text-xs;
        }
    }

    /* Mobile Card Alternative */
    .mobile-card-list {
        @apply space-y-4;
    }

    .mobile-card-item {
        @apply card;
    }

    .mobile-card-header {
        @apply flex items-start justify-between mb-3;
    }

    .mobile-card-content {
        @apply space-y-2;
    }

    .mobile-card-field {
        @apply flex justify-between items-center;
    }

    .mobile-card-label {
        @apply text-sm text-secondary-600;
    }

    .mobile-card-value {
        @apply text-sm font-medium text-secondary-900;
    }

    /* Status Indicators */
    .status-dot {
        @apply inline-block w-2 h-2 rounded-full mr-2;
    }

    .status-dot-success {
        @apply status-dot bg-success-500;
    }

    .status-dot-warning {
        @apply status-dot bg-warning-500;
    }

    .status-dot-danger {
        @apply status-dot bg-danger-500;
    }

    .status-dot-secondary {
        @apply status-dot bg-secondary-400;
    }

    /* Action Buttons */
    .action-button {
        @apply text-sm font-medium hover:underline;
    }

    .action-button-primary {
        @apply action-button text-primary-600 hover:text-primary-900;
    }

    .action-button-warning {
        @apply action-button text-warning-600 hover:text-warning-900;
    }

    .action-button-danger {
        @apply action-button text-danger-600 hover:text-danger-900;
    }

    /* Dropdown Menu */
    .dropdown-menu {
        @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-50;
    }

    .dropdown-item {
        @apply block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100;
    }

    .dropdown-item-danger {
        @apply dropdown-item text-danger-700 hover:bg-danger-50;
    }

    /* Mobile Navigation Enhancements */
    .mobile-nav-overlay {
        @apply fixed inset-0 z-40 lg:hidden;
    }

    .mobile-nav-backdrop {
        @apply fixed inset-0 bg-secondary-600 bg-opacity-75;
    }

    .mobile-nav-panel {
        @apply fixed top-0 left-0 bottom-0 flex flex-col w-5/6 max-w-sm bg-white shadow-xl;
    }

    .mobile-nav-header {
        @apply flex items-center justify-between px-4 py-4 border-b border-secondary-200;
    }

    .mobile-nav-content {
        @apply flex-1 px-4 py-4 overflow-y-auto;
    }

    .mobile-nav-footer {
        @apply px-4 py-4 border-t border-secondary-200;
    }

    /* Bottom Navigation for Mobile */
    .bottom-nav {
        @apply fixed bottom-0 left-0 right-0 bg-white border-t border-secondary-200 z-30;
    }

    .bottom-nav-container {
        @apply flex justify-around items-center h-16 px-2;
    }

    .bottom-nav-item {
        @apply flex flex-col items-center justify-center flex-1 py-2 text-xs font-medium text-secondary-600 hover:text-primary-600;
    }

    .bottom-nav-item.active {
        @apply text-primary-600;
    }

    .bottom-nav-icon {
        @apply h-6 w-6 mb-1;
    }

    .bottom-nav-label {
        @apply text-xs;
    }

    /* Floating Action Button */
    .fab {
        @apply fixed bottom-20 right-4 z-40 h-14 w-14 bg-primary-600 rounded-full shadow-lg flex items-center justify-center text-white hover:bg-primary-700 transition-all duration-200;
    }

    .fab-sm {
        @apply h-12 w-12 bottom-24 right-6;
    }

    .fab-lg {
        @apply h-16 w-16 bottom-24 right-6;
    }

    /* Pull to Refresh */
    .pull-to-refresh {
        @apply relative;
    }

    .pull-to-refresh-indicator {
        @apply absolute top-0 left-0 right-0 flex items-center justify-center py-4 bg-primary-50 text-primary-600 text-sm font-medium transform -translate-y-full transition-transform duration-200;
    }

    .pull-to-refresh-indicator.active {
        @apply translate-y-0;
    }

    /* Swipe Actions */
    .swipe-container {
        @apply relative overflow-hidden;
    }

    .swipe-content {
        @apply transition-transform duration-200;
    }

    .swipe-actions {
        @apply absolute top-0 right-0 bottom-0 flex items-center;
    }

    .swipe-action {
        @apply h-full px-4 flex items-center justify-center text-white font-medium;
    }

    .swipe-action-edit {
        @apply swipe-action bg-warning-500;
    }

    .swipe-action-delete {
        @apply swipe-action bg-danger-500;
    }

    /* Touch Feedback */
    .touch-feedback {
        @apply transition-all duration-150 active:scale-95 active:bg-secondary-100;
    }

    .touch-feedback-primary {
        @apply touch-feedback active:bg-primary-100;
    }

    .touch-feedback-danger {
        @apply touch-feedback active:bg-danger-100;
    }

    /* Mobile-First Responsive Utilities */
    .mobile-only {
        @apply block sm:hidden;
    }

    .tablet-up {
        @apply hidden sm:block;
    }

    .desktop-up {
        @apply hidden lg:block;
    }

    .mobile-tablet {
        @apply block lg:hidden;
    }

    /* Safe Area Support for iOS */
    .safe-area-top {
        padding-top: env(safe-area-inset-top);
    }

    .safe-area-bottom {
        padding-bottom: env(safe-area-inset-bottom);
    }

    .safe-area-left {
        padding-left: env(safe-area-inset-left);
    }

    .safe-area-right {
        padding-right: env(safe-area-inset-right);
    }

    /* Mobile Optimized Forms */
    .mobile-form-group {
        @apply mb-6;
    }

    .mobile-form-label {
        @apply block text-base font-medium text-secondary-700 mb-2;
    }

    .mobile-form-input {
        @apply form-input text-base;
    }

    .mobile-form-select {
        @apply form-select text-base;
    }

    .mobile-form-textarea {
        @apply form-textarea text-base;
    }

    /* Mobile Optimized Buttons */
    .mobile-btn {
        @apply btn min-h-12 text-base;
    }

    .mobile-btn-lg {
        @apply mobile-btn min-h-14 text-lg px-8;
    }

    /* Loading States */
    .loading-overlay {
        @apply fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50;
    }

    .loading-spinner {
        @apply spinner h-8 w-8;
    }

    .loading-text {
        @apply ml-3 text-secondary-600;
    }
}
