@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        @apply w-2;
    }

    ::-webkit-scrollbar-track {
        @apply bg-secondary-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-secondary-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-secondary-400;
    }
}

@layer components {
    /* Button Components */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 border-primary-600 text-white hover:bg-primary-700 hover:border-primary-700 focus:ring-primary-500;
    }

    .btn-secondary {
        @apply btn bg-secondary-100 border-secondary-300 text-secondary-700 hover:bg-secondary-200 hover:border-secondary-400 focus:ring-secondary-500;
    }

    .btn-success {
        @apply btn bg-success-600 border-success-600 text-white hover:bg-success-700 hover:border-success-700 focus:ring-success-500;
    }

    .btn-warning {
        @apply btn bg-warning-600 border-warning-600 text-white hover:bg-warning-700 hover:border-warning-700 focus:ring-warning-500;
    }

    .btn-danger {
        @apply btn bg-danger-600 border-danger-600 text-white hover:bg-danger-700 hover:border-danger-700 focus:ring-danger-500;
    }

    .btn-outline {
        @apply btn bg-transparent border-secondary-300 text-secondary-700 hover:bg-secondary-50 hover:border-secondary-400 focus:ring-secondary-500;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    /* Form Components */
    .form-input {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-select {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-textarea {
        @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-checkbox {
        @apply h-4 w-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500;
    }

    .form-radio {
        @apply h-4 w-4 text-primary-600 border-secondary-300 focus:ring-primary-500;
    }

    /* Card Components */
    .card {
        @apply bg-white rounded-lg shadow-sm border border-secondary-200;
    }

    .card-header {
        @apply px-4 py-3 border-b border-secondary-200 sm:px-6;
    }

    .card-body {
        @apply px-4 py-4 sm:px-6;
    }

    .card-footer {
        @apply px-4 py-3 border-t border-secondary-200 sm:px-6;
    }

    /* Badge Components */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-secondary {
        @apply badge bg-secondary-100 text-secondary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-danger {
        @apply badge bg-danger-100 text-danger-800;
    }

    /* Alert Components */
    .alert {
        @apply p-4 rounded-lg border;
    }

    .alert-success {
        @apply alert bg-success-50 border-success-200 text-success-800;
    }

    .alert-warning {
        @apply alert bg-warning-50 border-warning-200 text-warning-800;
    }

    .alert-danger {
        @apply alert bg-danger-50 border-danger-200 text-danger-800;
    }

    .alert-info {
        @apply alert bg-primary-50 border-primary-200 text-primary-800;
    }

    /* Table Components */
    .table {
        @apply min-w-full divide-y divide-secondary-200;
    }

    .table-header {
        @apply bg-secondary-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-secondary-200;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-secondary-900;
    }

    /* Mobile Navigation */
    .mobile-menu-button {
        @apply inline-flex items-center justify-center p-2 rounded-md text-secondary-400 hover:text-secondary-500 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500;
    }

    /* Loading Spinner */
    .spinner {
        @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
    }
}
