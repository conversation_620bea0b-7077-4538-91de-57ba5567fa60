# cPanel Deployment Guide for Laravel API Gateway Manager

This guide will help you deploy the Laravel API Gateway Manager on cPanel shared hosting.

## Prerequisites

- cPanel hosting account with PHP 8.2+ support
- MySQL database access
- SSH access (optional but recommended)
- Composer installed (or ability to upload vendor folder)

## Step 1: Prepare Your Files

1. **Upload Files**: Upload all project files to your cPanel account
   - If uploading to subdomain: `/public_html/subdomain/`
   - If uploading to main domain: `/public_html/`
   - **Important**: The Laravel `public` folder contents should be in your web root

2. **File Structure**: Your final structure should look like:
   ```
   /public_html/
   ├── index.php (from <PERSON><PERSON>'s public folder)
   ├── .htaccess (from <PERSON><PERSON>'s public folder)
   ├── css/ (from <PERSON><PERSON>'s public folder)
   ├── js/ (from <PERSON><PERSON>'s public folder)
   └── app/ (Laravel application folder - one level up from public)
       ├── app/
       ├── bootstrap/
       ├── config/
       ├── database/
       ├── resources/
       ├── routes/
       ├── storage/
       ├── vendor/
       ├── .env
       ├── artisan
       └── composer.json
   ```

## Step 2: Database Setup

1. **Create Database**: In cPanel, create a new MySQL database
2. **Create Database User**: Create a database user and assign it to the database
3. **Note Credentials**: Save the database name, username, and password

## Step 3: Environment Configuration

1. **Copy Environment File**:
   ```bash
   cp .env.cpanel .env
   ```

2. **Update Database Settings** in `.env`:
   ```
   DB_DATABASE=your_cpanel_database_name
   DB_USERNAME=your_cpanel_database_user
   DB_PASSWORD=your_cpanel_database_password
   ```

3. **Update App URL**:
   ```
   APP_URL=https://yourdomain.com
   ```

4. **Update Mail Settings** (optional):
   ```
   MAIL_HOST=mail.yourdomain.com
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your_email_password
   ```

## Step 4: Installation

### Option A: Using SSH (Recommended)

1. **SSH into your account**:
   ```bash
   ssh <EMAIL>
   ```

2. **Navigate to your app directory**:
   ```bash
   cd public_html/app  # or wherever you placed the Laravel files
   ```

3. **Run the deployment script**:
   ```bash
   chmod +x cpanel-deploy.sh
   ./cpanel-deploy.sh
   ```

### Option B: Using cPanel File Manager

1. **Set Permissions**: In File Manager, set permissions:
   - `storage/` folder: 755 (recursive)
   - `bootstrap/cache/` folder: 755 (recursive)
   - `.env` file: 644

2. **Run Commands**: Use cPanel Terminal or create a PHP script to run:
   ```php
   <?php
   // Run this once via browser, then delete this file
   exec('php artisan key:generate');
   exec('php artisan migrate --force');
   exec('php artisan db:seed --class=AdminUserSeeder --force');
   exec('php artisan config:cache');
   exec('php artisan route:cache');
   exec('php artisan view:cache');
   echo "Installation completed!";
   ?>
   ```

## Step 5: Cron Job Setup

1. **Access Cron Jobs**: In cPanel, go to "Cron Jobs"
2. **Add New Cron Job**:
   - Minute: `*`
   - Hour: `*`
   - Day: `*`
   - Month: `*`
   - Weekday: `*`
   - Command: `/usr/local/bin/php /home/<USER>/public_html/app/artisan schedule:run >> /dev/null 2>&1`

3. **Replace Path**: Update the path to match your actual installation directory

## Step 6: SSL Certificate

1. **Enable SSL**: In cPanel, go to "SSL/TLS" and enable SSL for your domain
2. **Force HTTPS**: Update your `.env` file:
   ```
   APP_URL=https://yourdomain.com
   ```

## Step 7: Testing

1. **Visit Your Site**: Go to `https://yourdomain.com`
2. **Admin Login**: 
   - Email: `<EMAIL>`
   - Password: `password`
3. **Change Password**: Immediately change the admin password

## Step 8: Configuration

### Create Your First API Proxy

1. **Login as Admin**
2. **Go to Admin Dashboard** → **API Proxies** → **Create New**
3. **Configure Proxy**:
   - Name: "Example API"
   - Proxy Path: "/api/v1/example"
   - Target URL: "https://jsonplaceholder.typicode.com"
   - Methods: ["GET", "POST"]
   - Requires Auth: Yes

### Create Developer Account

1. **Register New User** or **Admin Creates User**
2. **User Creates App**
3. **Generate API Key**
4. **Test API**: 
   ```bash
   curl -H "X-API-Key: your-api-key" https://yourdomain.com/api/v1/example/posts
   ```

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**:
   - Check `.env` file exists and has correct database credentials
   - Verify storage folder permissions (755)
   - Check error logs in cPanel

2. **Database Connection Error**:
   - Verify database credentials in `.env`
   - Ensure database user has proper permissions
   - Check if database server is accessible

3. **Composer Dependencies**:
   - If you can't run composer on cPanel, run it locally and upload the `vendor` folder
   - Ensure all dependencies are uploaded

4. **File Permissions**:
   - `storage/` and `bootstrap/cache/` must be writable (755)
   - `.env` should be readable but not writable by web server (644)

### Performance Optimization

1. **Enable OPcache**: In cPanel PHP settings, enable OPcache
2. **Increase Memory Limit**: Set `memory_limit = 256M` in PHP settings
3. **Database Optimization**: Regularly clean old request logs
4. **CDN**: Consider using a CDN for static assets

## Security Considerations

1. **Change Default Passwords**: Immediately change admin password
2. **Environment File**: Ensure `.env` is not accessible via web
3. **Regular Updates**: Keep Laravel and dependencies updated
4. **Backup**: Regularly backup your database and files
5. **Monitor Logs**: Check request logs for suspicious activity

## Maintenance

### Regular Tasks

1. **Clean Old Logs**: 
   ```bash
   php artisan app:cleanup-logs
   ```

2. **Clear Caches**:
   ```bash
   php artisan cache:clear
   php artisan config:clear
   ```

3. **Database Backup**: Use cPanel backup tools or create custom backup script

### Monitoring

- Check admin dashboard for API usage statistics
- Monitor error logs in cPanel
- Review request logs for unusual patterns
- Monitor database size and performance

## Support

For issues specific to this API Gateway Manager:
1. Check the application logs in `storage/logs/`
2. Review the admin analytics dashboard
3. Check cPanel error logs
4. Verify cron jobs are running properly

Remember to always test changes in a staging environment before applying to production!
