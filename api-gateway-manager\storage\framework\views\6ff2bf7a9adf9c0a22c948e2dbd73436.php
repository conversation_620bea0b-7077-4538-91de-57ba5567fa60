<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', 'API Gateway Manager - Secure, scalable API management platform'); ?>">

    <title><?php echo $__env->yieldContent('title', config('app.name', 'API Gateway Manager')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Additional Head Content -->
    <?php echo $__env->yieldPushContent('head'); ?>
</head>
<body class="h-full bg-secondary-50 font-sans antialiased">
    <div class="min-h-full flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="<?php echo e(route('portal.index')); ?>" class="flex items-center">
                            <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">AG</span>
                            </div>
                            <span class="ml-2 text-lg font-semibold text-secondary-900 hidden sm:block"><?php echo e(config('app.name')); ?></span>
                        </a>
                    </div>

                    <!-- Navigation -->
                    <nav class="hidden md:flex space-x-8">
                        <a href="<?php echo e(route('portal.index')); ?>" class="text-secondary-600 hover:text-secondary-900 px-3 py-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="<?php echo e(route('docs.index')); ?>" class="text-secondary-600 hover:text-secondary-900 px-3 py-2 text-sm font-medium">
                            Documentation
                        </a>
                        <a href="<?php echo e(route('portal.pricing')); ?>" class="text-secondary-600 hover:text-secondary-900 px-3 py-2 text-sm font-medium">
                            Pricing
                        </a>
                        <a href="<?php echo e(route('portal.support')); ?>" class="text-secondary-600 hover:text-secondary-900 px-3 py-2 text-sm font-medium">
                            Support
                        </a>
                    </nav>

                    <!-- Auth Links -->
                    <div class="flex items-center space-x-4">
                        <?php if(Route::currentRouteName() !== 'login'): ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-secondary-600 hover:text-secondary-900 text-sm font-medium">
                            Sign in
                        </a>
                        <?php endif; ?>

                        <?php if(Route::currentRouteName() !== 'register'): ?>
                        <a href="<?php echo e(route('register')); ?>" class="btn-primary btn-sm">
                            Get Started
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 flex">
            <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
                <div class="mx-auto w-full max-w-sm lg:w-96">
                    <?php echo e($slot); ?>

                </div>
            </div>

            <!-- Right side image/illustration (hidden on mobile) -->
            <div class="hidden lg:block relative w-0 flex-1">
                <div class="absolute inset-0 h-full w-full bg-gradient-to-br from-primary-600 to-primary-800">
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center text-white p-8">
                            <div class="mb-8">
                                <div class="h-24 w-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <h2 class="text-2xl font-bold mb-2">Powerful API Management</h2>
                                <p class="text-primary-100 text-lg">
                                    Secure, scalable, and simple API gateway solution for modern applications.
                                </p>
                            </div>

                            <div class="space-y-4 text-left">
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 text-primary-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-primary-100">API Key Management</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 text-primary-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-primary-100">Rate Limiting & Analytics</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 text-primary-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-primary-100">Developer Portal</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="h-5 w-5 text-primary-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-primary-100">Secure & Scalable</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-secondary-200">
            <div class="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
                <div class="flex flex-col sm:flex-row justify-between items-center text-sm text-secondary-500">
                    <div class="mb-2 sm:mb-0">
                        © <?php echo e(date('Y')); ?> <?php echo e(config('app.name')); ?>. All rights reserved.
                    </div>
                    <div class="flex space-x-4">
                        <a href="<?php echo e(route('docs.index')); ?>" class="hover:text-secondary-700">Documentation</a>
                        <a href="<?php echo e(route('portal.support')); ?>" class="hover:text-secondary-700">Support</a>
                        <a href="#" class="hover:text-secondary-700">Privacy</a>
                        <a href="#" class="hover:text-secondary-700">Terms</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/layouts/guest.blade.php ENDPATH**/ ?>