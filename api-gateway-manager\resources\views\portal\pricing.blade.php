@extends('layouts.guest')

@section('title', 'Pricing - ' . config('app.name'))
@section('meta_description', 'Simple, transparent pricing for API Gateway Manager. Start free and scale as you grow.')

@section('content')
<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="pricing-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#pricing-grid)" />
        </svg>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                14-day free trial • No credit card required
            </div>

            <h1 class="text-4xl font-bold sm:text-5xl lg:text-6xl mb-6">
                Simple, transparent
                <span class="text-primary-200">pricing</span>
            </h1>
            <p class="text-xl text-primary-100 max-w-3xl mx-auto mb-8">
                Start free and scale as you grow. No hidden fees, no surprises.
                Choose the plan that fits your needs and upgrade anytime.
            </p>

            <!-- Billing Toggle -->
            <div class="flex items-center justify-center space-x-4 mb-8">
                <span class="text-primary-200">Monthly</span>
                <div class="relative">
                    <input type="checkbox" id="billing-toggle" class="sr-only" onchange="toggleBilling()">
                    <label for="billing-toggle" class="flex items-center cursor-pointer">
                        <div class="relative">
                            <div class="block bg-white bg-opacity-20 w-14 h-8 rounded-full"></div>
                            <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition transform"></div>
                        </div>
                    </label>
                </div>
                <span class="text-primary-200">
                    Annual
                    <span class="inline-flex items-center px-2 py-1 bg-success-500 text-white text-xs font-medium rounded-full ml-2">
                        Save 20%
                    </span>
                </span>
            </div>

            <!-- Trust Indicators -->
            <div class="flex flex-wrap justify-center gap-6 text-primary-200 text-sm">
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Cancel anytime</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>30-day money back</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Instant setup</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Cards Section -->
<div class="bg-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-3 lg:gap-6">
            <!-- Free Plan -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-shadow duration-300">
                <div class="p-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900 mb-2">Starter</h3>
                        <p class="text-secondary-600 mb-6">Perfect for getting started and small projects</p>
                        <div class="mb-8">
                            <span class="text-5xl font-bold text-secondary-900">$0</span>
                            <span class="text-secondary-600 text-lg">/month</span>
                        </div>
                        <div class="text-sm text-secondary-500 mb-6">Forever free</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Up to 3 APIs</span>
                                <div class="text-sm text-secondary-600">Manage up to 3 different APIs</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">10,000 requests/month</span>
                                <div class="text-sm text-secondary-600">Perfect for development and testing</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Basic analytics</span>
                                <div class="text-sm text-secondary-600">Request counts and response times</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Rate limiting</span>
                                <div class="text-sm text-secondary-600">Basic protection for your APIs</div>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <span class="text-secondary-900 font-medium">Community support</span>
                                <div class="text-sm text-secondary-600">Access to community forums</div>
                            </div>
                        </li>
                    </ul>

                    <a href="{{ route('register') }}" class="w-full inline-flex items-center justify-center px-6 py-3 border border-secondary-300 text-base font-medium rounded-lg text-secondary-700 bg-white hover:bg-secondary-50 transition-colors duration-200">
                        Get Started Free
                    </a>
                </div>
            </div>

    <!-- Pricing Cards -->
    <div class="max-w-7xl mx-auto px-4 pb-16 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
            <!-- Free Plan -->
            <div class="card">
                <div class="card-body">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900">Free</h3>
                        <p class="mt-2 text-secondary-600">Perfect for getting started</p>
                        <div class="mt-6">
                            <span class="text-4xl font-bold text-secondary-900">$0</span>
                            <span class="text-secondary-600">/month</span>
                        </div>
                    </div>
                    
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Up to 3 apps</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">10,000 API calls/month</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Basic analytics</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Community support</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Rate limiting</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8">
                        <a href="{{ route('register') }}" class="btn-outline w-full">
                            Get Started
                        </a>
                    </div>
                </div>
            </div>

            <!-- Pro Plan -->
            <div class="card border-2 border-primary-500 relative">
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="badge-primary px-4 py-1">Most Popular</span>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900">Pro</h3>
                        <p class="mt-2 text-secondary-600">For growing businesses</p>
                        <div class="mt-6">
                            <span class="text-4xl font-bold text-secondary-900">$29</span>
                            <span class="text-secondary-600">/month</span>
                        </div>
                    </div>
                    
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Unlimited apps</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">1M API calls/month</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Advanced analytics</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Priority support</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Custom rate limits</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Webhooks</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8">
                        <a href="{{ route('register') }}" class="btn-primary w-full">
                            Start Free Trial
                        </a>
                    </div>
                </div>
            </div>

            <!-- Enterprise Plan -->
            <div class="card">
                <div class="card-body">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-secondary-900">Enterprise</h3>
                        <p class="mt-2 text-secondary-600">For large organizations</p>
                        <div class="mt-6">
                            <span class="text-4xl font-bold text-secondary-900">Custom</span>
                        </div>
                    </div>
                    
                    <ul class="mt-8 space-y-4">
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Unlimited everything</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Custom API calls</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">White-label solution</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">24/7 dedicated support</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">SLA guarantee</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="h-5 w-5 text-success-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-secondary-700">Custom integrations</span>
                        </li>
                    </ul>
                    
                    <div class="mt-8">
                        <a href="{{ route('portal.support') }}" class="btn-outline w-full">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="bg-secondary-50">
        <div class="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-secondary-900">
                    Frequently Asked Questions
                </h2>
            </div>
            
            <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
                <div>
                    <h3 class="font-semibold text-secondary-900 mb-2">
                        Can I change plans anytime?
                    </h3>
                    <p class="text-secondary-600 text-sm">
                        Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-secondary-900 mb-2">
                        What happens if I exceed my API call limit?
                    </h3>
                    <p class="text-secondary-600 text-sm">
                        Your API calls will be rate limited. You can upgrade your plan or wait for the next billing cycle.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-secondary-900 mb-2">
                        Is there a free trial for paid plans?
                    </h3>
                    <p class="text-secondary-600 text-sm">
                        Yes, all paid plans come with a 14-day free trial. No credit card required.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-secondary-900 mb-2">
                        Do you offer refunds?
                    </h3>
                    <p class="text-secondary-600 text-sm">
                        We offer a 30-day money-back guarantee for all paid plans.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
