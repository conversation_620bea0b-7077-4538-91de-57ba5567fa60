<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
        <exclude>
            <directory>app/Console</directory>
            <file>app/Http/Kernel.php</file>
            <file>app/Providers/RouteServiceProvider.php</file>
        </exclude>
    </source>
    <coverage>
        <report>
            <html outputDirectory="coverage-html" lowUpperBound="50" highLowerBound="90"/>
            <text outputFile="coverage.txt" showUncoveredFiles="false" showOnlySummary="true"/>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>

        <!-- API Gateway specific test settings -->
        <env name="API_RATE_LIMIT_ENABLED" value="true"/>
        <env name="REQUEST_LOGGING_ENABLED" value="true"/>
        <env name="SECURE_HEADERS_ENABLED" value="true"/>
        <env name="CORS_ENABLED" value="true"/>
    </php>
</phpunit>
