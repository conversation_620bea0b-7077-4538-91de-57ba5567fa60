<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;

class ApiKeyManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_developer_can_create_app()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', [
            'name' => 'Test App',
            'description' => 'A test application',
            'callback_url' => 'https://example.com/callback',
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('apps', [
            'name' => 'Test App',
            'user_id' => $user->id,
            'is_active' => true,
        ]);
    }

    public function test_developer_can_view_their_apps()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get('/developer/apps');

        $response->assertStatus(200);
        $response->assertSee($app->name);
    }

    public function test_developer_cannot_view_other_users_apps()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);

        $response = $this->actingAs($user1)->get("/developer/apps/{$app->id}");

        $response->assertStatus(403);
    }

    public function test_developer_can_create_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->post('/developer/api-keys', [
            'name' => 'Test API Key',
            'app_id' => $app->id,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('api_keys', [
            'name' => 'Test API Key',
            'app_id' => $app->id,
            'is_active' => true,
        ]);
    }

    public function test_api_key_generation_creates_proper_hash()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $this->actingAs($user)->post('/developer/api-keys', [
            'name' => 'Test API Key',
            'app_id' => $app->id,
        ]);

        $apiKey = ApiKey::where('app_id', $app->id)->first();

        $this->assertNotNull($apiKey->key_hash);
        $this->assertNotNull($apiKey->key_prefix);
        $this->assertEquals(8, strlen($apiKey->key_prefix));
    }

    public function test_developer_can_regenerate_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $originalHash = $apiKey->key_hash;

        $response = $this->actingAs($user)->post("/developer/api-keys/{$apiKey->id}/regenerate");

        $response->assertRedirect();
        $apiKey->refresh();
        $this->assertNotEquals($originalHash, $apiKey->key_hash);
    }

    public function test_developer_can_deactivate_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => true]);

        $response = $this->actingAs($user)->put("/developer/api-keys/{$apiKey->id}", [
            'name' => $apiKey->name,
            'is_active' => false,
        ]);

        $response->assertRedirect();
        $apiKey->refresh();
        $this->assertFalse($apiKey->is_active);
    }

    public function test_api_key_validation_methods()
    {
        $key = ApiKey::generateKey();
        $hash = ApiKey::hashKey($key);
        $prefix = ApiKey::getKeyPrefix($key);

        $this->assertStringStartsWith('agm_', $key);
        $this->assertEquals(36, strlen($key)); // agm_ + 32 chars
        $this->assertEquals(64, strlen($hash)); // SHA256 hash
        $this->assertEquals(8, strlen($prefix));
    }

    public function test_expired_api_key_detection()
    {
        $apiKey = ApiKey::factory()->create([
            'expires_at' => now()->subDay(),
        ]);

        $this->assertTrue($apiKey->isExpired());

        $apiKey->expires_at = now()->addDay();
        $apiKey->save();

        $this->assertFalse($apiKey->isExpired());
    }

    public function test_api_key_usage_tracking()
    {
        $apiKey = ApiKey::factory()->create([
            'last_used_at' => null,
        ]);

        $this->assertNull($apiKey->last_used_at);

        $apiKey->markAsUsed();
        $apiKey->refresh();

        $this->assertNotNull($apiKey->last_used_at);
        $this->assertTrue($apiKey->last_used_at->isToday());
    }
}
