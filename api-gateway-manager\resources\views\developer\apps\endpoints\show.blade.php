@extends('layouts.developer')

@php
    $pageTitle = 'Endpoint Details';
@endphp

@section('title', $endpoint->name . ' - ' . $app->name . ' - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('developer.apps.show', $app) }}" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">{{ $endpoint->name }}</h1>
                <p class="text-secondary-600">{{ $app->name }} → {{ $endpoint->proxy_path }}</p>
            </div>
        </div>
        <div class="flex space-x-4">
            <a href="{{ route('developer.apps.endpoints.edit', [$app, $endpoint]) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Endpoint
            </a>
            <a href="{{ route('developer.apps.show', $app) }}" class="btn-outline">
                Back to App
            </a>
        </div>
    </div>

    <!-- Status Banner -->
    @if(!$endpoint->is_active)
        <div class="bg-warning-50 border border-warning-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <svg class="h-5 w-5 text-warning-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-warning-800">Endpoint Inactive</h3>
                    <p class="mt-1 text-sm text-warning-700">
                        This endpoint is currently inactive and will not accept requests.
                    </p>
                </div>
            </div>
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Endpoint Details -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Endpoint Information</h2>
            </div>
            <div class="card-body">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Name</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $endpoint->name }}</dd>
                    </div>
                    @if($endpoint->description)
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Description</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $endpoint->description }}</dd>
                    </div>
                    @endif
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Proxy Path</dt>
                        <dd class="mt-1">
                            <code class="text-sm bg-secondary-100 px-2 py-1 rounded">{{ $endpoint->proxy_path }}</code>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Target URL</dt>
                        <dd class="mt-1 text-sm text-secondary-900 break-all">{{ $endpoint->target_url }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Allowed Methods</dt>
                        <dd class="mt-1">
                            <div class="flex flex-wrap gap-1">
                                @foreach($endpoint->allowed_methods as $method)
                                    <span class="badge-secondary text-xs">{{ $method }}</span>
                                @endforeach
                            </div>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Authentication Required</dt>
                        <dd class="mt-1">
                            <span class="badge-{{ $endpoint->requires_auth ? 'warning' : 'success' }}">
                                {{ $endpoint->requires_auth ? 'Yes' : 'No' }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Timeout</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $endpoint->timeout }} seconds</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Status</dt>
                        <dd class="mt-1">
                            <span class="badge-{{ $endpoint->is_active ? 'success' : 'secondary' }}">
                                {{ $endpoint->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Headers Configuration -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Headers Configuration</h2>
            </div>
            <div class="card-body">
                <!-- Headers to Add -->
                @if($endpoint->headers_to_add)
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-secondary-700 mb-3">Headers to Add</h3>
                    <div class="space-y-2">
                        @foreach($endpoint->headers_to_add as $name => $value)
                            <div class="flex items-center justify-between p-2 bg-secondary-50 rounded">
                                <code class="text-sm font-medium text-secondary-900">{{ $name }}</code>
                                <span class="text-sm text-secondary-600">{{ Str::limit($value, 30) }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Headers to Remove -->
                @if($endpoint->headers_to_remove)
                <div>
                    <h3 class="text-sm font-medium text-secondary-700 mb-3">Headers to Remove</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($endpoint->headers_to_remove as $header)
                            <span class="badge-secondary text-xs">{{ $header }}</span>
                        @endforeach
                    </div>
                </div>
                @endif

                @if(!$endpoint->headers_to_add && !$endpoint->headers_to_remove)
                <p class="text-sm text-secondary-500 italic">No custom headers configured for this endpoint.</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Usage Information -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Usage Information</h2>
            </div>
            <div class="card-body">
                <div class="bg-secondary-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-secondary-900 mb-2">How to use this endpoint:</h3>
                    <div class="space-y-2 text-sm text-secondary-700">
                        <p><strong>Endpoint URL:</strong> <code class="bg-white px-2 py-1 rounded">{{ config('app.url') }}{{ $endpoint->proxy_path }}</code></p>
                        @if($endpoint->requires_auth)
                        <p><strong>Authentication:</strong> Include your API key in the <code class="bg-white px-2 py-1 rounded">X-API-Key</code> header</p>
                        @endif
                        <p><strong>Allowed Methods:</strong> {{ implode(', ', $endpoint->allowed_methods) }}</p>
                    </div>
                </div>

                <!-- Example Request -->
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-secondary-900 mb-2">Example Request:</h3>
                    <div class="bg-secondary-900 text-secondary-100 p-4 rounded-lg overflow-x-auto">
                        <pre class="text-sm"><code>curl -X {{ $endpoint->allowed_methods[0] ?? 'GET' }} \
  "{{ config('app.url') }}{{ $endpoint->proxy_path }}" \@if($endpoint->requires_auth)
  -H "X-API-Key: your-api-key-here" \@endif
  -H "Content-Type: application/json"</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Quick Actions</h2>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="{{ route('developer.apps.endpoints.edit', [$app, $endpoint]) }}" class="flex items-center p-3 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">Edit Endpoint</div>
                            <div class="text-sm text-secondary-600">Modify settings</div>
                        </div>
                    </a>

                    <button onclick="testEndpoint()" class="flex items-center p-3 bg-success-50 rounded-lg hover:bg-success-100 transition-colors">
                        <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">Test Endpoint</div>
                            <div class="text-sm text-secondary-600">Send test request</div>
                        </div>
                    </button>

                    <a href="{{ route('developer.analytics.app', $app) }}" class="flex items-center p-3 bg-info-50 rounded-lg hover:bg-info-100 transition-colors">
                        <div class="h-8 w-8 bg-info-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-info-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">View Analytics</div>
                            <div class="text-sm text-secondary-600">Usage statistics</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testEndpoint() {
    // Simple endpoint testing functionality
    alert('Endpoint testing feature coming soon!');
}
</script>
@endsection
