<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ApiProxy>
 */
class ApiProxyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->sentence(),
            'proxy_path' => '/api/v1/' . Str::slug($name),
            'target_url' => $this->faker->url(),
            'allowed_methods' => $this->faker->randomElements(['GET', 'POST', 'PUT', 'PATCH', 'DELETE'], $this->faker->numberBetween(1, 3)),
            'headers_to_add' => null,
            'headers_to_remove' => null,
            'requires_auth' => $this->faker->boolean(80), // 80% chance of requiring auth
            'is_active' => true,
            'timeout' => $this->faker->numberBetween(10, 60),
        ];
    }

    /**
     * Indicate that the API proxy is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the API proxy doesn't require authentication.
     */
    public function public(): static
    {
        return $this->state(fn (array $attributes) => [
            'requires_auth' => false,
        ]);
    }

    /**
     * Indicate that the API proxy has custom headers.
     */
    public function withCustomHeaders(): static
    {
        return $this->state(fn (array $attributes) => [
            'headers_to_add' => [
                'X-Custom-Header' => 'custom-value',
                'X-API-Version' => 'v1',
            ],
            'headers_to_remove' => [
                'X-Powered-By',
                'Server',
            ],
        ]);
    }

    /**
     * Indicate that the API proxy only allows GET requests.
     */
    public function readOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'allowed_methods' => ['GET'],
        ]);
    }
}
