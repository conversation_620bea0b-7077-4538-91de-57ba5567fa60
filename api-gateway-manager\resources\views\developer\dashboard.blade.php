@extends('layouts.developer')

@php
    $pageTitle = 'Dashboard';
@endphp

@section('title', 'Developer Dashboard - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Welcome Section -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Welcome back, {{ auth()->user()->name }}!
        </h1>
        <p class="mt-2 text-secondary-600">
            Here's an overview of your API usage and applications.
        </p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- Total Apps -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Total Apps</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ $stats['total_apps'] ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active API Keys -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Active Keys</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ $stats['active_keys'] ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Calls Today -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Calls Today</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['calls_today'] ?? 0) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Rate -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-danger-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Success Rate</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ number_format($stats['success_rate'] ?? 99.5, 1) }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
        <!-- Recent Apps -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-secondary-900">Recent Apps</h3>
                    <a href="{{ route('developer.apps.index') }}" class="text-sm text-primary-600 hover:text-primary-500">
                        View all
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(isset($recent_apps) && $recent_apps->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_apps as $app)
                        <div class="flex items-center justify-between p-3 bg-secondary-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                    <span class="text-primary-600 font-semibold text-sm">
                                        {{ substr($app->name, 0, 2) }}
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-secondary-900">{{ $app->name }}</p>
                                    <p class="text-xs text-secondary-500">{{ $app->api_keys_count ?? 0 }} keys</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="badge-{{ $app->is_active ? 'success' : 'secondary' }}">
                                    {{ $app->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                <a href="{{ route('developer.apps.show', $app) }}" class="text-secondary-400 hover:text-secondary-600">
                                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No apps yet</h3>
                        <p class="text-sm text-secondary-500 mb-4">Get started by creating your first app</p>
                        <a href="{{ route('developer.apps.create') }}" class="btn-primary btn-sm">
                            Create App
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-secondary-900">Recent Activity</h3>
                    <a href="{{ route('developer.analytics.index') }}" class="text-sm text-primary-600 hover:text-primary-500">
                        View all
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(isset($recent_logs) && $recent_logs->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_logs as $log)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="h-2 w-2 rounded-full {{ $log->status_code >= 200 && $log->status_code < 300 ? 'bg-success-500' : ($log->status_code >= 400 ? 'bg-danger-500' : 'bg-warning-500') }}"></div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-secondary-900">{{ $log->method }} {{ $log->endpoint }}</p>
                                    <p class="text-xs text-secondary-500">{{ $log->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-medium text-secondary-900">{{ $log->status_code }}</span>
                                <p class="text-xs text-secondary-500">{{ $log->response_time }}ms</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No activity yet</h3>
                        <p class="text-sm text-secondary-500">API calls will appear here once you start using your keys</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Usage Chart -->
    <div class="card mb-8">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-secondary-900">API Usage (Last 7 Days)</h3>
        </div>
        <div class="card-body">
            <div class="h-64 flex items-center justify-center" id="usage-chart">
                <div class="text-center">
                    <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <p class="text-secondary-500">Chart will appear here once you have API usage data</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Getting Started -->
    @if((!isset($recent_apps) || $recent_apps->count() === 0))
    <div class="card">
        <div class="card-body">
            <div class="text-center">
                <div class="h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Get Started with API Gateway Manager</h3>
                <p class="text-secondary-600 mb-6 max-w-2xl mx-auto">
                    Welcome to your developer dashboard! Follow these simple steps to start managing your APIs.
                </p>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-3 max-w-4xl mx-auto">
                    <div class="text-center">
                        <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-white font-bold">1</span>
                        </div>
                        <h4 class="font-medium text-secondary-900 mb-1">Create an App</h4>
                        <p class="text-sm text-secondary-600">Set up your first application</p>
                    </div>
                    <div class="text-center">
                        <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-white font-bold">2</span>
                        </div>
                        <h4 class="font-medium text-secondary-900 mb-1">Generate API Keys</h4>
                        <p class="text-sm text-secondary-600">Create secure authentication keys</p>
                    </div>
                    <div class="text-center">
                        <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-white font-bold">3</span>
                        </div>
                        <h4 class="font-medium text-secondary-900 mb-1">Start Building</h4>
                        <p class="text-sm text-secondary-600">Make your first API calls</p>
                    </div>
                </div>
                <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('developer.apps.create') }}" class="btn-primary">
                        Create Your First App
                    </a>
                    <a href="{{ route('portal.getting-started') }}" class="btn-outline">
                        View Getting Started Guide
                    </a>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
