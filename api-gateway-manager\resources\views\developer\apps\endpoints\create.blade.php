@extends('layouts.developer')

@php
    $pageTitle = 'Add Endpoint';
@endphp

@section('title', 'Add Endpoint - ' . $app->name . ' - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('developer.apps.show', $app) }}" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                    Add Endpoint
                </h1>
                <p class="text-secondary-600">{{ $app->name }}</p>
            </div>
        </div>
        <p class="text-secondary-600">
            Create a new API endpoint for your application.
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-4xl">
        <form method="POST" action="{{ route('developer.apps.endpoints.store', $app) }}" class="space-y-6" x-data="{ 
            methods: {{ old('allowed_methods') ? json_encode(old('allowed_methods')) : '[]' }},
            headersToAdd: {{ old('headers_to_add') ? json_encode(old('headers_to_add')) : '[]' }},
            headersToRemove: {{ old('headers_to_remove') ? json_encode(old('headers_to_remove')) : '[]' }}
        }">
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Endpoint Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                            Endpoint Name <span class="text-danger-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required
                               value="{{ old('name') }}"
                               class="form-input @error('name') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                               placeholder="User Profile API">
                        @error('name')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                            Description
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="3"
                                  class="form-textarea @error('description') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                                  placeholder="Describe what this endpoint does...">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Proxy Path -->
                    <div>
                        <label for="proxy_path" class="block text-sm font-medium text-secondary-700 mb-2">
                            Proxy Path <span class="text-danger-500">*</span>
                        </label>
                        <input type="text" 
                               id="proxy_path" 
                               name="proxy_path" 
                               required
                               value="{{ old('proxy_path') }}"
                               class="form-input @error('proxy_path') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                               placeholder="/api/v1/users">
                        @error('proxy_path')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-2 text-sm text-secondary-500">
                            The path where this endpoint will be accessible (e.g., /api/v1/users)
                        </p>
                    </div>

                    <!-- Target URL -->
                    <div>
                        <label for="target_url" class="block text-sm font-medium text-secondary-700 mb-2">
                            Target URL <span class="text-danger-500">*</span>
                        </label>
                        <input type="url" 
                               id="target_url" 
                               name="target_url" 
                               required
                               value="{{ old('target_url') }}"
                               class="form-input @error('target_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                               placeholder="{{ $app->base_url ? '/users' : 'https://api.example.com/users' }}">
                        @error('target_url')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-2 text-sm text-secondary-500">
                            @if($app->base_url)
                                Relative URL (will be combined with base URL: {{ $app->base_url }})
                            @else
                                Full URL where requests will be forwarded
                            @endif
                        </p>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Allowed Methods -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Allowed Methods <span class="text-danger-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            @foreach(['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'] as $method)
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="allowed_methods[]" 
                                           value="{{ $method }}"
                                           x-model="methods"
                                           class="form-checkbox">
                                    <span class="ml-2 text-sm text-secondary-700">{{ $method }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('allowed_methods')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Authentication Required -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="requires_auth" 
                                   name="requires_auth" 
                                   value="1"
                                   {{ old('requires_auth') ? 'checked' : '' }}
                                   class="form-checkbox">
                            <label for="requires_auth" class="ml-2 block text-sm text-secondary-700">
                                Require API Key Authentication
                            </label>
                        </div>
                        <p class="mt-2 text-sm text-secondary-500">
                            If enabled, requests must include a valid API key
                        </p>
                    </div>

                    <!-- Timeout -->
                    <div>
                        <label for="timeout" class="block text-sm font-medium text-secondary-700 mb-2">
                            Timeout (seconds)
                        </label>
                        <input type="number" 
                               id="timeout" 
                               name="timeout" 
                               min="1" 
                               max="300"
                               value="{{ old('timeout', 30) }}"
                               class="form-input @error('timeout') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">
                        @error('timeout')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Headers Configuration -->
            <div class="border-t border-secondary-200 pt-6">
                <h3 class="text-lg font-medium text-secondary-900 mb-4">Header Configuration</h3>
                
                <!-- App Headers Info -->
                @if($app->custom_headers || $app->authorization_token)
                <div class="bg-info-50 border border-info-200 rounded-lg p-4 mb-6">
                    <h4 class="text-sm font-medium text-info-800 mb-2">App-level Headers (automatically included)</h4>
                    <div class="space-y-1">
                        @if($app->authorization_token)
                            <div class="text-sm text-info-700">
                                <code class="bg-info-100 px-2 py-1 rounded">Authorization</code>: {{ Str::mask($app->authorization_token, '*', 10) }}
                            </div>
                        @endif
                        @if($app->custom_headers)
                            @foreach($app->custom_headers as $name => $value)
                                <div class="text-sm text-info-700">
                                    <code class="bg-info-100 px-2 py-1 rounded">{{ $name }}</code>: {{ Str::limit($value, 30) }}
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
                @endif

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Headers to Add -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Additional Headers to Add
                        </label>
                        <div class="space-y-3">
                            <template x-for="(header, index) in headersToAdd" :key="index">
                                <div class="flex gap-3 items-start">
                                    <div class="flex-1">
                                        <input type="text" 
                                               :name="`headers_to_add[${index}][name]`"
                                               x-model="header.name"
                                               class="form-input"
                                               placeholder="Header name">
                                    </div>
                                    <div class="flex-1">
                                        <input type="text" 
                                               :name="`headers_to_add[${index}][value]`"
                                               x-model="header.value"
                                               class="form-input"
                                               placeholder="Header value">
                                    </div>
                                    <button type="button" 
                                            @click="headersToAdd.splice(index, 1)"
                                            class="btn-sm btn-outline text-danger-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                            <button type="button" 
                                    @click="headersToAdd.push({ name: '', value: '' })"
                                    class="btn-sm btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Header
                            </button>
                        </div>
                    </div>

                    <!-- Headers to Remove -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Headers to Remove
                        </label>
                        <div class="space-y-3">
                            <template x-for="(header, index) in headersToRemove" :key="index">
                                <div class="flex gap-3 items-center">
                                    <input type="text" 
                                           :name="`headers_to_remove[${index}]`"
                                           x-model="headersToRemove[index]"
                                           class="form-input flex-1"
                                           placeholder="Header name to remove">
                                    <button type="button" 
                                            @click="headersToRemove.splice(index, 1)"
                                            class="btn-sm btn-outline text-danger-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                            <button type="button" 
                                    @click="headersToRemove.push('')"
                                    class="btn-sm btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Header to Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-secondary-200">
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Endpoint
                </button>
                <a href="{{ route('developer.apps.show', $app) }}" class="btn-outline">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
@endsection
