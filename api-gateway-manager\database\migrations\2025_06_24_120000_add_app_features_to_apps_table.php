<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('apps', function (Blueprint $table) {
            $table->json('custom_headers')->nullable()->after('callback_url');
            $table->text('authorization_token')->nullable()->after('custom_headers');
            $table->string('base_url')->nullable()->after('authorization_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('apps', function (Blueprint $table) {
            $table->dropColumn(['custom_headers', 'authorization_token', 'base_url']);
        });
    }
};
