@extends('layouts.guest')

@section('title', 'Sign In - ' . config('app.name'))
@section('meta_description', 'Sign in to your API Gateway Manager account to manage your APIs, keys, and analytics.')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Welcome back
        </h2>
        <p class="mt-2 text-sm text-secondary-600">
            Sign in to your account to continue
        </p>
    </div>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert-info">
            {{ session('status') }}
        </div>
    @endif

    <!-- Login Form -->
    <form method="POST" action="{{ route('login') }}" class="space-y-6">
        @csrf

        <!-- Email Address -->
        <div>
            <label for="email" class="block text-sm font-medium text-secondary-700 mb-2">
                Email address
            </label>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   autofocus
                   value="{{ old('email') }}"
                   class="form-input @error('email') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                   placeholder="Enter your email">
            @error('email')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Password -->
        <div>
            <label for="password" class="block text-sm font-medium text-secondary-700 mb-2">
                Password
            </label>
            <div class="relative">
                <input id="password"
                       name="password"
                       type="password"
                       autocomplete="current-password"
                       required
                       class="form-input pr-10 @error('password') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Enter your password">
                <button type="button"
                        onclick="togglePassword()"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg id="eye-icon" class="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                </button>
            </div>
            @error('password')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input id="remember_me"
                       name="remember"
                       type="checkbox"
                       class="form-checkbox">
                <label for="remember_me" class="ml-2 block text-sm text-secondary-700">
                    Remember me
                </label>
            </div>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}"
                   class="text-sm text-primary-600 hover:text-primary-500 font-medium">
                    Forgot password?
                </a>
            @endif
        </div>

        <!-- Submit Button -->
        <div>
            <button type="submit" class="btn-primary w-full">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                </svg>
                Sign in
            </button>
        </div>

        <!-- Sign up link -->
        <div class="text-center">
            <p class="text-sm text-secondary-600">
                Don't have an account?
                <a href="{{ route('register') }}" class="font-medium text-primary-600 hover:text-primary-500">
                    Sign up for free
                </a>
            </p>
        </div>
    </form>
</div>

@push('scripts')
<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const eyeIcon = document.getElementById('eye-icon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
        `;
    } else {
        passwordInput.type = 'password';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        `;
    }
}
</script>
@endpush
@endsection
