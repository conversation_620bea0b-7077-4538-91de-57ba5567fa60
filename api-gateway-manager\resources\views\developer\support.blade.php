@extends('layouts.developer')

@php
    $pageTitle = 'Support';
@endphp

@section('title', 'Support - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Developer Support</h1>
        <p class="mt-2 text-secondary-600">Get help with your API integration and account</p>
    </div>

    <!-- Support Options -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Contact Support -->
        <div class="card">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Email Support</h3>
                <p class="text-secondary-600 mb-4">Get help via email with detailed responses</p>
                <a href="mailto:<EMAIL>" class="btn-primary">
                    Send Email
                </a>
            </div>
        </div>

        <!-- Documentation -->
        <div class="card">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Documentation</h3>
                <p class="text-secondary-600 mb-4">Browse our comprehensive guides and tutorials</p>
                <a href="{{ route('developer.documentation') }}" class="btn-outline">
                    View Docs
                </a>
            </div>
        </div>

        <!-- Community -->
        <div class="card">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Community Forum</h3>
                <p class="text-secondary-600 mb-4">Connect with other developers and share knowledge</p>
                <a href="#" class="btn-outline">
                    Join Forum
                </a>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Account Details -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Your Account</h2>
            </div>
            <div class="card-body">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Name</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ auth()->user()->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Email</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ auth()->user()->email }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Account Type</dt>
                        <dd class="mt-1">
                            <span class="badge-primary">{{ ucfirst(auth()->user()->role) }}</span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Member Since</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ auth()->user()->created_at->format('F j, Y') }}</dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Quick Stats</h2>
            </div>
            <div class="card-body">
                @php
                    $user = auth()->user();
                    $totalApps = $user->apps()->count();
                    $totalApiKeys = $user->apps()->withCount('apiKeys')->get()->sum('api_keys_count');
                    $apiKeyIds = $user->apps()->with('apiKeys')->get()->pluck('apiKeys')->flatten()->pluck('id');
                    $totalRequests = \App\Models\RequestLog::whereIn('api_key_id', $apiKeyIds)->count();
                @endphp
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-600">{{ $totalApps }}</div>
                        <div class="text-sm text-secondary-600">Total Apps</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-success-600">{{ $totalApiKeys }}</div>
                        <div class="text-sm text-secondary-600">API Keys</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-warning-600">{{ number_format($totalRequests) }}</div>
                        <div class="text-sm text-secondary-600">Total Requests</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-danger-600">
                            {{ $totalRequests > 0 ? number_format((\App\Models\RequestLog::whereIn('api_key_id', $apiKeyIds)->whereBetween('response_status', [200, 299])->count() / $totalRequests) * 100, 1) : '100.0' }}%
                        </div>
                        <div class="text-sm text-secondary-600">Success Rate</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="card">
        <div class="card-header">
            <h2 class="text-lg font-semibold text-secondary-900">Frequently Asked Questions</h2>
        </div>
        <div class="card-body">
            <div class="space-y-6">
                <!-- FAQ Item 1 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">How do I create an API key?</h3>
                    <p class="text-sm text-secondary-600 mb-2">
                        To create an API key, first create an application in your dashboard, then navigate to the API Keys section and click "Create API Key". 
                        Choose your application and provide a descriptive name for the key.
                    </p>
                    <a href="{{ route('developer.api-keys.create') }}" class="text-sm text-primary-600 hover:text-primary-500">Create API Key →</a>
                </div>

                <!-- FAQ Item 2 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">What are the rate limits?</h3>
                    <p class="text-sm text-secondary-600">
                        Rate limits depend on your plan. Free accounts have 1,000 requests per hour, while Professional accounts have 10,000 requests per hour. 
                        Enterprise plans have custom limits based on your needs.
                    </p>
                </div>

                <!-- FAQ Item 3 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">How do I authenticate API requests?</h3>
                    <p class="text-sm text-secondary-600 mb-2">
                        Include your API key in the request headers using <code class="bg-secondary-100 px-1 rounded">X-API-Key</code> or as a query parameter 
                        <code class="bg-secondary-100 px-1 rounded">api_key</code>. Always use HTTPS for secure transmission.
                    </p>
                    <a href="{{ route('developer.documentation') }}#authentication" class="text-sm text-primary-600 hover:text-primary-500">View Authentication Guide →</a>
                </div>

                <!-- FAQ Item 4 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">Can I monitor my API usage?</h3>
                    <p class="text-sm text-secondary-600 mb-2">
                        Yes! Use the Analytics dashboard to monitor your API usage, response times, error rates, and more. 
                        You can view detailed logs and export data for further analysis.
                    </p>
                    <a href="{{ route('developer.analytics.index') }}" class="text-sm text-primary-600 hover:text-primary-500">View Analytics →</a>
                </div>

                <!-- FAQ Item 5 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">What should I do if I'm getting 401 errors?</h3>
                    <p class="text-sm text-secondary-600">
                        401 errors typically indicate authentication issues. Check that your API key is correct, active, and not expired. 
                        Ensure you're including the key in the correct header or query parameter format.
                    </p>
                </div>

                <!-- FAQ Item 6 -->
                <div>
                    <h3 class="font-medium text-secondary-900 mb-2">How do I upgrade my plan?</h3>
                    <p class="text-sm text-secondary-600 mb-2">
                        You can upgrade your plan at any time from the billing section of your account. 
                        Upgrades take effect immediately and you'll only be charged for the prorated difference.
                    </p>
                    <a href="{{ route('portal.pricing') }}" class="text-sm text-primary-600 hover:text-primary-500">View Pricing →</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Form -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Still Need Help?</h2>
            </div>
            <div class="card-body">
                <p class="text-secondary-600 mb-6">
                    Can't find the answer you're looking for? Send us a message and we'll get back to you as soon as possible.
                </p>
                
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="form-group">
                            <label for="subject" class="form-label">Subject</label>
                            <select id="subject" class="form-input">
                                <option>API Integration Help</option>
                                <option>Account Issues</option>
                                <option>Billing Questions</option>
                                <option>Feature Request</option>
                                <option>Bug Report</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="priority" class="form-label">Priority</label>
                            <select id="priority" class="form-input">
                                <option>Low</option>
                                <option>Medium</option>
                                <option>High</option>
                                <option>Urgent</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">Message</label>
                        <textarea id="message" rows="4" class="form-input" placeholder="Describe your issue or question in detail..."></textarea>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                            </svg>
                            Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
