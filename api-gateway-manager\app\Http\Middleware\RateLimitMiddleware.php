<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\RateLimit;
use App\Models\ApiKey;
use App\Models\ApiProxy;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $proxyId = null): Response
    {
        // Get API key from request
        $apiKey = $request->attributes->get('api_key');

        if (!$apiKey) {
            // If no API key, skip rate limiting
            return $next($request);
        }

        // Get API proxy
        $apiProxy = null;
        if ($proxyId) {
            $apiProxy = ApiProxy::find($proxyId);
        } else {
            // Try to find proxy by path
            $path = '/' . ltrim($request->path(), '/');
            $apiProxy = ApiProxy::findByPath($path);
        }

        if (!$apiProxy) {
            return $next($request);
        }

        // Check rate limits
        $rateLimit = RateLimit::getOrCreate($apiKey->id, $apiProxy->id);

        if ($rateLimit->isExceeded()) {
            return response()->json([
                'error' => 'Rate limit exceeded',
                'message' => 'You have exceeded the rate limit for this API',
                'limits' => [
                    'requests_per_minute' => $rateLimit->requests_per_minute,
                    'requests_per_hour' => $rateLimit->requests_per_hour,
                    'requests_per_day' => $rateLimit->requests_per_day,
                ],
                'current' => [
                    'minute_count' => $rateLimit->current_minute_count,
                    'hour_count' => $rateLimit->current_hour_count,
                    'day_count' => $rateLimit->current_day_count,
                ],
                'reset_times' => [
                    'minute_reset_at' => $rateLimit->minute_reset_at?->toISOString(),
                    'hour_reset_at' => $rateLimit->hour_reset_at?->toISOString(),
                    'day_reset_at' => $rateLimit->day_reset_at?->toISOString(),
                ],
            ], 429, [
                'X-RateLimit-Limit-Minute' => $rateLimit->requests_per_minute,
                'X-RateLimit-Limit-Hour' => $rateLimit->requests_per_hour,
                'X-RateLimit-Limit-Day' => $rateLimit->requests_per_day,
                'X-RateLimit-Remaining-Minute' => max(0, $rateLimit->requests_per_minute - $rateLimit->current_minute_count),
                'X-RateLimit-Remaining-Hour' => max(0, $rateLimit->requests_per_hour - $rateLimit->current_hour_count),
                'X-RateLimit-Remaining-Day' => max(0, $rateLimit->requests_per_day - $rateLimit->current_day_count),
                'X-RateLimit-Reset-Minute' => $rateLimit->minute_reset_at?->timestamp,
                'X-RateLimit-Reset-Hour' => $rateLimit->hour_reset_at?->timestamp,
                'X-RateLimit-Reset-Day' => $rateLimit->day_reset_at?->timestamp,
            ]);
        }

        // Increment counters
        $rateLimit->incrementCounters();

        // Add rate limit headers to response
        $response = $next($request);

        if ($response instanceof \Illuminate\Http\JsonResponse) {
            $response->headers->add([
                'X-RateLimit-Limit-Minute' => $rateLimit->requests_per_minute,
                'X-RateLimit-Limit-Hour' => $rateLimit->requests_per_hour,
                'X-RateLimit-Limit-Day' => $rateLimit->requests_per_day,
                'X-RateLimit-Remaining-Minute' => max(0, $rateLimit->requests_per_minute - $rateLimit->current_minute_count),
                'X-RateLimit-Remaining-Hour' => max(0, $rateLimit->requests_per_hour - $rateLimit->current_hour_count),
                'X-RateLimit-Remaining-Day' => max(0, $rateLimit->requests_per_day - $rateLimit->current_day_count),
            ]);
        }

        return $response;
    }
}
