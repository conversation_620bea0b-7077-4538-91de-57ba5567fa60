<?php
    $pageTitle = 'App Analytics';
?>

<?php $__env->startSection('title', $app->name . ' Analytics - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl"><?php echo e($app->name); ?> Analytics</h1>
                <p class="mt-2 text-secondary-600">Detailed analytics and usage statistics for this application</p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('developer.apps.show', $app)); ?>" class="btn-outline">
                    View App Details
                </a>
                <a href="<?php echo e(route('developer.analytics.index')); ?>" class="btn-outline">
                    Back to Analytics
                </a>
            </div>
        </div>
    </div>

    <!-- Time Period Filter -->
    <div class="mb-8">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="flex items-center space-x-4">
                    <label for="days" class="text-sm font-medium text-secondary-700">Time Period:</label>
                    <select name="days" id="days" class="form-input w-auto" onchange="this.form.submit()">
                        <option value="1" <?php echo e(request('days', 7) == 1 ? 'selected' : ''); ?>>Last 24 hours</option>
                        <option value="7" <?php echo e(request('days', 7) == 7 ? 'selected' : ''); ?>>Last 7 days</option>
                        <option value="30" <?php echo e(request('days', 7) == 30 ? 'selected' : ''); ?>>Last 30 days</option>
                        <option value="90" <?php echo e(request('days', 7) == 90 ? 'selected' : ''); ?>>Last 90 days</option>
                    </select>
                </form>
            </div>
        </div>
    </div>

    <?php if($hasData): ?>
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- Total Requests -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-secondary-600">Total Requests</p>
                            <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['total_requests'] ?? 0)); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Successful Requests -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-secondary-600">Successful</p>
                            <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['successful_requests'] ?? 0)); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Requests -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-danger-100 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-secondary-600">Errors</p>
                            <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['error_requests'] ?? 0)); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Average Response Time -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-secondary-600">Avg Response</p>
                            <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['avg_response_time'] ?? 0)); ?>ms</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unique IPs -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-info-100 rounded-lg flex items-center justify-center">
                                <svg class="h-5 w-5 text-info-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4 flex-1">
                            <p class="text-sm font-medium text-secondary-600">Unique IPs</p>
                            <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['unique_ips'] ?? 0)); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Data -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Usage by API Key -->
            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">Usage by API Key</h2>
                </div>
                <div class="card-body">
                    <?php if($usageByApiKey->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $usageByApiKey; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $usage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-medium text-secondary-900"><?php echo e($usage->apiKey->name); ?></div>
                                            <div class="text-sm text-secondary-500"><?php echo e($usage->apiKey->key_prefix); ?>...</div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-secondary-900"><?php echo e(number_format($usage->request_count)); ?></div>
                                        <div class="text-sm text-secondary-500"><?php echo e(number_format($usage->avg_response_time)); ?>ms avg</div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <p class="text-secondary-500">No API key usage data available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- App Information -->
            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">App Information</h2>
                </div>
                <div class="card-body">
                    <dl class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-secondary-500">Name</dt>
                            <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->name); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-secondary-500">Description</dt>
                            <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->description ?: 'No description provided'); ?></dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-secondary-500">Status</dt>
                            <dd class="mt-1">
                                <span class="badge-<?php echo e($app->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($app->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-secondary-500">API Keys</dt>
                            <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->apiKeys->count()); ?> total</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-secondary-500">Created</dt>
                            <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->created_at->format('F j, Y')); ?></dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-secondary-900">Recent Activity</h2>
                    <a href="<?php echo e(route('developer.analytics.logs')); ?>?app_id=<?php echo e($app->id); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                        View All Logs
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($recentLogs->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Method</th>
                                    <th>Path</th>
                                    <th>API Key</th>
                                    <th>Status</th>
                                    <th>Response Time</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="table-cell">
                                            <div class="text-sm text-secondary-900">
                                                <?php echo e($log->requested_at->format('M j, H:i:s')); ?>

                                            </div>
                                            <div class="text-xs text-secondary-500">
                                                <?php echo e($log->requested_at->diffForHumans()); ?>

                                            </div>
                                        </td>
                                        <td class="table-cell">
                                            <span class="badge-<?php echo e($log->method === 'GET' ? 'success' : ($log->method === 'POST' ? 'primary' : ($log->method === 'DELETE' ? 'danger' : 'warning'))); ?> text-xs">
                                                <?php echo e($log->method); ?>

                                            </span>
                                        </td>
                                        <td class="table-cell">
                                            <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($log->path); ?></code>
                                        </td>
                                        <td class="table-cell">
                                            <?php if($log->apiKey): ?>
                                                <div class="text-sm text-secondary-900"><?php echo e($log->apiKey->name); ?></div>
                                                <code class="text-xs text-secondary-500"><?php echo e($log->apiKey->key_prefix); ?>...</code>
                                            <?php else: ?>
                                                <span class="text-sm text-secondary-500">Unknown</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="table-cell">
                                            <span class="badge-<?php echo e($log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning')); ?> text-xs">
                                                <?php echo e($log->response_status); ?>

                                            </span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm <?php echo e($log->response_time_ms > 1000 ? 'text-danger-600' : ($log->response_time_ms > 500 ? 'text-warning-600' : 'text-success-600')); ?>">
                                                <?php echo e($log->response_time_ms); ?>ms
                                            </span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm text-secondary-600 font-mono"><?php echo e($log->ip_address); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-secondary-900">No recent activity</h3>
                        <p class="mt-1 text-sm text-secondary-500">API requests will appear here once you start using this app</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <!-- No Data State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-secondary-900">No analytics data</h3>
            <p class="mt-1 text-sm text-secondary-500">
                Start making API requests to see analytics for <?php echo e($app->name); ?>.
            </p>
            <div class="mt-6">
                <a href="<?php echo e(route('developer.api-keys.create')); ?>?app_id=<?php echo e($app->id); ?>" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                    </svg>
                    Create API Key
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/analytics/app.blade.php ENDPATH**/ ?>