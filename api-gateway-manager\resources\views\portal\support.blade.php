@extends('layouts.app')

@section('title', 'Support - ' . config('app.name'))
@section('meta_description', 'Get help with API Gateway Manager. Find answers, contact support, and access resources.')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-secondary-900 sm:text-4xl">
            How can we help you?
        </h1>
        <p class="mt-4 text-lg text-secondary-600">
            Find answers, get support, and access helpful resources
        </p>
    </div>

    <!-- Quick Help Cards -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mb-16">
        <!-- Documentation -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Documentation</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Comprehensive guides and API reference
                </p>
                <a href="{{ route('docs.index') }}" class="btn-outline btn-sm">
                    Browse Docs
                </a>
            </div>
        </div>

        <!-- Getting Started -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Getting Started</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Step-by-step guide to get up and running
                </p>
                <a href="{{ route('portal.getting-started') }}" class="btn-outline btn-sm">
                    Get Started
                </a>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Contact Support</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Get help from our support team
                </p>
                <a href="#contact-form" class="btn-primary btn-sm">
                    Contact Us
                </a>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mb-16">
        <h2 class="text-2xl font-bold text-secondary-900 mb-8 text-center">
            Frequently Asked Questions
        </h2>
        
        <div class="space-y-6" x-data="{ openFaq: null }">
            <!-- FAQ Item 1 -->
            <div class="card">
                <div class="card-body">
                    <button @click="openFaq = openFaq === 1 ? null : 1" 
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            How do I get started with API Gateway Manager?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 1 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 1" x-transition class="mt-4">
                        <p class="text-secondary-600">
                            Getting started is easy! Simply create an account, set up your first app, generate API keys, and start making API calls. 
                            Check out our <a href="{{ route('portal.getting-started') }}" class="text-primary-600 hover:text-primary-500">getting started guide</a> for detailed instructions.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 2 -->
            <div class="card">
                <div class="card-body">
                    <button @click="openFaq = openFaq === 2 ? null : 2" 
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            What are the rate limits for API calls?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 2 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 2" x-transition class="mt-4">
                        <p class="text-secondary-600">
                            Rate limits depend on your plan and can be customized per API key. Free accounts have basic rate limits, 
                            while paid plans offer higher limits and custom configurations. Check your dashboard for specific limits.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 3 -->
            <div class="card">
                <div class="card-body">
                    <button @click="openFaq = openFaq === 3 ? null : 3" 
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            How do I authenticate API requests?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 3 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 3" x-transition class="mt-4">
                        <p class="text-secondary-600">
                            Include your API key in the request header as <code class="bg-secondary-100 px-2 py-1 rounded text-sm">X-API-Key: your_api_key</code>. 
                            All API keys start with the prefix "agm_" for easy identification.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 4 -->
            <div class="card">
                <div class="card-body">
                    <button @click="openFaq = openFaq === 4 ? null : 4" 
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            Can I monitor my API usage?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 4 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 4" x-transition class="mt-4">
                        <p class="text-secondary-600">
                            Yes! Your developer dashboard provides detailed analytics including request counts, response times, error rates, 
                            and usage trends. You can filter by date range, API key, or endpoint.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 5 -->
            <div class="card">
                <div class="card-body">
                    <button @click="openFaq = openFaq === 5 ? null : 5" 
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            What happens if my API key is compromised?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform" 
                             :class="{ 'rotate-180': openFaq === 5 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 5" x-transition class="mt-4">
                        <p class="text-secondary-600">
                            You can immediately deactivate or regenerate your API key from the developer dashboard. 
                            We also provide usage monitoring to help you detect unusual activity.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Form -->
    <div id="contact-form" class="card max-w-2xl mx-auto">
        <div class="card-header">
            <h2 class="text-xl font-bold text-secondary-900">
                Contact Support
            </h2>
            <p class="text-secondary-600 text-sm mt-1">
                Can't find what you're looking for? Send us a message and we'll get back to you.
            </p>
        </div>
        <div class="card-body">
            <form class="space-y-6" action="#" method="POST">
                @csrf
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                            Name
                        </label>
                        <input type="text" id="name" name="name" required class="form-input">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-secondary-700 mb-2">
                            Email
                        </label>
                        <input type="email" id="email" name="email" required class="form-input">
                    </div>
                </div>

                <div>
                    <label for="subject" class="block text-sm font-medium text-secondary-700 mb-2">
                        Subject
                    </label>
                    <select id="subject" name="subject" class="form-select">
                        <option value="">Select a topic</option>
                        <option value="technical">Technical Support</option>
                        <option value="billing">Billing Question</option>
                        <option value="feature">Feature Request</option>
                        <option value="bug">Bug Report</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label for="message" class="block text-sm font-medium text-secondary-700 mb-2">
                        Message
                    </label>
                    <textarea id="message" name="message" rows="6" required 
                              class="form-textarea"
                              placeholder="Describe your issue or question in detail..."></textarea>
                </div>

                <div>
                    <button type="submit" class="btn-primary w-full">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        Send Message
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Additional Resources -->
    <div class="mt-16 text-center">
        <h2 class="text-2xl font-bold text-secondary-900 mb-8">
            Additional Resources
        </h2>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <a href="{{ route('docs.index') }}" class="btn-outline">
                📚 API Documentation
            </a>
            <a href="{{ route('portal.getting-started') }}" class="btn-outline">
                🚀 Getting Started Guide
            </a>
            <a href="#" class="btn-outline">
                💬 Community Forum
            </a>
        </div>
    </div>
</div>
@endsection
