@extends('layouts.guest')

@section('title', 'Support - ' . config('app.name'))
@section('meta_description', 'Get help with API Gateway Manager. Find answers, contact support, and access resources.')

@section('content')
<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="support-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#support-grid)" />
        </svg>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                </svg>
                24/7 Support Available
            </div>

            <h1 class="text-4xl font-bold sm:text-5xl lg:text-6xl mb-6">
                How can we
                <span class="text-primary-200">help you?</span>
            </h1>
            <p class="text-xl text-primary-100 max-w-3xl mx-auto mb-8">
                Find answers to common questions, browse our documentation, or get in touch with our support team.
                We're here to help you succeed with your API management.
            </p>

            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto mb-8">
                <div class="relative">
                    <input type="text" placeholder="Search for help articles, guides, or FAQs..."
                           class="w-full px-6 py-4 text-lg text-secondary-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-primary-300 pr-12">
                    <button class="absolute right-4 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex flex-wrap justify-center gap-8 text-primary-200 text-sm">
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Average response time: 2 hours</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>98% customer satisfaction</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>500+ help articles</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Support Options Section -->
<div class="bg-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Choose how you'd like to get help
            </h2>
            <p class="text-lg text-secondary-600">
                Multiple ways to find the answers you need
            </p>
        </div>

        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Documentation -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="p-8 text-center">
                    <div class="h-16 w-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-200 transition-colors">
                        <svg class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Documentation</h3>
                    <p class="text-secondary-600 mb-6">
                        Comprehensive guides, API reference, and tutorials to help you get the most out of our platform.
                    </p>
                    <a href="{{ route('docs.index') }}" class="inline-flex items-center justify-center w-full px-4 py-2 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors">
                        Browse Docs
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                    </a>
                </div>
                <div class="absolute top-4 right-4 bg-primary-100 text-primary-600 px-2 py-1 rounded-full text-xs font-medium">
                    Self-service
                </div>
            </div>

            <!-- Live Chat -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="p-8 text-center">
                    <div class="h-16 w-16 bg-success-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-success-200 transition-colors">
                        <svg class="h-8 w-8 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Live Chat</h3>
                    <p class="text-secondary-600 mb-6">
                        Get instant help from our support team. Available 24/7 for urgent issues and quick questions.
                    </p>
                    <button onclick="openChat()" class="inline-flex items-center justify-center w-full px-4 py-2 bg-success-600 text-white font-medium rounded-lg hover:bg-success-700 transition-colors">
                        Start Chat
                        <div class="w-2 h-2 bg-green-400 rounded-full ml-2 animate-pulse"></div>
                    </button>
                </div>
                <div class="absolute top-4 right-4 bg-success-100 text-success-600 px-2 py-1 rounded-full text-xs font-medium">
                    Instant
                </div>
            </div>

            <!-- Email Support -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="p-8 text-center">
                    <div class="h-16 w-16 bg-warning-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-warning-200 transition-colors">
                        <svg class="h-8 w-8 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Email Support</h3>
                    <p class="text-secondary-600 mb-6">
                        Send us detailed questions or issues. Perfect for complex problems that need thorough investigation.
                    </p>
                    <a href="#contact-form" class="inline-flex items-center justify-center w-full px-4 py-2 bg-warning-600 text-white font-medium rounded-lg hover:bg-warning-700 transition-colors">
                        Send Email
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                    </a>
                </div>
                <div class="absolute top-4 right-4 bg-warning-100 text-warning-600 px-2 py-1 rounded-full text-xs font-medium">
                    Detailed
                </div>
            </div>

            <!-- Phone Support -->
            <div class="relative bg-white rounded-2xl shadow-lg border border-secondary-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div class="p-8 text-center">
                    <div class="h-16 w-16 bg-danger-100 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-danger-200 transition-colors">
                        <svg class="h-8 w-8 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Phone Support</h3>
                    <p class="text-secondary-600 mb-6">
                        Talk directly with our experts. Available for Pro and Enterprise customers during business hours.
                    </p>
                    <a href="tel:******-0123" class="inline-flex items-center justify-center w-full px-4 py-2 bg-danger-600 text-white font-medium rounded-lg hover:bg-danger-700 transition-colors">
                        Call Now
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </a>
                </div>
                <div class="absolute top-4 right-4 bg-danger-100 text-danger-600 px-2 py-1 rounded-full text-xs font-medium">
                    Premium
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Help Topics -->
<div class="bg-secondary-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Popular help topics
            </h2>
            <p class="text-lg text-secondary-600">
                Quick answers to the most common questions
            </p>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Getting Started -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">Getting Started</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• How to create your first API</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Setting up authentication</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Understanding rate limits</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Making your first API call</a></li>
                </ul>
                <a href="{{ route('portal.getting-started') }}" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View all guides
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>

            <!-- API Management -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-success-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">API Management</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Managing API keys</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Configuring rate limits</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Setting up webhooks</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• API versioning best practices</a></li>
                </ul>
                <a href="#" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View all guides
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>

            <!-- Analytics & Monitoring -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-warning-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">Analytics & Monitoring</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Understanding analytics dashboard</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Setting up alerts</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Monitoring API performance</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Exporting usage data</a></li>
                </ul>
                <a href="#" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View all guides
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>

            <!-- Security -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-danger-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">Security</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• API key security best practices</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• IP whitelisting</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• HTTPS enforcement</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Handling compromised keys</a></li>
                </ul>
                <a href="#" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View all guides
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>

            <!-- Billing & Plans -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">Billing & Plans</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Understanding pricing plans</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Upgrading your account</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Managing billing information</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Usage limits and overages</a></li>
                </ul>
                <a href="{{ route('portal.pricing') }}" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View pricing
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>

            <!-- Troubleshooting -->
            <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center mb-4">
                    <div class="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900">Troubleshooting</h3>
                </div>
                <ul class="space-y-2 text-sm text-secondary-600">
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Common error codes</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• API not responding</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Authentication failures</a></li>
                    <li><a href="#" class="hover:text-primary-600 transition-colors">• Rate limit exceeded</a></li>
                </ul>
                <a href="#" class="inline-flex items-center mt-4 text-primary-600 hover:text-primary-700 font-medium">
                    View all guides
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="bg-white py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Frequently Asked Questions
            </h2>
            <p class="text-lg text-secondary-600">
                Quick answers to help you get the most out of our platform
            </p>
        </div>

        <div class="space-y-6" x-data="{ openFaq: null }">
            <!-- FAQ Item 1 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 1 ? null : 1"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            How do I get started with API Gateway Manager?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 1 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 1" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            Getting started is easy! Simply create an account, set up your first API, generate API keys, and start making API calls.
                            Our platform provides a step-by-step onboarding process to guide you through each step.
                            Check out our <a href="{{ route('portal.getting-started') }}" class="text-primary-600 hover:text-primary-500 font-medium">getting started guide</a> for detailed instructions.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 2 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 2 ? null : 2"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            What are the rate limits for API calls?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 2 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 2" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            Rate limits depend on your plan and can be customized per API key. Free accounts have basic rate limits (10,000 requests/month),
                            while paid plans offer higher limits (1M+ requests/month) and custom configurations. You can view and modify your specific limits
                            in the dashboard under API Settings.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 3 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 3 ? null : 3"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            How do I authenticate API requests?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 3 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 3" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600 mb-3">
                            Include your API key in the request header as <code class="bg-secondary-100 px-2 py-1 rounded text-sm font-mono">X-API-Key: your_api_key</code>.
                            All API keys start with the prefix "agm_" for easy identification.
                        </p>
                        <p class="text-secondary-600">
                            Example: <code class="bg-secondary-100 px-2 py-1 rounded text-sm font-mono">X-API-Key: agm_1234567890abcdef</code>
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 4 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 4 ? null : 4"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            Can I monitor my API usage and performance?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 4 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 4" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            Absolutely! Your developer dashboard provides comprehensive analytics including request counts, response times, error rates,
                            geographic distribution, and usage trends. You can filter data by date range, API key, endpoint, or status code.
                            Pro and Enterprise plans also include real-time monitoring and custom alerts.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 5 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 5 ? null : 5"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            What happens if my API key is compromised?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 5 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 5" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            You can immediately deactivate or regenerate your API key from the developer dashboard with just one click.
                            We also provide real-time usage monitoring and anomaly detection to help you identify unusual activity.
                            For added security, consider using IP whitelisting and setting up usage alerts.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 6 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 6 ? null : 6"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            How do I upgrade my plan or add more API calls?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 6 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 6" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            You can upgrade your plan anytime from the billing section in your dashboard. Changes take effect immediately,
                            and we'll prorate any billing differences. If you need a custom plan or have specific requirements,
                            contact our sales team for a tailored solution.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 7 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 7 ? null : 7"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            Do you provide webhooks and real-time notifications?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 7 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 7" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            Yes! Pro and Enterprise plans include webhook support for real-time notifications about API events,
                            usage thresholds, errors, and system status. You can configure webhooks to send data to your endpoints
                            when specific events occur, enabling seamless integration with your existing systems.
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 8 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200">
                <div class="p-6">
                    <button @click="openFaq = openFaq === 8 ? null : 8"
                            class="flex justify-between items-center w-full text-left">
                        <h3 class="text-lg font-semibold text-secondary-900">
                            What support options are available?
                        </h3>
                        <svg class="h-5 w-5 text-secondary-400 transform transition-transform flex-shrink-0 ml-4"
                             :class="{ 'rotate-180': openFaq === 8 }"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 8" x-transition class="mt-4 pt-4 border-t border-secondary-100">
                        <p class="text-secondary-600">
                            We offer multiple support channels: community forums and documentation for all users,
                            priority email support for Pro customers (4-hour response time), and 24/7 phone and chat support
                            for Enterprise customers. All plans include access to our comprehensive documentation and API reference.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Contact Form Section -->
<div id="contact-form" class="bg-secondary-50 py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Still need help?
            </h2>
            <p class="text-lg text-secondary-600">
                Can't find what you're looking for? Send us a message and we'll get back to you within 2 hours.
            </p>
        </div>

        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2">
                <!-- Contact Info -->
                <div class="bg-primary-600 p-8 lg:p-12">
                    <h3 class="text-2xl font-bold text-white mb-6">Get in touch</h3>
                    <p class="text-primary-100 mb-8">
                        Our support team is here to help you succeed. Choose the best way to reach us.
                    </p>

                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-white font-medium">Email Support</h4>
                                <p class="text-primary-100 text-sm"><EMAIL></p>
                                <p class="text-primary-200 text-xs">Response within 2 hours</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-white font-medium">Live Chat</h4>
                                <p class="text-primary-100 text-sm">Available 24/7</p>
                                <p class="text-primary-200 text-xs">Instant responses</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-white font-medium">Phone Support</h4>
                                <p class="text-primary-100 text-sm">+****************</p>
                                <p class="text-primary-200 text-xs">Pro & Enterprise only</p>
                            </div>
                        </div>
                    </div>

                    <!-- Response Time Guarantee -->
                    <div class="mt-8 p-4 bg-white bg-opacity-10 rounded-lg">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-white text-sm font-medium">98% customer satisfaction rate</span>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="p-8 lg:p-12">
                    <form class="space-y-6" action="#" method="POST">
                        @csrf

                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                                    Full Name *
                                </label>
                                <input type="text" id="name" name="name" required
                                       class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                                       placeholder="John Doe">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-secondary-700 mb-2">
                                    Email Address *
                                </label>
                                <input type="email" id="email" name="email" required
                                       class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div>
                            <label for="company" class="block text-sm font-medium text-secondary-700 mb-2">
                                Company (Optional)
                            </label>
                            <input type="text" id="company" name="company"
                                   class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                                   placeholder="Your Company">
                        </div>

                        <div>
                            <label for="subject" class="block text-sm font-medium text-secondary-700 mb-2">
                                How can we help? *
                            </label>
                            <select id="subject" name="subject" required
                                    class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                                <option value="">Select a topic</option>
                                <option value="technical">Technical Support</option>
                                <option value="billing">Billing & Account</option>
                                <option value="feature">Feature Request</option>
                                <option value="bug">Bug Report</option>
                                <option value="integration">Integration Help</option>
                                <option value="sales">Sales Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-secondary-700 mb-2">
                                Priority Level
                            </label>
                            <select id="priority" name="priority"
                                    class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                                <option value="low">Low - General question</option>
                                <option value="medium" selected>Medium - Need assistance</option>
                                <option value="high">High - Blocking issue</option>
                                <option value="urgent">Urgent - Production down</option>
                            </select>
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium text-secondary-700 mb-2">
                                Message *
                            </label>
                            <textarea id="message" name="message" rows="6" required
                                      class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                                      placeholder="Please describe your issue or question in detail. Include any relevant error messages, steps to reproduce, or specific requirements..."></textarea>
                        </div>

                        <div class="flex items-start">
                            <input type="checkbox" id="updates" name="updates"
                                   class="mt-1 h-4 w-4 text-primary-600 border-secondary-300 rounded focus:ring-primary-500">
                            <label for="updates" class="ml-2 text-sm text-secondary-600">
                                I'd like to receive product updates and helpful tips via email
                            </label>
                        </div>

                        <div>
                            <button type="submit" class="w-full inline-flex items-center justify-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200 shadow-lg hover:shadow-xl">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Send Message
                            </button>
                        </div>

                        <p class="text-xs text-secondary-500 text-center">
                            By submitting this form, you agree to our privacy policy and terms of service.
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Resources -->
<div class="bg-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Additional Resources
            </h2>
            <p class="text-lg text-secondary-600">
                Explore more ways to get help and stay informed
            </p>
        </div>

        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            <a href="{{ route('docs.index') }}" class="group bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary-200 transition-colors">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">API Documentation</h3>
                <p class="text-secondary-600 text-sm">Complete API reference and integration guides</p>
            </a>

            <a href="{{ route('portal.getting-started') }}" class="group bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-success-200 transition-colors">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Quick Start Guide</h3>
                <p class="text-secondary-600 text-sm">Get up and running in under 5 minutes</p>
            </a>

            <a href="#" class="group bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-warning-200 transition-colors">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Community Forum</h3>
                <p class="text-secondary-600 text-sm">Connect with other developers and share knowledge</p>
            </a>

            <a href="#" class="group bg-white rounded-xl shadow-lg border border-secondary-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-danger-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-danger-200 transition-colors">
                    <svg class="h-6 w-6 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Status Page</h3>
                <p class="text-secondary-600 text-sm">Real-time system status and incident reports</p>
            </a>
        </div>
    </div>
</div>

<script>
function openChat() {
    // Placeholder for chat widget integration
    alert('Live chat would open here. Integration with chat service like Intercom, Zendesk, or custom solution.');
}
</script>
@endsection
