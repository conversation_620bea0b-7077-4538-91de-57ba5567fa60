@extends('layouts.app')

@section('title', '403 - Access Forbidden')

@section('content')
<div class="min-h-screen flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Illustration -->
        <div class="mx-auto">
            <svg class="h-32 w-32 sm:h-40 sm:w-40 text-danger-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl sm:text-8xl font-bold text-secondary-900">403</h1>
            <h2 class="mt-4 text-2xl sm:text-3xl font-bold text-secondary-900">Access Forbidden</h2>
            <p class="mt-4 text-lg text-secondary-600">
                You don't have permission to access this resource. This could be due to insufficient privileges or an expired session.
            </p>
        </div>

        <!-- Actions -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="history.back()" class="w-full sm:w-auto btn-outline">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
            
            @auth
                @if(auth()->user()->role === 'admin')
                    <a href="{{ route('admin.dashboard') }}" class="w-full sm:w-auto btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Admin Dashboard
                    </a>
                @else
                    <a href="{{ route('developer.dashboard') }}" class="w-full sm:w-auto btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Dashboard
                    </a>
                @endif
                
                <form method="POST" action="{{ route('logout') }}" class="inline w-full sm:w-auto">
                    @csrf
                    <button type="submit" class="w-full sm:w-auto btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Sign Out
                    </button>
                </form>
            @else
                <a href="{{ route('login') }}" class="w-full sm:w-auto btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Sign In
                </a>
            @endauth
        </div>

        <!-- Help Information -->
        <div class="pt-8 border-t border-secondary-200">
            <h3 class="text-sm font-medium text-secondary-900 mb-3">Why am I seeing this?</h3>
            <div class="text-sm text-secondary-600 space-y-2 text-left">
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-2 h-2 bg-secondary-400 rounded-full mt-2 mr-3"></span>
                    <span>You may not have the required permissions to access this resource</span>
                </div>
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-2 h-2 bg-secondary-400 rounded-full mt-2 mr-3"></span>
                    <span>Your session may have expired - try signing in again</span>
                </div>
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-2 h-2 bg-secondary-400 rounded-full mt-2 mr-3"></span>
                    <span>The resource may be restricted to certain user roles</span>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="pt-6">
            <p class="text-sm text-secondary-500 mb-4">Still need help?</p>
            <div class="flex flex-col sm:flex-row sm:justify-center space-y-2 sm:space-y-0 sm:space-x-6">
                <a href="{{ route('portal.support') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    💬 Contact Support
                </a>
                <a href="{{ route('docs.index') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    📚 Documentation
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Track 403 errors for security monitoring
if (typeof gtag !== 'undefined') {
    gtag('event', 'access_forbidden', {
        'page_location': window.location.href,
        'page_referrer': document.referrer,
        'user_authenticated': {{ auth()->check() ? 'true' : 'false' }},
        @auth
        'user_role': '{{ auth()->user()->role }}'
        @endauth
    });
}
</script>
@endpush
@endsection
