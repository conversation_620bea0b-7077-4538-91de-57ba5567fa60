<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AppController;
use App\Http\Controllers\ApiKeyController;
use App\Http\Controllers\AppEndpointController;
use App\Http\Controllers\ApiProxyController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\ApiProxyManagementController;
use App\Http\Controllers\Admin\RateLimitController;
use App\Http\Controllers\Admin\AnalyticsController as AdminAnalyticsController;
use App\Http\Controllers\Admin\RequestLogController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\AppManagementController;
use App\Http\Controllers\Developer\DeveloperDashboardController;
use App\Http\Controllers\Developer\AnalyticsController as DeveloperAnalyticsController;
use App\Http\Controllers\PortalController;
use App\Http\Controllers\ApiDocumentationController;
use Illuminate\Support\Facades\Route;

Route::get('/', [PortalController::class, 'index'])->name('portal.index');

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::resource('api-proxies', ApiProxyManagementController::class);
    Route::post('api-proxies/{apiProxy}/test', [ApiProxyManagementController::class, 'test'])->name('api-proxies.test');
    Route::resource('rate-limits', RateLimitController::class);
    Route::post('rate-limits/{rateLimit}/reset', [RateLimitController::class, 'reset'])->name('rate-limits.reset');

    // Analytics routes
    Route::get('/analytics', [AdminAnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/data', [AdminAnalyticsController::class, 'data'])->name('analytics.data');

    // Request logs routes
    Route::resource('request-logs', RequestLogController::class)->only(['index', 'show', 'destroy']);
    Route::post('request-logs/cleanup', [RequestLogController::class, 'cleanup'])->name('request-logs.cleanup');
    Route::get('request-logs/export', [RequestLogController::class, 'export'])->name('request-logs.export');

    // User management routes
    Route::resource('users', AdminUserController::class);
    Route::post('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');

    // App management routes
    Route::resource('apps', AppManagementController::class)->only(['index', 'show', 'edit', 'update', 'destroy']);
    Route::post('apps/{app}/toggle-status', [AppManagementController::class, 'toggleStatus'])->name('apps.toggle-status');
    Route::get('apps/{app}/api-keys', [AppManagementController::class, 'apiKeys'])->name('apps.api-keys');
    Route::post('apps/{app}/deactivate-api-keys', [AppManagementController::class, 'deactivateApiKeys'])->name('apps.deactivate-api-keys');
});

// Developer routes
Route::middleware(['auth', 'developer'])->prefix('developer')->name('developer.')->group(function () {
    Route::get('/dashboard', [DeveloperDashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [DeveloperAnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/data', [DeveloperAnalyticsController::class, 'data'])->name('analytics.data');
    Route::get('/analytics/logs', [DeveloperAnalyticsController::class, 'logs'])->name('analytics.logs');
    Route::get('/analytics/app/{app}', [DeveloperAnalyticsController::class, 'app'])->name('analytics.app');
    Route::get('/documentation', [DeveloperDashboardController::class, 'documentation'])->name('documentation');
    Route::get('/support', [DeveloperDashboardController::class, 'support'])->name('support');
});

// App management routes
Route::middleware(['auth', 'developer'])->prefix('developer')->name('developer.')->group(function () {
    Route::resource('apps', AppController::class);
    Route::resource('api-keys', ApiKeyController::class);
    Route::post('api-keys/{apiKey}/regenerate', [ApiKeyController::class, 'regenerate'])->name('api-keys.regenerate');

    // App endpoints management
    Route::resource('apps.endpoints', AppEndpointController::class)->parameters([
        'endpoints' => 'apiProxy'
    ]);
    Route::post('apps/{app}/endpoints/{apiProxy}/test', [AppEndpointController::class, 'test'])->name('apps.endpoints.test');
});

require __DIR__.'/auth.php';

// Public portal routes
Route::get('/getting-started', [PortalController::class, 'gettingStarted'])->name('portal.getting-started');
Route::get('/api-reference', [PortalController::class, 'apiReference'])->name('portal.api-reference');
Route::get('/pricing', [PortalController::class, 'pricing'])->name('portal.pricing');
Route::get('/support', [PortalController::class, 'support'])->name('portal.support');
Route::post('/support', [PortalController::class, 'submitSupport'])->name('portal.support.submit');

// API Documentation routes
Route::prefix('docs')->name('docs.')->group(function () {
    Route::get('/', [ApiDocumentationController::class, 'index'])->name('index');
    Route::get('/api/{apiProxy}', [ApiDocumentationController::class, 'show'])->name('show');
    Route::get('/swagger', [ApiDocumentationController::class, 'swagger'])->name('swagger');
    Route::get('/openapi.json', [ApiDocumentationController::class, 'openapi'])->name('openapi');
});

// API Proxy routes - These should be last to catch all API requests
Route::any('/api/{path?}', [ApiProxyController::class, 'handle'])
    ->where('path', '.*')
    ->middleware(['validate.api'])
    ->name('api.proxy');
