<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\RequestLog;
use App\Models\ApiProxy;

class PortalTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_view_home_page()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewIs('portal.home');
        $response->assertSee('API Gateway Manager');
    }

    public function test_can_view_pricing_page()
    {
        $response = $this->get('/pricing');

        $response->assertStatus(200);
        $response->assertViewIs('portal.pricing');
        $response->assertSee('Pricing Plans');
    }

    public function test_can_view_support_page()
    {
        $response = $this->get('/support');

        $response->assertStatus(200);
        $response->assertViewIs('portal.support');
        $response->assertSee('Support');
    }

    public function test_home_page_shows_statistics()
    {
        // Create test data
        User::factory()->count(10)->create(['role' => 'developer']);
        $apps = App::factory()->count(5)->create();
        $apiKeys = ApiKey::factory()->count(8)->create(['app_id' => $apps->random()->id]);
        ApiProxy::factory()->count(3)->create(['is_active' => true]);

        RequestLog::factory()->count(100)->create([
            'api_key_id' => $apiKeys->random()->id,
            'api_proxy_id' => ApiProxy::factory()->create()->id
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        // Just check that stats exist, don't check exact values
        $response->assertSee('developers');
    }

    public function test_home_page_shows_featured_apis()
    {
        $featuredApi = ApiProxy::factory()->create([
            'name' => 'Featured API',
            'description' => 'This is a featured API',
            'is_active' => true
        ]);
        
        $regularApi = ApiProxy::factory()->create([
            'name' => 'Regular API',
            'is_active' => true
        ]);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('featured_apis');
        $response->assertSee('Featured API');
    }

    public function test_pricing_page_shows_all_plans()
    {
        $response = $this->get('/pricing');

        $response->assertStatus(200);
        $response->assertSee('Free');
        $response->assertSee('Professional');
        $response->assertSee('Enterprise');
    }

    public function test_support_page_has_contact_form()
    {
        $response = $this->get('/support');

        $response->assertStatus(200);
        $response->assertSee('Contact Us');
        $response->assertSee('name="name"', false);
        $response->assertSee('name="email"', false);
        $response->assertSee('name="subject"', false);
        $response->assertSee('name="message"', false);
    }

    public function test_can_submit_support_form()
    {
        $response = $this->post('/support', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'This is a test message.',
        ]);

        $response->assertRedirect('/support');
        $response->assertSessionHas('success');
    }

    public function test_support_form_validates_required_fields()
    {
        $response = $this->post('/support', []);

        $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
    }

    public function test_support_form_validates_email_format()
    {
        $response = $this->post('/support', [
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'subject' => 'Test Subject',
            'message' => 'This is a test message.',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    public function test_home_page_handles_no_data()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(0, $stats['total_developers']);
        $this->assertEquals(0, $stats['total_apps']);
        $this->assertEquals(0, $stats['total_apis']);
        $this->assertEquals(0, $stats['total_requests']);
    }

    public function test_home_page_shows_recent_activity()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        RequestLog::factory()->count(15)->create(['api_key_id' => $apiKey->id]);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('recent_activity');
        
        $recentActivity = $response->viewData('recent_activity');
        $this->assertCount(10, $recentActivity); // Should limit to 10
    }

    public function test_home_page_shows_success_metrics()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        // Create 9 successful and 1 failed request
        RequestLog::factory()->count(9)->successful()->create(['api_key_id' => $apiKey->id]);
        RequestLog::factory()->count(1)->failed()->create(['api_key_id' => $apiKey->id]);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(90.0, $stats['success_rate']); // 9/10 * 100 = 90%
    }

    public function test_home_page_shows_average_response_time()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        // Create requests with specific response times
        RequestLog::factory()->create(['api_key_id' => $apiKey->id, 'response_time_ms' => 100]);
        RequestLog::factory()->create(['api_key_id' => $apiKey->id, 'response_time_ms' => 200]);
        RequestLog::factory()->create(['api_key_id' => $apiKey->id, 'response_time_ms' => 300]);

        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(200.0, $stats['avg_response_time']); // (100+200+300)/3 = 200
    }

    public function test_pricing_page_shows_feature_comparison()
    {
        $response = $this->get('/pricing');

        $response->assertStatus(200);
        $response->assertSee('1,000 requests/month');
        $response->assertSee('10,000 requests/month');
        $response->assertSee('Unlimited requests');
        $response->assertSee('Email support');
        $response->assertSee('Priority support');
        $response->assertSee('24/7 phone support');
    }

    public function test_support_page_shows_faq()
    {
        $response = $this->get('/support');

        $response->assertStatus(200);
        $response->assertSee('Frequently Asked Questions');
        $response->assertSee('How do I get started?');
        $response->assertSee('What are the rate limits?');
    }

    public function test_home_page_performance_with_large_dataset()
    {
        // Create a large dataset
        User::factory()->count(1000)->create(['role' => 'developer']);
        $apps = App::factory()->count(500)->create();
        $apiKeys = ApiKey::factory()->count(2000)->create(['app_id' => $apps->random()->id]);
        ApiProxy::factory()->count(100)->create(['is_active' => true]);
        
        // Don't create too many request logs to keep test reasonable
        RequestLog::factory()->count(1000)->create(['api_key_id' => $apiKeys->random()->id]);

        $startTime = microtime(true);
        $response = $this->get('/');
        $endTime = microtime(true);

        $response->assertStatus(200);
        
        // Home page should load in reasonable time (less than 3 seconds)
        $this->assertLessThan(3.0, $endTime - $startTime);
    }
}
