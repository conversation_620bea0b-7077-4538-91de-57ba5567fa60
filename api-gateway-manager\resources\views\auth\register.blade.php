@extends('layouts.guest')

@section('title', 'Sign Up - ' . config('app.name'))
@section('meta_description', 'Create your API Gateway Manager account to start managing APIs, generating keys, and accessing analytics.')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Create your account
        </h2>
        <p class="mt-2 text-sm text-secondary-600">
            Get started with API Gateway Manager today
        </p>
    </div>

    <!-- Registration Form -->
    <form method="POST" action="{{ route('register') }}" class="space-y-6">
        @csrf

        <!-- Name -->
        <div>
            <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                Full name
            </label>
            <input id="name"
                   name="name"
                   type="text"
                   autocomplete="name"
                   required
                   autofocus
                   value="{{ old('name') }}"
                   class="form-input @error('name') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                   placeholder="Enter your full name">
            @error('name')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Email Address -->
        <div>
            <label for="email" class="block text-sm font-medium text-secondary-700 mb-2">
                Email address
            </label>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   value="{{ old('email') }}"
                   class="form-input @error('email') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                   placeholder="Enter your email address">
            @error('email')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Password -->
        <div>
            <label for="password" class="block text-sm font-medium text-secondary-700 mb-2">
                Password
            </label>
            <div class="relative">
                <input id="password"
                       name="password"
                       type="password"
                       autocomplete="new-password"
                       required
                       class="form-input pr-10 @error('password') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Create a strong password">
                <button type="button"
                        onclick="togglePassword('password', 'eye-icon-1')"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg id="eye-icon-1" class="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                </button>
            </div>
            @error('password')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
            <p class="mt-2 text-xs text-secondary-500">
                Must be at least 8 characters with letters and numbers
            </p>
        </div>

        <!-- Confirm Password -->
        <div>
            <label for="password_confirmation" class="block text-sm font-medium text-secondary-700 mb-2">
                Confirm password
            </label>
            <div class="relative">
                <input id="password_confirmation"
                       name="password_confirmation"
                       type="password"
                       autocomplete="new-password"
                       required
                       class="form-input pr-10 @error('password_confirmation') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Confirm your password">
                <button type="button"
                        onclick="togglePassword('password_confirmation', 'eye-icon-2')"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg id="eye-icon-2" class="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                </button>
            </div>
            @error('password_confirmation')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Terms and Privacy -->
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="terms"
                       name="terms"
                       type="checkbox"
                       required
                       class="form-checkbox">
            </div>
            <div class="ml-3 text-sm">
                <label for="terms" class="text-secondary-700">
                    I agree to the
                    <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Terms of Service</a>
                    and
                    <a href="#" class="text-primary-600 hover:text-primary-500 font-medium">Privacy Policy</a>
                </label>
            </div>
        </div>

        <!-- Submit Button -->
        <div>
            <button type="submit" class="btn-primary w-full">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
                Create account
            </button>
        </div>

        <!-- Sign in link -->
        <div class="text-center">
            <p class="text-sm text-secondary-600">
                Already have an account?
                <a href="{{ route('login') }}" class="font-medium text-primary-600 hover:text-primary-500">
                    Sign in here
                </a>
            </p>
        </div>
    </form>
</div>

@push('scripts')
<script>
function togglePassword(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(iconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
        `;
    } else {
        passwordInput.type = 'password';
        eyeIcon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        `;
    }
}
</script>
@endpush
@endsection
