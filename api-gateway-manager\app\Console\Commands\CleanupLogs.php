<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RequestLog;
use Carbon\Carbon;

class CleanupLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:cleanup-logs {--days=30 : Number of days to keep logs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old request logs to save database space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $cutoffDate = Carbon::now()->subDays($days);

        $this->info("Cleaning up request logs older than {$days} days...");
        $this->info("Cutoff date: {$cutoffDate->format('Y-m-d H:i:s')}");

        // Count logs to be deleted
        $logsToDelete = RequestLog::where('requested_at', '<', $cutoffDate)->count();

        if ($logsToDelete === 0) {
            $this->info('No old logs found to clean up.');
            return 0;
        }

        $this->info("Found {$logsToDelete} logs to delete.");

        if ($this->confirm('Do you want to proceed with the cleanup?')) {
            // Delete in chunks to avoid memory issues
            $chunkSize = 1000;
            $totalDeleted = 0;

            $this->output->progressStart($logsToDelete);

            while (true) {
                $deleted = RequestLog::where('requested_at', '<', $cutoffDate)
                    ->limit($chunkSize)
                    ->delete();

                if ($deleted === 0) {
                    break;
                }

                $totalDeleted += $deleted;
                $this->output->progressAdvance($deleted);
            }

            $this->output->progressFinish();
            $this->info("Successfully deleted {$totalDeleted} old request logs.");

            // Optimize the table after cleanup
            $this->info('Optimizing database table...');
            \DB::statement('OPTIMIZE TABLE request_logs');
            $this->info('Database optimization completed.');

        } else {
            $this->info('Cleanup cancelled.');
        }

        return 0;
    }
}
