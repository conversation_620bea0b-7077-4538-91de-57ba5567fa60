<?php $__env->startSection('title', 'API Gateway Manager - Secure, Scalable API Management'); ?>
<?php $__env->startSection('meta_description', 'Powerful API gateway solution for modern applications. Manage API keys, rate limiting, analytics, and more with our developer-friendly platform.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
        </svg>
    </div>

    <div class="relative w-full px-4 py-16 sm:px-6 lg:px-8 lg:py-32">
        <div class="max-w-7xl mx-auto">
            <div class="text-center">
                <!-- Trust Badge -->
                <div class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    Trusted by 10,000+ developers
                </div>

                <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
                    Powerful API
                    <span class="text-primary-200">Management</span>
                    <span class="block">Platform</span>
                </h1>
                <p class="text-xl text-primary-100 max-w-4xl mx-auto mb-8">
                    Secure, scalable, and simple API gateway solution for modern applications.
                    Manage your APIs with confidence, monitor performance in real-time, and scale effortlessly.
                </p>

                <!-- Key Benefits -->
                <div class="flex flex-wrap justify-center gap-8 mb-10">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-white font-medium">5-min Setup</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-white font-medium">99.9% Uptime</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-white font-medium">24/7 Support</span>
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                    <a href="<?php echo e(route('register')); ?>" class="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-white rounded-lg hover:bg-primary-50 transition-all duration-200 shadow-lg hover:shadow-xl">
                        Get Started Free
                        <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                    </a>
                    <a href="<?php echo e(route('docs.index')); ?>" class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white hover:text-primary-600 transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        View Documentation
                    </a>
                </div>

                <!-- Dashboard Preview - Centered -->
                <div class="max-w-4xl mx-auto">
                    <div class="relative bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 shadow-2xl">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-white font-semibold text-lg">Live Dashboard</h3>
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-green-400 text-sm font-medium">Live</span>
                            </div>
                        </div>

                        <!-- Stats Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-white text-2xl font-bold">1.2M</div>
                                <div class="text-primary-200 text-sm">Requests Today</div>
                                <div class="mt-2 flex items-center text-green-400 text-xs">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    +12%
                                </div>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-white text-2xl font-bold">45ms</div>
                                <div class="text-primary-200 text-sm">Avg Response</div>
                                <div class="mt-2 flex items-center text-green-400 text-xs">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                    -8ms
                                </div>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-white text-2xl font-bold">99.9%</div>
                                <div class="text-primary-200 text-sm">Success Rate</div>
                                <div class="mt-2 flex items-center text-green-400 text-xs">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    Stable
                                </div>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-4 backdrop-blur-sm">
                                <div class="text-white text-2xl font-bold">24</div>
                                <div class="text-primary-200 text-sm">Active APIs</div>
                                <div class="mt-2 flex items-center text-blue-400 text-xs">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    +3 new
                                </div>
                            </div>
                        </div>

                        <!-- Status Indicators -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-white text-sm">API Gateway</span>
                                </div>
                                <span class="text-green-400 text-sm font-medium">Operational</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-white text-sm">Rate Limiting</span>
                                </div>
                                <span class="text-green-400 text-sm font-medium">Active</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-white text-sm">Analytics</span>
                                </div>
                                <span class="text-green-400 text-sm font-medium">Recording</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                    <span class="text-white text-sm">Security</span>
                                </div>
                                <span class="text-green-400 text-sm font-medium">Protected</span>
                            </div>
                        </div>
                    </div>

                    <!-- Floating Elements -->
                    <div class="absolute -top-4 -right-4 bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium animate-bounce shadow-lg">
                        99.9% Uptime
                    </div>
                    <div class="absolute -bottom-4 -left-4 bg-warning-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                        Real-time Analytics
                    </div>
                </div>

                <!-- Social Proof -->
                <div class="mt-12 text-primary-200 text-sm">
                    No credit card required • Free forever plan • Enterprise ready
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Everything you need to manage APIs
            </h2>
            <p class="text-lg text-secondary-600">
                Built for developers, designed for scale
            </p>
        </div>

        <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Feature 1 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">API Key Management</h3>
                <p class="text-secondary-600">
                    Generate, manage, and secure API keys with advanced authentication and authorization controls.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Analytics & Monitoring</h3>
                <p class="text-secondary-600">
                    Real-time analytics, usage tracking, and performance monitoring for all your APIs.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Rate Limiting</h3>
                <p class="text-secondary-600">
                    Protect your APIs with configurable rate limiting and quota management.
                </p>
            </div>

            <!-- Feature 4 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-danger-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Security First</h3>
                <p class="text-secondary-600">
                    Enterprise-grade security with encryption, validation, and threat detection.
                </p>
            </div>

            <!-- Feature 5 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Developer Portal</h3>
                <p class="text-secondary-600">
                    Self-service portal for developers with documentation, testing tools, and analytics.
                </p>
            </div>

            <!-- Feature 6 -->
            <div class="bg-white rounded-xl shadow-lg border border-secondary-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">High Performance</h3>
                <p class="text-secondary-600">
                    Optimized for speed and scalability with efficient request routing and caching.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- How It Works Section -->
<div class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                Get started in minutes
            </h2>
            <p class="text-lg text-secondary-600 max-w-2xl mx-auto">
                Simple setup process that gets you from zero to production-ready API gateway in no time
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="relative text-center">
                <div class="flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-6">
                    <span class="text-2xl font-bold text-primary-600">1</span>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Create Account</h3>
                <p class="text-secondary-600">
                    Sign up for free and get instant access to your API management dashboard
                </p>
                <!-- Arrow -->
                <div class="hidden md:block absolute top-8 -right-4 text-primary-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>

            <!-- Step 2 -->
            <div class="relative text-center">
                <div class="flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-6">
                    <span class="text-2xl font-bold text-primary-600">2</span>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Configure APIs</h3>
                <p class="text-secondary-600">
                    Add your APIs, set up rate limits, and configure security policies with our intuitive interface
                </p>
                <!-- Arrow -->
                <div class="hidden md:block absolute top-8 -right-4 text-primary-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>

            <!-- Step 3 -->
            <div class="text-center">
                <div class="flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mx-auto mb-6">
                    <span class="text-2xl font-bold text-primary-600">3</span>
                </div>
                <h3 class="text-xl font-semibold text-secondary-900 mb-4">Go Live</h3>
                <p class="text-secondary-600">
                    Deploy your API gateway and start monitoring traffic, analytics, and performance in real-time
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="bg-secondary-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 mb-4">
                Trusted by developers worldwide
            </h2>
            <p class="text-lg text-secondary-600">
                Join thousands of companies using our platform to manage their APIs
            </p>
        </div>

        <div class="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div class="text-center">
                <div class="text-4xl font-bold text-primary-600 mb-2"><?php echo e($stats['total_apis'] ?? '1,000+'); ?></div>
                <div class="text-sm text-secondary-600 uppercase tracking-wide">Active APIs</div>
                <div class="text-xs text-secondary-500 mt-1">Managed daily</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-primary-600 mb-2"><?php echo e($stats['total_developers'] ?? '10,000+'); ?></div>
                <div class="text-sm text-secondary-600 uppercase tracking-wide">Developers</div>
                <div class="text-xs text-secondary-500 mt-1">Trust our platform</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-primary-600 mb-2">99.9%</div>
                <div class="text-sm text-secondary-600 uppercase tracking-wide">Uptime</div>
                <div class="text-xs text-secondary-500 mt-1">SLA guaranteed</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-primary-600 mb-2">24/7</div>
                <div class="text-sm text-secondary-600 uppercase tracking-wide">Support</div>
                <div class="text-xs text-secondary-500 mt-1">Expert assistance</div>
            </div>
        </div>

        <!-- Company Logos -->
        <div class="mt-16">
            <p class="text-center text-sm text-secondary-500 mb-8">Trusted by leading companies</p>
            <div class="flex items-center justify-center space-x-8 opacity-60">
                <!-- Placeholder logos -->
                <div class="bg-secondary-300 h-8 w-24 rounded"></div>
                <div class="bg-secondary-300 h-8 w-20 rounded"></div>
                <div class="bg-secondary-300 h-8 w-28 rounded"></div>
                <div class="bg-secondary-300 h-8 w-22 rounded"></div>
                <div class="bg-secondary-300 h-8 w-26 rounded"></div>
            </div>
        </div>
    </div>
</div>

<!-- Testimonials Section -->
<div class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl mb-4">
                What developers are saying
            </h2>
            <p class="text-lg text-secondary-600">
                Don't just take our word for it
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Testimonial 1 -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-secondary-100">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    </div>
                </div>
                <p class="text-secondary-700 mb-6">
                    "This API gateway has transformed how we manage our microservices. The setup was incredibly easy and the analytics are fantastic."
                </p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-primary-600 font-semibold">JS</span>
                    </div>
                    <div>
                        <div class="font-semibold text-secondary-900">John Smith</div>
                        <div class="text-sm text-secondary-600">Lead Developer, TechCorp</div>
                    </div>
                </div>
            </div>

            <!-- Testimonial 2 -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-secondary-100">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    </div>
                </div>
                <p class="text-secondary-700 mb-6">
                    "The rate limiting and security features are exactly what we needed. Our API performance has improved significantly."
                </p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-success-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-success-600 font-semibold">MJ</span>
                    </div>
                    <div>
                        <div class="font-semibold text-secondary-900">Maria Johnson</div>
                        <div class="text-sm text-secondary-600">CTO, StartupXYZ</div>
                    </div>
                </div>
            </div>

            <!-- Testimonial 3 -->
            <div class="bg-white rounded-xl shadow-lg p-8 border border-secondary-100">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                    </div>
                </div>
                <p class="text-secondary-700 mb-6">
                    "Outstanding support team and documentation. We were up and running in less than an hour."
                </p>
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-warning-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-warning-600 font-semibold">DL</span>
                    </div>
                    <div>
                        <div class="font-semibold text-secondary-900">David Lee</div>
                        <div class="text-sm text-secondary-600">Senior Engineer, DataFlow</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="relative bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="cta-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#cta-grid)" />
        </svg>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-4xl font-bold text-white sm:text-5xl mb-6">
                Ready to transform your API management?
            </h2>
            <p class="text-xl text-primary-100 max-w-3xl mx-auto mb-8">
                Join thousands of developers who trust our platform to manage, secure, and scale their APIs.
                Start your free trial today and see the difference.
            </p>

            <!-- Benefits List -->
            <div class="flex flex-wrap justify-center gap-6 mb-10 text-primary-100">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>Free forever plan</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>No credit card required</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>5-minute setup</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                    <span>24/7 support</span>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="<?php echo e(route('register')); ?>" class="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-white rounded-lg hover:bg-primary-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    Start Free Trial
                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
                <a href="<?php echo e(route('docs.index')); ?>" class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white hover:text-primary-600 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    View Documentation
                </a>
                <a href="<?php echo e(route('portal.pricing')); ?>" class="text-primary-100 hover:text-white transition-colors duration-200 text-lg font-medium underline">
                    See Pricing Plans
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-12 pt-8 border-t border-primary-500 border-opacity-30">
                <p class="text-primary-200 text-sm mb-4">Trusted by developers at</p>
                <div class="flex items-center justify-center space-x-8 opacity-70">
                    <!-- Placeholder company names -->
                    <span class="text-primary-100 font-medium">TechCorp</span>
                    <span class="text-primary-100 font-medium">StartupXYZ</span>
                    <span class="text-primary-100 font-medium">DataFlow</span>
                    <span class="text-primary-100 font-medium">CloudSys</span>
                    <span class="text-primary-100 font-medium">DevTools</span>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/portal/index.blade.php ENDPATH**/ ?>