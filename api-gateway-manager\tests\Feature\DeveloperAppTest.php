<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;

class DeveloperAppTest extends TestCase
{
    use RefreshDatabase;

    public function test_developer_can_view_apps_index()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id, 'name' => 'Test App']);

        $response = $this->actingAs($user)->get('/developer/apps');

        $response->assertStatus(200);
        $response->assertViewIs('developer.apps.index');
        $response->assertSee('Test App');
    }

    public function test_developer_only_sees_own_apps()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        
        $app1 = App::factory()->create(['user_id' => $user1->id, 'name' => 'User 1 App']);
        $app2 = App::factory()->create(['user_id' => $user2->id, 'name' => 'User 2 App']);

        $response = $this->actingAs($user1)->get('/developer/apps');

        $response->assertStatus(200);
        $response->assertSee('User 1 App');
        $response->assertDontSee('User 2 App');
    }

    public function test_developer_can_create_app()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/apps/create');

        $response->assertStatus(200);
        $response->assertViewIs('developer.apps.create');
    }

    public function test_developer_can_store_app()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', [
            'name' => 'New App',
            'description' => 'A new application',
            'callback_url' => 'https://example.com/callback',
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('apps', [
            'user_id' => $user->id,
            'name' => 'New App',
            'description' => 'A new application',
            'callback_url' => 'https://example.com/callback',
            'is_active' => true,
        ]);
    }

    public function test_developer_can_view_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id, 'name' => 'View App']);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}");

        $response->assertStatus(200);
        $response->assertViewIs('developer.apps.show');
        $response->assertSee('View App');
    }

    public function test_developer_cannot_view_other_users_app()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);

        $response = $this->actingAs($user1)->get("/developer/apps/{$app->id}");

        $response->assertStatus(403);
    }

    public function test_developer_can_edit_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}/edit");

        $response->assertStatus(200);
        $response->assertViewIs('developer.apps.edit');
    }

    public function test_developer_can_update_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id, 'name' => 'Old Name']);

        $response = $this->actingAs($user)->put("/developer/apps/{$app->id}", [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'callback_url' => 'https://updated.com/callback',
            'is_active' => '0',
        ]);

        $response->assertRedirect("/developer/apps/{$app->id}");
        $this->assertDatabaseHas('apps', [
            'id' => $app->id,
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'callback_url' => 'https://updated.com/callback',
            'is_active' => false,
        ]);
    }

    public function test_developer_can_delete_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->delete("/developer/apps/{$app->id}");

        $response->assertRedirect('/developer/apps');
        $this->assertDatabaseMissing('apps', ['id' => $app->id]);
    }

    public function test_app_deletion_cascades_to_api_keys()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $this->assertDatabaseHas('api_keys', ['id' => $apiKey->id]);

        $response = $this->actingAs($user)->delete("/developer/apps/{$app->id}");

        $response->assertRedirect('/developer/apps');
        $this->assertDatabaseMissing('apps', ['id' => $app->id]);
        $this->assertDatabaseMissing('api_keys', ['id' => $apiKey->id]);
    }

    public function test_app_creation_validates_required_fields()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', []);

        $response->assertSessionHasErrors(['name']);
    }

    public function test_app_creation_validates_callback_url_format()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', [
            'name' => 'Test App',
            'callback_url' => 'invalid-url',
        ]);

        $response->assertSessionHasErrors(['callback_url']);
    }

    public function test_app_update_validates_required_fields()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->put("/developer/apps/{$app->id}", [
            'name' => '', // Empty name should fail validation
        ]);

        $response->assertSessionHasErrors(['name']);
    }

    public function test_app_show_displays_api_keys()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'name' => 'Test Key']);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}");

        $response->assertStatus(200);
        $response->assertSee('Test Key');
    }

    public function test_app_show_displays_statistics()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey1 = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => true]);
        $apiKey2 = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => false]);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}");

        $response->assertStatus(200);
        $response->assertViewHas('app');
        
        $viewApp = $response->viewData('app');
        $this->assertEquals(2, $viewApp->apiKeys->count());
    }

    public function test_apps_index_shows_pagination()
    {
        $user = User::factory()->create(['role' => 'developer']);
        
        // Create more than one page of apps
        App::factory()->count(30)->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get('/developer/apps');

        $response->assertStatus(200);
        $response->assertViewHas('apps');
        
        $apps = $response->viewData('apps');
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $apps);
    }

    public function test_app_creation_sets_default_values()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', [
            'name' => 'Default App',
            'description' => 'Test description',
        ]);

        $app = App::where('name', 'Default App')->first();
        
        $this->assertNotNull($app);
        $this->assertTrue($app->is_active); // Should default to true
        $this->assertEquals($user->id, $app->user_id);
    }

    public function test_admin_can_view_any_app()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $developer = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $developer->id]);

        $response = $this->actingAs($admin)->get("/developer/apps/{$app->id}");

        $response->assertStatus(200);
    }

    public function test_app_index_handles_empty_state()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/apps');

        $response->assertStatus(200);
        $response->assertSee('No applications found');
    }

    public function test_app_show_handles_inactive_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id, 'is_active' => false]);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}");

        $response->assertStatus(200);
        $response->assertSee('App Inactive');
    }
}
