<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Validator;

class ValidateApiRequestMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Validate request size
        if ($this->isRequestTooLarge($request)) {
            return response()->json([
                'error' => 'Request too large',
                'message' => 'The request payload is too large'
            ], 413);
        }

        // Validate content type for POST/PUT/PATCH requests
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            if (!$this->isValidContentType($request)) {
                return response()->json([
                    'error' => 'Invalid content type',
                    'message' => 'Content-Type must be application/json or application/x-www-form-urlencoded'
                ], 400);
            }
        }

        // Validate JSON payload if content type is JSON
        if ($request->isJson()) {
            if (!$this->isValidJson($request)) {
                return response()->json([
                    'error' => 'Invalid JSON',
                    'message' => 'The request contains invalid JSON'
                ], 400);
            }
        }

        // Check for suspicious patterns
        if ($this->hasSuspiciousContent($request)) {
            return response()->json([
                'error' => 'Invalid request',
                'message' => 'The request contains invalid content'
            ], 400);
        }

        // Validate headers
        if (!$this->hasValidHeaders($request)) {
            return response()->json([
                'error' => 'Invalid headers',
                'message' => 'The request contains invalid headers'
            ], 400);
        }

        return $next($request);
    }

    /**
     * Check if request is too large
     */
    private function isRequestTooLarge(Request $request): bool
    {
        $maxSize = 10 * 1024 * 1024; // 10MB
        return $request->server('CONTENT_LENGTH', 0) > $maxSize;
    }

    /**
     * Validate content type
     */
    private function isValidContentType(Request $request): bool
    {
        $contentType = $request->header('Content-Type', '');
        $allowedTypes = [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data'
        ];

        foreach ($allowedTypes as $type) {
            if (str_starts_with($contentType, $type)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate JSON payload
     */
    private function isValidJson(Request $request): bool
    {
        try {
            $request->json()->all();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Check for suspicious content patterns
     */
    private function hasSuspiciousContent(Request $request): bool
    {
        $suspiciousPatterns = [
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
            '/<iframe[^>]*>/i',
            '/<object[^>]*>/i',
            '/<embed[^>]*>/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+.*set/i',
        ];

        $content = $request->getContent();
        $queryString = $request->getQueryString();
        $allInput = json_encode($request->all());

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content) ||
                preg_match($pattern, $queryString) ||
                preg_match($pattern, $allInput)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate request headers
     */
    private function hasValidHeaders(Request $request): bool
    {
        // Check for excessively long headers
        foreach ($request->headers->all() as $name => $values) {
            if (strlen($name) > 100) {
                return false;
            }

            foreach ($values as $value) {
                if (strlen($value) > 1000) {
                    return false;
                }
            }
        }

        // Check for suspicious header patterns
        $suspiciousHeaders = [
            'X-Forwarded-For' => '/[^0-9.,\s:a-fA-F]/',
            'User-Agent' => '/<script|javascript:|vbscript:/i',
            'Referer' => '/<script|javascript:|vbscript:/i',
        ];

        foreach ($suspiciousHeaders as $header => $pattern) {
            $value = $request->header($header);
            if ($value && preg_match($pattern, $value)) {
                return false;
            }
        }

        return true;
    }
}
