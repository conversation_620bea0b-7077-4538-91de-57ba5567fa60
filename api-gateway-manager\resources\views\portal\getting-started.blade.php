@extends('layouts.app')

@section('title', 'Getting Started - ' . config('app.name'))
@section('meta_description', 'Learn how to get started with API Gateway Manager. Step-by-step guide to create your first API proxy and generate API keys.')

@section('content')
<div class="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-secondary-900 sm:text-4xl">
            Getting Started
        </h1>
        <p class="mt-4 text-lg text-secondary-600">
            Learn how to set up and use API Gateway Manager in just a few steps
        </p>
    </div>

    <!-- Steps -->
    <div class="space-y-12">
        <!-- Step 1 -->
        <div class="flex flex-col md:flex-row gap-6">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-lg">1</span>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-xl font-semibold text-secondary-900 mb-3">
                    Create Your Account
                </h3>
                <p class="text-secondary-600 mb-4">
                    Sign up for a free account to get started with API Gateway Manager. 
                    You'll automatically be assigned the developer role.
                </p>
                <div class="bg-secondary-50 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <svg class="h-5 w-5 text-primary-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span class="font-medium text-secondary-900">What you get:</span>
                    </div>
                    <ul class="text-sm text-secondary-600 space-y-1 ml-7">
                        <li>• Access to developer dashboard</li>
                        <li>• Ability to create and manage apps</li>
                        <li>• API key generation and management</li>
                        <li>• Usage analytics and monitoring</li>
                    </ul>
                </div>
                @guest
                <div class="mt-4">
                    <a href="{{ route('register') }}" class="btn-primary">
                        Create Account
                    </a>
                </div>
                @endguest
            </div>
        </div>

        <!-- Step 2 -->
        <div class="flex flex-col md:flex-row gap-6">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-lg">2</span>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-xl font-semibold text-secondary-900 mb-3">
                    Create Your First App
                </h3>
                <p class="text-secondary-600 mb-4">
                    Apps are containers for your API keys. Create an app to represent your project or application.
                </p>
                <div class="bg-secondary-50 rounded-lg p-4">
                    <code class="text-sm text-secondary-800">
                        1. Go to Developer Dashboard<br>
                        2. Click "Create New App"<br>
                        3. Fill in app details (name, description, callback URL)<br>
                        4. Click "Create App"
                    </code>
                </div>
            </div>
        </div>

        <!-- Step 3 -->
        <div class="flex flex-col md:flex-row gap-6">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-lg">3</span>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-xl font-semibold text-secondary-900 mb-3">
                    Generate API Keys
                </h3>
                <p class="text-secondary-600 mb-4">
                    Generate API keys for your app to authenticate API requests.
                </p>
                <div class="bg-secondary-50 rounded-lg p-4">
                    <code class="text-sm text-secondary-800">
                        1. Open your app details<br>
                        2. Go to "API Keys" tab<br>
                        3. Click "Generate New Key"<br>
                        4. Give your key a descriptive name<br>
                        5. Copy and securely store your API key
                    </code>
                </div>
                <div class="mt-4 p-4 bg-warning-50 border border-warning-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="h-5 w-5 text-warning-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium text-warning-800">Important:</span>
                    </div>
                    <p class="text-warning-700 mt-1 text-sm">
                        API keys are only shown once. Make sure to copy and store them securely.
                    </p>
                </div>
            </div>
        </div>

        <!-- Step 4 -->
        <div class="flex flex-col md:flex-row gap-6">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-lg">4</span>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-xl font-semibold text-secondary-900 mb-3">
                    Make Your First API Call
                </h3>
                <p class="text-secondary-600 mb-4">
                    Use your API key to make authenticated requests to available API endpoints.
                </p>
                <div class="bg-secondary-900 rounded-lg p-4 overflow-x-auto">
                    <code class="text-green-400 text-sm">
                        <span class="text-secondary-400"># Example API call</span><br>
                        curl -X GET "{{ config('app.url') }}/api/v1/example" \<br>
                        &nbsp;&nbsp;-H "X-API-Key: agm_your_api_key_here" \<br>
                        &nbsp;&nbsp;-H "Content-Type: application/json"
                    </code>
                </div>
            </div>
        </div>

        <!-- Step 5 -->
        <div class="flex flex-col md:flex-row gap-6">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-lg">5</span>
                </div>
            </div>
            <div class="flex-1">
                <h3 class="text-xl font-semibold text-secondary-900 mb-3">
                    Monitor Usage & Analytics
                </h3>
                <p class="text-secondary-600 mb-4">
                    Track your API usage, monitor performance, and analyze trends through the developer dashboard.
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div class="bg-secondary-50 rounded-lg p-4">
                        <h4 class="font-medium text-secondary-900 mb-2">Request Analytics</h4>
                        <p class="text-sm text-secondary-600">
                            View request counts, response times, and error rates
                        </p>
                    </div>
                    <div class="bg-secondary-50 rounded-lg p-4">
                        <h4 class="font-medium text-secondary-900 mb-2">Usage Trends</h4>
                        <p class="text-sm text-secondary-600">
                            Analyze usage patterns and plan for scaling
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Next Steps -->
    <div class="mt-16 bg-primary-50 rounded-lg p-8">
        <h2 class="text-2xl font-bold text-secondary-900 mb-4">
            Next Steps
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-secondary-900 mb-2">Explore Documentation</h3>
                <p class="text-secondary-600 text-sm mb-3">
                    Learn about advanced features, API reference, and best practices.
                </p>
                <a href="{{ route('docs.index') }}" class="btn-outline btn-sm">
                    View Docs
                </a>
            </div>
            <div>
                <h3 class="font-semibold text-secondary-900 mb-2">Need Help?</h3>
                <p class="text-secondary-600 text-sm mb-3">
                    Get support from our team or community.
                </p>
                <a href="{{ route('portal.support') }}" class="btn-outline btn-sm">
                    Get Support
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
