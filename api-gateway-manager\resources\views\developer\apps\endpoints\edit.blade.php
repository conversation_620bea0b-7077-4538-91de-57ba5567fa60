@extends('layouts.developer')

@php
    $pageTitle = 'Edit Endpoint';
@endphp

@section('title', 'Edit ' . $endpoint->name . ' - ' . $app->name . ' - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('developer.apps.endpoints.show', [$app, $endpoint]) }}" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                    Edit Endpoint
                </h1>
                <p class="text-secondary-600">{{ $app->name }} → {{ $endpoint->name }}</p>
            </div>
        </div>
        <p class="text-secondary-600">
            Update the configuration for this API endpoint.
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-4xl">
        <form method="POST" action="{{ route('developer.apps.endpoints.update', [$app, $endpoint]) }}" class="space-y-6" x-data="{ 
            methods: {{ json_encode($endpoint->allowed_methods) }},
            headersToAdd: {{ $endpoint->headers_to_add ? json_encode(array_map(fn($value, $key) => ['name' => $key, 'value' => $value], $endpoint->headers_to_add, array_keys($endpoint->headers_to_add))) : '[]' }},
            headersToRemove: {{ $endpoint->headers_to_remove ? json_encode($endpoint->headers_to_remove) : '[]' }}
        }">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Endpoint Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                            Endpoint Name <span class="text-danger-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required
                               value="{{ old('name', $endpoint->name) }}"
                               class="form-input @error('name') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">
                        @error('name')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                            Description
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="3"
                                  class="form-textarea @error('description') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">{{ old('description', $endpoint->description) }}</textarea>
                        @error('description')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Proxy Path -->
                    <div>
                        <label for="proxy_path" class="block text-sm font-medium text-secondary-700 mb-2">
                            Proxy Path <span class="text-danger-500">*</span>
                        </label>
                        <input type="text" 
                               id="proxy_path" 
                               name="proxy_path" 
                               required
                               value="{{ old('proxy_path', $endpoint->proxy_path) }}"
                               class="form-input @error('proxy_path') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">
                        @error('proxy_path')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Target URL -->
                    <div>
                        <label for="target_url" class="block text-sm font-medium text-secondary-700 mb-2">
                            Target URL <span class="text-danger-500">*</span>
                        </label>
                        <input type="text" 
                               id="target_url" 
                               name="target_url" 
                               required
                               value="{{ old('target_url', $endpoint->target_url) }}"
                               class="form-input @error('target_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">
                        @error('target_url')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Allowed Methods -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Allowed Methods <span class="text-danger-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 gap-2">
                            @foreach(['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'] as $method)
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="allowed_methods[]" 
                                           value="{{ $method }}"
                                           x-model="methods"
                                           class="form-checkbox">
                                    <span class="ml-2 text-sm text-secondary-700">{{ $method }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('allowed_methods')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Authentication Required -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="requires_auth" 
                                   name="requires_auth" 
                                   value="1"
                                   {{ old('requires_auth', $endpoint->requires_auth) ? 'checked' : '' }}
                                   class="form-checkbox">
                            <label for="requires_auth" class="ml-2 block text-sm text-secondary-700">
                                Require API Key Authentication
                            </label>
                        </div>
                    </div>

                    <!-- Active Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1"
                                   {{ old('is_active', $endpoint->is_active) ? 'checked' : '' }}
                                   class="form-checkbox">
                            <label for="is_active" class="ml-2 block text-sm text-secondary-700">
                                Active Endpoint
                            </label>
                        </div>
                    </div>

                    <!-- Timeout -->
                    <div>
                        <label for="timeout" class="block text-sm font-medium text-secondary-700 mb-2">
                            Timeout (seconds)
                        </label>
                        <input type="number" 
                               id="timeout" 
                               name="timeout" 
                               min="1" 
                               max="300"
                               value="{{ old('timeout', $endpoint->timeout) }}"
                               class="form-input @error('timeout') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror">
                        @error('timeout')
                            <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Headers Configuration -->
            <div class="border-t border-secondary-200 pt-6">
                <h3 class="text-lg font-medium text-secondary-900 mb-4">Header Configuration</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Headers to Add -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Additional Headers to Add
                        </label>
                        <div class="space-y-3">
                            <template x-for="(header, index) in headersToAdd" :key="index">
                                <div class="flex gap-3 items-start">
                                    <div class="flex-1">
                                        <input type="text" 
                                               :name="`headers_to_add[${index}][name]`"
                                               x-model="header.name"
                                               class="form-input"
                                               placeholder="Header name">
                                    </div>
                                    <div class="flex-1">
                                        <input type="text" 
                                               :name="`headers_to_add[${index}][value]`"
                                               x-model="header.value"
                                               class="form-input"
                                               placeholder="Header value">
                                    </div>
                                    <button type="button" 
                                            @click="headersToAdd.splice(index, 1)"
                                            class="btn-sm btn-outline text-danger-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                            <button type="button" 
                                    @click="headersToAdd.push({ name: '', value: '' })"
                                    class="btn-sm btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Header
                            </button>
                        </div>
                    </div>

                    <!-- Headers to Remove -->
                    <div>
                        <label class="block text-sm font-medium text-secondary-700 mb-2">
                            Headers to Remove
                        </label>
                        <div class="space-y-3">
                            <template x-for="(header, index) in headersToRemove" :key="index">
                                <div class="flex gap-3 items-center">
                                    <input type="text" 
                                           :name="`headers_to_remove[${index}]`"
                                           x-model="headersToRemove[index]"
                                           class="form-input flex-1"
                                           placeholder="Header name to remove">
                                    <button type="button" 
                                            @click="headersToRemove.splice(index, 1)"
                                            class="btn-sm btn-outline text-danger-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </template>
                            <button type="button" 
                                    @click="headersToRemove.push('')"
                                    class="btn-sm btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Header to Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-secondary-200">
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Update Endpoint
                </button>
                <a href="{{ route('developer.apps.endpoints.show', [$app, $endpoint]) }}" class="btn-outline">
                    Cancel
                </a>
                <form action="{{ route('developer.apps.endpoints.destroy', [$app, $endpoint]) }}" method="POST" class="ml-auto">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn-danger" onclick="return confirm('Are you sure you want to delete this endpoint?')">
                        Delete Endpoint
                    </button>
                </form>
            </div>
        </form>
    </div>
</div>
@endsection
