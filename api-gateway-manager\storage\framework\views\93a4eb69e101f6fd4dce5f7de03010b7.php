<?php
    $pageTitle = 'Request Logs';
?>

<?php $__env->startSection('title', 'Request Logs - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Request Logs</h1>
                <p class="mt-2 text-secondary-600">Detailed view of all API requests across your applications</p>
            </div>
            <a href="<?php echo e(route('developer.analytics.index')); ?>" class="btn-outline">
                Back to Analytics
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-8">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('developer.analytics.logs')); ?>" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="form-group">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-input" 
                           value="<?php echo e($search); ?>" placeholder="Path or IP address">
                </div>

                <div class="form-group">
                    <label for="app_id" class="form-label">Application</label>
                    <select name="app_id" id="app_id" class="form-input">
                        <option value="">All Applications</option>
                        <?php $__currentLoopData = $apps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $app): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($app->id); ?>" <?php echo e($app_id == $app->id ? 'selected' : ''); ?>>
                                <?php echo e($app->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="method" class="form-label">Method</label>
                    <select name="method" id="method" class="form-input">
                        <option value="">All Methods</option>
                        <option value="GET" <?php echo e($method === 'GET' ? 'selected' : ''); ?>>GET</option>
                        <option value="POST" <?php echo e($method === 'POST' ? 'selected' : ''); ?>>POST</option>
                        <option value="PUT" <?php echo e($method === 'PUT' ? 'selected' : ''); ?>>PUT</option>
                        <option value="DELETE" <?php echo e($method === 'DELETE' ? 'selected' : ''); ?>>DELETE</option>
                        <option value="PATCH" <?php echo e($method === 'PATCH' ? 'selected' : ''); ?>>PATCH</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-input">
                        <option value="">All Status</option>
                        <option value="success" <?php echo e($status === 'success' ? 'selected' : ''); ?>>Success (2xx)</option>
                        <option value="error" <?php echo e($status === 'error' ? 'selected' : ''); ?>>Error (4xx-5xx)</option>
                    </select>
                </div>

                <div class="form-group flex items-end">
                    <button type="submit" class="btn-primary w-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <?php if($logs->count() > 0): ?>
        <!-- Results Summary -->
        <div class="mb-6">
            <p class="text-sm text-secondary-600">
                Showing <?php echo e($logs->firstItem()); ?> to <?php echo e($logs->lastItem()); ?> of <?php echo e($logs->total()); ?> results
            </p>
        </div>

        <!-- Logs Table -->
        <div class="card">
            <div class="card-body p-0">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Method</th>
                                <th>Path</th>
                                <th>Application</th>
                                <th>API Key</th>
                                <th>Status</th>
                                <th>Response Time</th>
                                <th>IP Address</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="hover:bg-secondary-50">
                                    <td class="table-cell">
                                        <div class="text-sm text-secondary-900">
                                            <?php echo e($log->requested_at->format('M j, H:i:s')); ?>

                                        </div>
                                        <div class="text-xs text-secondary-500">
                                            <?php echo e($log->requested_at->diffForHumans()); ?>

                                        </div>
                                    </td>
                                    <td class="table-cell">
                                        <span class="badge-<?php echo e($log->method === 'GET' ? 'success' : ($log->method === 'POST' ? 'primary' : ($log->method === 'DELETE' ? 'danger' : 'warning'))); ?> text-xs">
                                            <?php echo e($log->method); ?>

                                        </span>
                                    </td>
                                    <td class="table-cell">
                                        <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($log->path); ?></code>
                                    </td>
                                    <td class="table-cell">
                                        <?php if($log->apiKey && $log->apiKey->app): ?>
                                            <div class="flex items-center">
                                                <div class="h-6 w-6 bg-primary-100 rounded flex items-center justify-center mr-2">
                                                    <svg class="h-3 w-3 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                    </svg>
                                                </div>
                                                <span class="text-sm text-secondary-900"><?php echo e($log->apiKey->app->name); ?></span>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-sm text-secondary-500">Unknown</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="table-cell">
                                        <?php if($log->apiKey): ?>
                                            <div class="text-sm text-secondary-900"><?php echo e($log->apiKey->name); ?></div>
                                            <code class="text-xs text-secondary-500"><?php echo e($log->apiKey->key_prefix); ?>...</code>
                                        <?php else: ?>
                                            <span class="text-sm text-secondary-500">Unknown</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="table-cell">
                                        <span class="badge-<?php echo e($log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning')); ?> text-xs">
                                            <?php echo e($log->response_status); ?>

                                        </span>
                                    </td>
                                    <td class="table-cell">
                                        <span class="text-sm <?php echo e($log->response_time_ms > 1000 ? 'text-danger-600' : ($log->response_time_ms > 500 ? 'text-warning-600' : 'text-success-600')); ?>">
                                            <?php echo e($log->response_time_ms); ?>ms
                                        </span>
                                    </td>
                                    <td class="table-cell">
                                        <span class="text-sm text-secondary-600 font-mono"><?php echo e($log->ip_address); ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            <?php echo e($logs->appends(request()->query())->links()); ?>

        </div>
    <?php else: ?>
        <!-- Empty State -->
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-secondary-900">No request logs found</h3>
            <p class="mt-1 text-sm text-secondary-500">
                <?php if($search || $status || $method || $app_id): ?>
                    Try adjusting your filters to see more results.
                <?php else: ?>
                    Start making API requests to see logs here.
                <?php endif; ?>
            </p>
            <?php if($search || $status || $method || $app_id): ?>
                <div class="mt-6">
                    <a href="<?php echo e(route('developer.analytics.logs')); ?>" class="btn-outline">
                        Clear Filters
                    </a>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/analytics/logs.blade.php ENDPATH**/ ?>