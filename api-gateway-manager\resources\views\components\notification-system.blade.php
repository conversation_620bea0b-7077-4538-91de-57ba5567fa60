@props([
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center
    'maxNotifications' => 5,
    'autoRemove' => true,
    'removeDelay' => 5000
])

@php
    $positionClasses = [
        'top-right' => 'top-4 right-4',
        'top-left' => 'top-4 left-4',
        'bottom-right' => 'bottom-4 right-4',
        'bottom-left' => 'bottom-4 left-4',
        'top-center' => 'top-4 left-1/2 transform -translate-x-1/2',
        'bottom-center' => 'bottom-4 left-1/2 transform -translate-x-1/2'
    ];
    
    $containerClass = $positionClasses[$position] ?? $positionClasses['top-right'];
@endphp

<!-- Notification Container -->
<div id="notification-container" 
     class="fixed {{ $containerClass }} z-50 space-y-3 pointer-events-none"
     x-data="notificationSystem()"
     x-init="init()">
    
    <template x-for="notification in notifications" :key="notification.id">
        <div class="notification-item pointer-events-auto"
             :class="getNotificationClasses(notification)"
             x-show="notification.visible"
             x-transition:enter="transform ease-out duration-300 transition"
             x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
             x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
             x-transition:leave="transition ease-in duration-100"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            
            <div class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div x-html="getNotificationIcon(notification)"></div>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium text-secondary-900" x-text="notification.title"></p>
                            <p class="mt-1 text-sm text-secondary-500" x-text="notification.message"></p>
                            
                            <!-- Action Buttons -->
                            <div x-show="notification.actions && notification.actions.length > 0" class="mt-3 flex space-x-2">
                                <template x-for="action in notification.actions" :key="action.label">
                                    <button @click="handleAction(notification, action)"
                                            :class="action.style === 'primary' ? 'btn-primary btn-sm' : 'btn-outline btn-sm'"
                                            x-text="action.label">
                                    </button>
                                </template>
                            </div>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="removeNotification(notification.id)"
                                    class="bg-white rounded-md inline-flex text-secondary-400 hover:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Bar for Auto-Remove -->
                <div x-show="notification.autoRemove && notification.progress !== undefined" 
                     class="h-1 bg-secondary-200">
                    <div class="h-full transition-all duration-100 ease-linear"
                         :class="getProgressBarClass(notification)"
                         :style="`width: ${notification.progress}%`">
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<!-- Toast Notification Styles -->
@push('styles')
<style>
.notification-item {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile optimizations */
@media (max-width: 640px) {
    #notification-container {
        left: 1rem !important;
        right: 1rem !important;
        transform: none !important;
    }
    
    .notification-item {
        max-width: none !important;
        width: 100% !important;
    }
}

/* Notification types */
.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

/* Progress bars */
.progress-success {
    background-color: #10b981;
}

.progress-error {
    background-color: #ef4444;
}

.progress-warning {
    background-color: #f59e0b;
}

.progress-info {
    background-color: #3b82f6;
}
</style>
@endpush

@push('scripts')
<script>
function notificationSystem() {
    return {
        notifications: [],
        maxNotifications: {{ $maxNotifications }},
        autoRemove: {{ $autoRemove ? 'true' : 'false' }},
        removeDelay: {{ $removeDelay }},
        
        init() {
            // Listen for custom notification events
            window.addEventListener('show-notification', (event) => {
                this.addNotification(event.detail);
            });
            
            // Listen for Laravel flash messages
            this.checkFlashMessages();
        },
        
        addNotification(notification) {
            const id = Date.now() + Math.random();
            const newNotification = {
                id: id,
                type: notification.type || 'info',
                title: notification.title || this.getDefaultTitle(notification.type),
                message: notification.message || '',
                actions: notification.actions || [],
                autoRemove: notification.autoRemove !== undefined ? notification.autoRemove : this.autoRemove,
                visible: true,
                progress: 100
            };
            
            // Remove oldest notification if at max capacity
            if (this.notifications.length >= this.maxNotifications) {
                this.notifications.shift();
            }
            
            this.notifications.push(newNotification);
            
            // Auto-remove if enabled
            if (newNotification.autoRemove) {
                this.startAutoRemove(newNotification);
            }
        },
        
        removeNotification(id) {
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications[index].visible = false;
                setTimeout(() => {
                    this.notifications.splice(index, 1);
                }, 300);
            }
        },
        
        startAutoRemove(notification) {
            const interval = 100; // Update every 100ms
            const steps = this.removeDelay / interval;
            const progressStep = 100 / steps;
            
            const timer = setInterval(() => {
                notification.progress -= progressStep;
                
                if (notification.progress <= 0) {
                    clearInterval(timer);
                    this.removeNotification(notification.id);
                }
            }, interval);
            
            // Store timer reference for potential cancellation
            notification.timer = timer;
        },
        
        handleAction(notification, action) {
            if (action.callback) {
                action.callback(notification);
            }
            
            if (action.url) {
                window.location.href = action.url;
            }
            
            if (action.removeOnClick !== false) {
                this.removeNotification(notification.id);
            }
        },
        
        getNotificationClasses(notification) {
            return `notification-${notification.type}`;
        },
        
        getNotificationIcon(notification) {
            const icons = {
                success: '<svg class="h-6 w-6 text-success-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
                error: '<svg class="h-6 w-6 text-danger-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
                warning: '<svg class="h-6 w-6 text-warning-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>',
                info: '<svg class="h-6 w-6 text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>'
            };
            
            return icons[notification.type] || icons.info;
        },
        
        getProgressBarClass(notification) {
            return `progress-${notification.type}`;
        },
        
        getDefaultTitle(type) {
            const titles = {
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Information'
            };
            
            return titles[type] || 'Notification';
        },
        
        checkFlashMessages() {
            // Check for Laravel flash messages
            @if(session('success'))
                this.addNotification({
                    type: 'success',
                    message: '{{ session("success") }}'
                });
            @endif
            
            @if(session('error'))
                this.addNotification({
                    type: 'error',
                    message: '{{ session("error") }}'
                });
            @endif
            
            @if(session('warning'))
                this.addNotification({
                    type: 'warning',
                    message: '{{ session("warning") }}'
                });
            @endif
            
            @if(session('info'))
                this.addNotification({
                    type: 'info',
                    message: '{{ session("info") }}'
                });
            @endif
        }
    }
}

// Global notification helper functions
window.showNotification = function(type, message, title = null, options = {}) {
    window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
            type: type,
            title: title,
            message: message,
            ...options
        }
    }));
};

window.showSuccess = function(message, title = null, options = {}) {
    window.showNotification('success', message, title, options);
};

window.showError = function(message, title = null, options = {}) {
    window.showNotification('error', message, title, options);
};

window.showWarning = function(message, title = null, options = {}) {
    window.showNotification('warning', message, title, options);
};

window.showInfo = function(message, title = null, options = {}) {
    window.showNotification('info', message, title, options);
};

// AJAX error handler
document.addEventListener('DOMContentLoaded', function() {
    // Intercept AJAX errors
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response;
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    window.showError('Network error occurred. Please try again.', 'Connection Error');
                }
                throw error;
            });
    };
});
</script>
@endpush
