<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RequestLog;
use App\Models\ApiProxy;
use App\Models\ApiKey;

class RequestLogController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = RequestLog::with(['apiKey.app.user', 'apiProxy']);

        // Apply filters
        if ($request->filled('api_proxy_id')) {
            $query->where('api_proxy_id', $request->api_proxy_id);
        }

        if ($request->filled('api_key_id')) {
            $query->where('api_key_id', $request->api_key_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'success') {
                $query->where('response_status', '<', 400);
            } elseif ($request->status === 'error') {
                $query->where('response_status', '>=', 400);
            }
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('ip_address')) {
            $query->where('ip_address', 'like', '%' . $request->ip_address . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->date_to . ' 23:59:59');
        }

        $logs = $query->orderBy('requested_at', 'desc')->paginate(50);

        // Get filter options
        $apiProxies = ApiProxy::all();
        $apiKeys = ApiKey::with('app')->get();

        return view('admin.request-logs.index', compact('logs', 'apiProxies', 'apiKeys'));
    }

    /**
     * Display the specified resource.
     */
    public function show(RequestLog $requestLog)
    {
        $requestLog->load(['apiKey.app.user', 'apiProxy']);
        return view('admin.request-logs.show', compact('requestLog'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RequestLog $requestLog)
    {
        $requestLog->delete();

        return redirect()->route('admin.request-logs.index')
            ->with('success', 'Request log deleted successfully!');
    }

    /**
     * Bulk delete old logs
     */
    public function cleanup(Request $request)
    {
        $request->validate([
            'days' => 'required|integer|min:1|max:365',
        ]);

        $cutoffDate = now()->subDays($request->days);
        $deletedCount = RequestLog::where('requested_at', '<', $cutoffDate)->delete();

        return redirect()->route('admin.request-logs.index')
            ->with('success', "Deleted {$deletedCount} old request logs.");
    }

    /**
     * Export logs as CSV
     */
    public function export(Request $request)
    {
        $query = RequestLog::with(['apiKey.app.user', 'apiProxy']);

        // Apply same filters as index
        if ($request->filled('api_proxy_id')) {
            $query->where('api_proxy_id', $request->api_proxy_id);
        }

        if ($request->filled('api_key_id')) {
            $query->where('api_key_id', $request->api_key_id);
        }

        if ($request->filled('status')) {
            if ($request->status === 'success') {
                $query->where('response_status', '<', 400);
            } elseif ($request->status === 'error') {
                $query->where('response_status', '>=', 400);
            }
        }

        if ($request->filled('date_from')) {
            $query->where('requested_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('requested_at', '<=', $request->date_to . ' 23:59:59');
        }

        $logs = $query->orderBy('requested_at', 'desc')->limit(10000)->get();

        $filename = 'request_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($logs) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Date/Time',
                'API Proxy',
                'Method',
                'Path',
                'IP Address',
                'API Key',
                'User',
                'Status',
                'Response Time (ms)',
                'Error Message'
            ]);

            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->requested_at->format('Y-m-d H:i:s'),
                    $log->apiProxy->name ?? 'N/A',
                    $log->method,
                    $log->path,
                    $log->ip_address,
                    $log->apiKey->name ?? 'N/A',
                    $log->apiKey->app->user->name ?? 'N/A',
                    $log->response_status,
                    $log->response_time_ms,
                    $log->error_message ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
