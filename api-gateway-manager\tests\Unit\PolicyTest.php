<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Policies\AppPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_app_policy_view_any_allows_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $policy = new AppPolicy();

        $this->assertTrue($policy->viewAny($user));
    }

    public function test_app_policy_view_allows_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->view($user, $app));
    }

    public function test_app_policy_view_denies_non_owner()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);
        $policy = new AppPolicy();

        $this->assertFalse($policy->view($user1, $app));
    }

    public function test_app_policy_view_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->view($admin, $app));
    }

    public function test_app_policy_create_allows_developer()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $policy = new AppPolicy();

        $this->assertTrue($policy->create($user));
    }

    public function test_app_policy_create_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $policy = new AppPolicy();

        $this->assertTrue($policy->create($admin));
    }

    public function test_app_policy_create_denies_null_user()
    {
        $policy = new AppPolicy();

        $this->assertFalse($policy->create(null));
    }

    public function test_app_policy_update_allows_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->update($user, $app));
    }

    public function test_app_policy_update_denies_non_owner()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);
        $policy = new AppPolicy();

        $this->assertFalse($policy->update($user1, $app));
    }

    public function test_app_policy_update_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->update($admin, $app));
    }

    public function test_app_policy_delete_allows_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->delete($user, $app));
    }

    public function test_app_policy_delete_denies_non_owner()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);
        $policy = new AppPolicy();

        $this->assertFalse($policy->delete($user1, $app));
    }

    public function test_app_policy_delete_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->delete($admin, $app));
    }

    public function test_app_policy_restore_denies_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertFalse($policy->restore($user, $app));
    }

    public function test_app_policy_restore_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->restore($admin, $app));
    }

    public function test_app_policy_force_delete_denies_owner()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertFalse($policy->forceDelete($user, $app));
    }

    public function test_app_policy_force_delete_allows_admin()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        $this->assertTrue($policy->forceDelete($admin, $app));
    }

    public function test_app_policy_handles_null_user()
    {
        $app = App::factory()->create();
        $policy = new AppPolicy();

        $this->assertFalse($policy->view(null, $app));
        $this->assertFalse($policy->create(null));
        $this->assertFalse($policy->update(null, $app));
        $this->assertFalse($policy->delete(null, $app));
    }

    public function test_app_policy_handles_inactive_user()
    {
        $user = User::factory()->create(['role' => 'developer', 'is_active' => false]);
        $app = App::factory()->create(['user_id' => $user->id]);
        $policy = new AppPolicy();

        // Inactive users should still be able to access their own apps
        $this->assertTrue($policy->view($user, $app));
        $this->assertTrue($policy->update($user, $app));
    }

    public function test_app_policy_with_different_user_roles()
    {
        $developer = User::factory()->create(['role' => 'developer']);
        $admin = User::factory()->create(['role' => 'admin']);
        $otherDeveloper = User::factory()->create(['role' => 'developer']);

        $app = App::factory()->create(['user_id' => $developer->id]);
        $policy = new AppPolicy();

        // Developer (owner) should have full access
        $this->assertTrue($policy->view($developer, $app));
        $this->assertTrue($policy->update($developer, $app));
        $this->assertTrue($policy->delete($developer, $app));

        // Admin should have full access to any app
        $this->assertTrue($policy->view($admin, $app));
        $this->assertTrue($policy->update($admin, $app));
        $this->assertTrue($policy->delete($admin, $app));

        // Other developer should have no access to this app
        $this->assertFalse($policy->view($otherDeveloper, $app));
        $this->assertFalse($policy->update($otherDeveloper, $app));
        $this->assertFalse($policy->delete($otherDeveloper, $app));
    }
}
