<?php
    $pageTitle = 'App Details';
?>

<?php $__env->startSection('title', $app->name . ' - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl"><?php echo e($app->name); ?></h1>
                <p class="mt-2 text-secondary-600"><?php echo e($app->description); ?></p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('developer.apps.edit', $app)); ?>" class="btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit App
                </a>
                <a href="<?php echo e(route('developer.apps.index')); ?>" class="btn-outline">
                    Back to Apps
                </a>
            </div>
        </div>
    </div>

    <!-- App Status Banner -->
    <?php if(!$app->is_active): ?>
        <div class="bg-warning-50 border border-warning-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <svg class="h-5 w-5 text-warning-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                    <h3 class="text-sm font-medium text-warning-800">App Inactive</h3>
                    <p class="mt-1 text-sm text-warning-700">
                        This app is currently inactive. API requests will be rejected until you activate it.
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Status -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-<?php echo e($app->is_active ? 'success' : 'secondary'); ?>-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-<?php echo e($app->is_active ? 'success' : 'secondary'); ?>-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo e($app->is_active ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'); ?>" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Status</p>
                        <p class="text-lg font-semibold text-secondary-900"><?php echo e($app->is_active ? 'Active' : 'Inactive'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Keys -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">API Keys</p>
                        <p class="text-lg font-semibold text-secondary-900"><?php echo e($app->apiKeys->count()); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Requests -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Total Requests</p>
                        <p class="text-lg font-semibold text-secondary-900">
                            <?php echo e(number_format($app->apiKeys->sum(function($key) { return $key->requestLogs->count(); }))); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Rate -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-secondary-600">Success Rate</p>
                        <p class="text-lg font-semibold text-secondary-900">
                            <?php
                                $totalRequests = $app->apiKeys->sum(function($key) { return $key->requestLogs->count(); });
                                $successfulRequests = $app->apiKeys->sum(function($key) { 
                                    return $key->requestLogs->whereBetween('response_status', [200, 299])->count(); 
                                });
                                $successRate = $totalRequests > 0 ? round(($successfulRequests / $totalRequests) * 100, 1) : 100;
                            ?>
                            <?php echo e($successRate); ?>%
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- App Details -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">App Information</h2>
            </div>
            <div class="card-body">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Name</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->name); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Description</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->description ?: 'No description provided'); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Status</dt>
                        <dd class="mt-1">
                            <span class="badge-<?php echo e($app->is_active ? 'success' : 'secondary'); ?>">
                                <?php echo e($app->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Created</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->created_at->format('F j, Y \a\t g:i A')); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Last Updated</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($app->updated_at->format('F j, Y \a\t g:i A')); ?></dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">Quick Actions</h2>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <a href="<?php echo e(route('developer.api-keys.create')); ?>?app_id=<?php echo e($app->id); ?>" class="flex items-center p-3 bg-primary-50 rounded-lg hover:bg-primary-100 transition-colors">
                        <div class="h-8 w-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">Create API Key</div>
                            <div class="text-sm text-secondary-600">Generate a new authentication key</div>
                        </div>
                    </a>

                    <a href="<?php echo e(route('developer.analytics.app', $app)); ?>" class="flex items-center p-3 bg-success-50 rounded-lg hover:bg-success-100 transition-colors">
                        <div class="h-8 w-8 bg-success-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">View Analytics</div>
                            <div class="text-sm text-secondary-600">See detailed usage statistics</div>
                        </div>
                    </a>

                    <a href="<?php echo e(route('developer.apps.edit', $app)); ?>" class="flex items-center p-3 bg-warning-50 rounded-lg hover:bg-warning-100 transition-colors">
                        <div class="h-8 w-8 bg-warning-100 rounded-lg flex items-center justify-center mr-3">
                            <svg class="h-4 w-4 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-secondary-900">Edit App</div>
                            <div class="text-sm text-secondary-600">Update app settings</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- API Keys Section -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-secondary-900">API Keys</h2>
                    <a href="<?php echo e(route('developer.api-keys.create')); ?>?app_id=<?php echo e($app->id); ?>" class="btn-sm btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create Key
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if($app->apiKeys->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Key Prefix</th>
                                    <th>Status</th>
                                    <th>Expires</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $app->apiKeys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $apiKey): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="table-cell">
                                            <div class="font-medium text-secondary-900"><?php echo e($apiKey->name); ?></div>
                                        </td>
                                        <td class="table-cell">
                                            <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($apiKey->key_prefix); ?>...</code>
                                        </td>
                                        <td class="table-cell">
                                            <?php if($apiKey->is_active && (!$apiKey->expires_at || $apiKey->expires_at->isFuture())): ?>
                                                <span class="badge-success">Active</span>
                                            <?php elseif($apiKey->expires_at && $apiKey->expires_at->isPast()): ?>
                                                <span class="badge-warning">Expired</span>
                                            <?php else: ?>
                                                <span class="badge-secondary">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="table-cell">
                                            <?php if($apiKey->expires_at): ?>
                                                <span class="text-sm text-secondary-600"><?php echo e($apiKey->expires_at->format('M j, Y')); ?></span>
                                            <?php else: ?>
                                                <span class="text-sm text-secondary-500">Never</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm text-secondary-600"><?php echo e($apiKey->created_at->format('M j, Y')); ?></span>
                                        </td>
                                        <td class="table-cell">
                                            <div class="flex items-center space-x-2">
                                                <a href="<?php echo e(route('developer.api-keys.show', $apiKey)); ?>" class="btn-sm btn-outline">
                                                    View
                                                </a>
                                                <a href="<?php echo e(route('developer.api-keys.edit', $apiKey)); ?>" class="btn-sm btn-secondary">
                                                    Edit
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-secondary-900">No API keys</h3>
                        <p class="mt-1 text-sm text-secondary-500">Get started by creating your first API key for this app.</p>
                        <div class="mt-6">
                            <a href="<?php echo e(route('developer.api-keys.create')); ?>?app_id=<?php echo e($app->id); ?>" class="btn-primary">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Create API Key
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/apps/show.blade.php ENDPATH**/ ?>