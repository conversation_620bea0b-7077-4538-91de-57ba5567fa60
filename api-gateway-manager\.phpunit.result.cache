{"version": 1, "defects": {"Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_app": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_view_their_apps": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_cannot_view_other_users_apps": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_api_key": 7, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_generation_creates_proper_hash": 8, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_regenerate_api_key": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_deactivate_api_key": 7, "Tests\\Feature\\ApiProxyTest::test_admin_can_create_api_proxy": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_requires_authentication_when_configured": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_allows_unauthenticated_access_when_configured": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_validates_http_methods": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_with_valid_api_key": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_logs_requests": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_handles_backend_errors": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_method_validation": 7, "Tests\\Feature\\AuthenticationTest::test_developer_middleware_allows_admin_users": 7, "Tests\\Feature\\AuthenticationTest::test_inactive_user_cannot_access_protected_routes": 7, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 7, "Tests\\Feature\\RateLimitingTest::test_admin_can_create_custom_rate_limit": 7, "Tests\\Feature\\RateLimitingTest::test_admin_can_reset_rate_limit_counters": 7, "Tests\\Feature\\RateLimitingTest::test_rate_limit_prevents_duplicate_creation": 7, "Tests\\Feature\\RateLimitingTest::test_rate_limit_validation": 7, "Tests\\Unit\\ApiProxyTest::test_api_proxy_factory_creates_valid_proxy": 7, "Tests\\Unit\\ApiProxyTest::test_api_proxy_factory_can_create_no_auth_proxy": 8, "Tests\\Unit\\AppTest::test_app_scope_active": 8, "Tests\\Unit\\AppTest::test_app_scope_inactive": 8, "Tests\\Unit\\MiddlewareTest::test_admin_middleware_blocks_non_admin_users": 7, "Tests\\Unit\\MiddlewareTest::test_admin_middleware_redirects_guests": 8, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_blocks_regular_users": 8, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_redirects_guests": 8, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_invalid_key": 8, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_missing_key": 8, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_inactive_key": 8, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_expired_key": 8, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_inactive_app": 7, "Tests\\Unit\\PolicyTest::test_app_policy_create_denies_regular_user": 8, "Tests\\Unit\\PolicyTest::test_app_policy_restore_allows_owner": 7, "Tests\\Unit\\PolicyTest::test_app_policy_force_delete_allows_owner": 7, "Tests\\Unit\\PolicyTest::test_app_policy_handles_null_user": 8, "Tests\\Unit\\PolicyTest::test_app_policy_with_different_user_roles": 8, "Tests\\Unit\\UserTest::test_user_has_default_role_developer": 7, "Tests\\Unit\\UserTest::test_user_factory_creates_valid_user": 7, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_correct_statistics": 8, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_activity": 7, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_users": 7, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_calculates_success_rate": 8, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_handles_no_data": 8, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_system_health": 7, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_top_apps": 7, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_error_rate_trends": 7, "Tests\\Feature\\ApiDocumentationTest::test_can_view_specific_api_proxy_documentation": 7, "Tests\\Feature\\ApiDocumentationTest::test_documentation_index_shows_no_apis_message_when_empty": 7, "Tests\\Feature\\ApiDocumentationTest::test_api_proxy_documentation_shows_all_details": 7, "Tests\\Feature\\DeveloperAnalyticsTest::test_analytics_index_shows_correct_data": 7, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_can_view_analytics_data_json": 7, "Tests\\Feature\\DeveloperAnalyticsTest::test_analytics_excludes_other_users_data": 7, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_cannot_create_api_key_for_other_users_app": 7, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_creation_validates_app_ownership": 7, "Tests\\Feature\\DeveloperAppTest::test_app_index_handles_empty_state": 7, "Tests\\Feature\\DeveloperDashboardTest::test_non_developer_cannot_access_dashboard": 8, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_shows_correct_statistics": 8, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_handles_user_with_no_data": 8, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_excludes_other_users_data": 8, "Tests\\Feature\\PortalTest::test_can_view_home_page": 7, "Tests\\Feature\\PortalTest::test_can_view_pricing_page": 7, "Tests\\Feature\\PortalTest::test_home_page_shows_statistics": 8, "Tests\\Feature\\PortalTest::test_home_page_shows_featured_apis": 7, "Tests\\Feature\\PortalTest::test_support_page_has_contact_form": 7, "Tests\\Feature\\PortalTest::test_can_submit_support_form": 7, "Tests\\Feature\\PortalTest::test_support_form_validates_required_fields": 7, "Tests\\Feature\\PortalTest::test_support_form_validates_email_format": 7, "Tests\\Feature\\PortalTest::test_home_page_handles_no_data": 8, "Tests\\Feature\\PortalTest::test_home_page_shows_recent_activity": 7, "Tests\\Feature\\PortalTest::test_home_page_shows_success_metrics": 7, "Tests\\Feature\\PortalTest::test_home_page_shows_average_response_time": 7, "Tests\\Feature\\PortalTest::test_pricing_page_shows_feature_comparison": 7, "Tests\\Feature\\PortalTest::test_support_page_shows_faq": 7, "Tests\\Feature\\PortalTest::test_home_page_performance_with_large_dataset": 8, "Tests\\Unit\\ApiKeyTest::test_api_key_has_many_request_logs": 8, "Tests\\Unit\\PolicyTest::test_app_policy_create_denies_null_user": 8, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_includes_authentication_when_required": 8, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_performance": 8, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_calculates_success_rate_correctly": 8, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_create_endpoint_for_app": 7, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_view_endpoint": 7, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_update_endpoint": 7, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_delete_endpoint": 7, "Tests\\Feature\\AppEndpointManagementTest::test_relative_target_url_is_combined_with_base_url": 8}, "times": {"Tests\\Unit\\ApiKeyTest::test_api_key_generation": 0.019, "Tests\\Unit\\ApiKeyTest::test_api_key_hashing": 0, "Tests\\Unit\\ApiKeyTest::test_api_key_prefix_extraction": 0.001, "Tests\\Unit\\ApiKeyTest::test_api_key_expiry_detection": 0.002, "Tests\\Unit\\ApiKeyTest::test_api_key_usage_marking": 0.003, "Tests\\Unit\\ApiKeyTest::test_api_key_belongs_to_app": 0.062, "Tests\\Unit\\ApiKeyTest::test_api_key_has_rate_limits_relationship": 0.108, "Tests\\Unit\\ApiKeyTest::test_api_key_has_request_logs_relationship": 0.036, "Tests\\Unit\\ApiKeyTest::test_api_key_casts": 0.081, "Tests\\Unit\\ApiKeyTest::test_api_key_fillable_attributes": 0.002, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_app": 0.015, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_view_their_apps": 0.039, "Tests\\Feature\\ApiKeyManagementTest::test_developer_cannot_view_other_users_apps": 0.009, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_api_key": 0.007, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_generation_creates_proper_hash": 0.009, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_regenerate_api_key": 0.006, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_deactivate_api_key": 0.005, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_validation_methods": 0, "Tests\\Feature\\ApiKeyManagementTest::test_expired_api_key_detection": 0.002, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_usage_tracking": 0.002, "Tests\\Feature\\ApiProxyTest::test_admin_can_create_api_proxy": 0.005, "Tests\\Feature\\ApiProxyTest::test_developer_cannot_create_api_proxy": 0.01, "Tests\\Feature\\ApiProxyTest::test_api_proxy_requires_authentication_when_configured": 0.003, "Tests\\Feature\\ApiProxyTest::test_api_proxy_allows_unauthenticated_access_when_configured": 0.003, "Tests\\Feature\\ApiProxyTest::test_api_proxy_validates_http_methods": 0.003, "Tests\\Feature\\ApiProxyTest::test_api_proxy_with_valid_api_key": 0.003, "Tests\\Feature\\ApiProxyTest::test_api_proxy_logs_requests": 0.005, "Tests\\Feature\\ApiProxyTest::test_api_proxy_returns_404_for_unknown_path": 0.002, "Tests\\Feature\\ApiProxyTest::test_api_proxy_handles_backend_errors": 0.004, "Tests\\Feature\\ApiProxyTest::test_api_proxy_method_validation": 0.002, "Tests\\Feature\\ApiProxyTest::test_api_proxy_target_url_generation": 0.002, "Tests\\Feature\\ApiProxyTest::test_api_proxy_scope_active": 0.005, "Tests\\Feature\\ApiProxyTest::test_api_proxy_find_by_path": 0.002, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 0.01, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.036, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.24, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 0.007, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.012, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.018, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 0.006, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.016, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.004, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.221, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.013, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.213, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.245, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.239, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 0.006, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 0.009, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 0.007, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.01, "Tests\\Feature\\AuthenticationTest::test_user_can_register": 0.004, "Tests\\Feature\\AuthenticationTest::test_user_can_login": 0.008, "Tests\\Feature\\AuthenticationTest::test_admin_user_redirects_to_admin_dashboard": 0.005, "Tests\\Feature\\AuthenticationTest::test_developer_user_redirects_to_developer_dashboard": 0.004, "Tests\\Feature\\AuthenticationTest::test_admin_middleware_blocks_non_admin_users": 0.008, "Tests\\Feature\\AuthenticationTest::test_developer_middleware_allows_admin_users": 0.014, "Tests\\Feature\\AuthenticationTest::test_unauthenticated_user_redirected_to_login": 0.004, "Tests\\Feature\\AuthenticationTest::test_inactive_user_cannot_access_protected_routes": 0.014, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.036, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 0.037, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 0.013, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 0.005, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 0.009, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 0.005, "Tests\\Feature\\RateLimitingTest::test_rate_limit_creation": 0.014, "Tests\\Feature\\RateLimitingTest::test_rate_limit_counter_increment": 0.005, "Tests\\Feature\\RateLimitingTest::test_rate_limit_exceeded_detection": 0.004, "Tests\\Feature\\RateLimitingTest::test_rate_limit_counter_reset": 0.008, "Tests\\Feature\\RateLimitingTest::test_admin_can_create_custom_rate_limit": 0.014, "Tests\\Feature\\RateLimitingTest::test_admin_can_reset_rate_limit_counters": 0.011, "Tests\\Feature\\RateLimitingTest::test_developer_cannot_access_rate_limit_management": 0.02, "Tests\\Feature\\RateLimitingTest::test_rate_limit_prevents_duplicate_creation": 0.014, "Tests\\Feature\\RateLimitingTest::test_rate_limit_validation": 0.008, "Tests\\Unit\\ApiKeyTest::test_api_key_prefix_generation": 0, "Tests\\Unit\\ApiKeyTest::test_api_key_has_many_request_logs": 0.016, "Tests\\Unit\\ApiKeyTest::test_api_key_is_expired_method": 0.009, "Tests\\Unit\\ApiKeyTest::test_api_key_mark_as_used_method": 0.008, "Tests\\Unit\\ApiKeyTest::test_api_key_factory_creates_valid_key": 0.003, "Tests\\Unit\\ApiKeyTest::test_api_key_factory_can_create_inactive_key": 0.002, "Tests\\Unit\\ApiKeyTest::test_api_key_factory_can_create_expired_key": 0.002, "Tests\\Unit\\ApiKeyTest::test_api_key_casts_attributes_correctly": 0.032, "Tests\\Unit\\ApiProxyTest::test_api_proxy_has_many_rate_limits": 0.011, "Tests\\Unit\\ApiProxyTest::test_api_proxy_has_many_request_logs": 0.007, "Tests\\Unit\\ApiProxyTest::test_api_proxy_is_method_allowed": 0.002, "Tests\\Unit\\ApiProxyTest::test_api_proxy_get_target_url": 0.004, "Tests\\Unit\\ApiProxyTest::test_api_proxy_scope_active": 0.007, "Tests\\Unit\\ApiProxyTest::test_api_proxy_find_by_path": 0.007, "Tests\\Unit\\ApiProxyTest::test_api_proxy_factory_creates_valid_proxy": 0.004, "Tests\\Unit\\ApiProxyTest::test_api_proxy_factory_can_create_inactive_proxy": 0.003, "Tests\\Unit\\ApiProxyTest::test_api_proxy_factory_can_create_no_auth_proxy": 0.004, "Tests\\Unit\\ApiProxyTest::test_api_proxy_casts_attributes_correctly": 0.004, "Tests\\Unit\\ApiProxyTest::test_api_proxy_fillable_attributes": 0.001, "Tests\\Unit\\AppTest::test_app_belongs_to_user": 0.003, "Tests\\Unit\\AppTest::test_app_has_many_api_keys": 0.002, "Tests\\Unit\\AppTest::test_app_can_have_multiple_api_keys": 0.004, "Tests\\Unit\\AppTest::test_app_factory_creates_valid_app": 0.002, "Tests\\Unit\\AppTest::test_app_factory_can_create_inactive_app": 0.003, "Tests\\Unit\\AppTest::test_app_casts_attributes_correctly": 0.003, "Tests\\Unit\\AppTest::test_app_fillable_attributes": 0.003, "Tests\\Unit\\AppTest::test_app_scope_active": 0.005, "Tests\\Unit\\AppTest::test_app_scope_inactive": 0.005, "Tests\\Unit\\AppTest::test_app_deletion_cascades_to_api_keys": 0.004, "Tests\\Unit\\AppTest::test_app_has_timestamps": 0.003, "Tests\\Unit\\MiddlewareTest::test_admin_middleware_allows_admin_users": 0.061, "Tests\\Unit\\MiddlewareTest::test_admin_middleware_blocks_non_admin_users": 0.01, "Tests\\Unit\\MiddlewareTest::test_admin_middleware_redirects_guests": 0.01, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_allows_developer_users": 0.022, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_allows_admin_users": 0.01, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_blocks_regular_users": 0.005, "Tests\\Unit\\MiddlewareTest::test_developer_middleware_redirects_guests": 0.002, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_validates_header_authentication": 0.042, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_validates_query_parameter_authentication": 0.005, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_invalid_key": 0.616, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_missing_key": 0.001, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_inactive_key": 0.002, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_expired_key": 0.003, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_rejects_inactive_app": 0.046, "Tests\\Unit\\PolicyTest::test_app_policy_view_any_allows_owner": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_view_allows_owner": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_view_denies_non_owner": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_view_allows_admin": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_create_allows_developer": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_create_allows_admin": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_create_denies_regular_user": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_update_allows_owner": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_update_denies_non_owner": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_update_allows_admin": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_delete_allows_owner": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_delete_denies_non_owner": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_delete_allows_admin": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_restore_allows_owner": 0.006, "Tests\\Unit\\PolicyTest::test_app_policy_restore_allows_admin": 0.004, "Tests\\Unit\\PolicyTest::test_app_policy_force_delete_allows_owner": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_force_delete_allows_admin": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_handles_null_user": 0.002, "Tests\\Unit\\PolicyTest::test_app_policy_handles_inactive_user": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_with_different_user_roles": 0.003, "Tests\\Unit\\RequestLogTest::test_request_log_belongs_to_api_key": 0.007, "Tests\\Unit\\RequestLogTest::test_request_log_belongs_to_api_proxy": 0.003, "Tests\\Unit\\RequestLogTest::test_request_log_can_have_null_api_key": 0.002, "Tests\\Unit\\RequestLogTest::test_request_log_factory_creates_valid_log": 0.005, "Tests\\Unit\\RequestLogTest::test_request_log_factory_can_create_successful_log": 0.042, "Tests\\Unit\\RequestLogTest::test_request_log_factory_can_create_failed_log": 0.01, "Tests\\Unit\\RequestLogTest::test_request_log_factory_can_create_slow_log": 0.007, "Tests\\Unit\\RequestLogTest::test_request_log_casts_attributes_correctly": 0.006, "Tests\\Unit\\RequestLogTest::test_request_log_fillable_attributes": 0.008, "Tests\\Unit\\RequestLogTest::test_request_log_has_timestamps": 0.003, "Tests\\Unit\\RequestLogTest::test_request_log_supports_ipv6": 0.006, "Tests\\Unit\\UserTest::test_user_has_default_role_developer": 0.001, "Tests\\Unit\\UserTest::test_user_can_be_admin": 0.002, "Tests\\Unit\\UserTest::test_user_can_be_developer": 0.002, "Tests\\Unit\\UserTest::test_user_has_apps_relationship": 0.004, "Tests\\Unit\\UserTest::test_user_can_have_multiple_apps": 0.002, "Tests\\Unit\\UserTest::test_user_factory_creates_valid_user": 0.001, "Tests\\Unit\\UserTest::test_user_factory_can_create_unverified_user": 0.001, "Tests\\Unit\\UserTest::test_user_casts_attributes_correctly": 0.002, "Tests\\Unit\\UserTest::test_user_hidden_attributes": 0.029, "Tests\\Unit\\UserTest::test_user_fillable_attributes": 0.002, "Tests\\Feature\\AdminDashboardTest::test_admin_can_access_dashboard": 0.03, "Tests\\Feature\\AdminDashboardTest::test_developer_cannot_access_admin_dashboard": 0.009, "Tests\\Feature\\AdminDashboardTest::test_guest_cannot_access_admin_dashboard": 0.002, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_correct_statistics": 0.056, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_activity": 0.055, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_users": 0.026, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_calculates_success_rate": 0.041, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_handles_no_data": 0.02, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_system_health": 0.021, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_top_apps": 0.038, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_error_rate_trends": 0.031, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_performance": 0.255, "Tests\\Feature\\ApiDocumentationTest::test_can_view_api_documentation_index": 0.017, "Tests\\Feature\\ApiDocumentationTest::test_can_view_specific_api_proxy_documentation": 0.009, "Tests\\Feature\\ApiDocumentationTest::test_cannot_view_inactive_api_proxy_documentation": 0.009, "Tests\\Feature\\ApiDocumentationTest::test_can_access_openapi_specification": 0.016, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_includes_active_proxies_only": 0.007, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_includes_correct_methods": 0.007, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_includes_authentication_when_required": 0.005, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_has_correct_structure": 0.005, "Tests\\Feature\\ApiDocumentationTest::test_documentation_index_shows_no_apis_message_when_empty": 0.006, "Tests\\Feature\\ApiDocumentationTest::test_documentation_index_orders_apis_by_name": 0.009, "Tests\\Feature\\ApiDocumentationTest::test_api_proxy_documentation_shows_all_details": 0.007, "Tests\\Feature\\ApiDocumentationTest::test_openapi_specification_handles_empty_database": 0.004, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_can_view_analytics_index": 0.01, "Tests\\Feature\\DeveloperAnalyticsTest::test_analytics_index_shows_correct_data": 0.033, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_can_view_analytics_data_json": 0.01, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_can_view_app_specific_analytics": 0.055, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_cannot_view_other_users_app_analytics": 0.006, "Tests\\Feature\\DeveloperAnalyticsTest::test_app_analytics_handles_no_data": 0.01, "Tests\\Feature\\DeveloperAnalyticsTest::test_app_analytics_with_time_period_filter": 0.049, "Tests\\Feature\\DeveloperAnalyticsTest::test_developer_can_view_request_logs": 0.042, "Tests\\Feature\\DeveloperAnalyticsTest::test_request_logs_can_be_filtered_by_search": 0.051, "Tests\\Feature\\DeveloperAnalyticsTest::test_request_logs_can_be_filtered_by_status": 0.075, "Tests\\Feature\\DeveloperAnalyticsTest::test_request_logs_can_be_filtered_by_method": 0.07, "Tests\\Feature\\DeveloperAnalyticsTest::test_request_logs_can_be_filtered_by_app": 0.059, "Tests\\Feature\\DeveloperAnalyticsTest::test_analytics_excludes_other_users_data": 0.017, "Tests\\Feature\\DeveloperAnalyticsTest::test_analytics_data_returns_empty_for_no_data": 0.004, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_view_api_keys_index": 0.018, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_create_api_key": 0.012, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_store_api_key": 0.009, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_cannot_create_api_key_for_other_users_app": 0.032, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_view_api_key": 0.018, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_cannot_view_other_users_api_key": 0.01, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_edit_api_key": 0.022, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_update_api_key": 0.009, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_delete_api_key": 0.009, "Tests\\Feature\\DeveloperApiKeyTest::test_developer_can_regenerate_api_key": 0.006, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_creation_validates_required_fields": 0.026, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_creation_validates_app_ownership": 0.011, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_update_validates_expiration_date": 0.01, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_creation_shows_new_key_once": 0.005, "Tests\\Feature\\DeveloperApiKeyTest::test_api_key_index_shows_pagination": 0.032, "Tests\\Feature\\DeveloperAppTest::test_developer_can_view_apps_index": 0.054, "Tests\\Feature\\DeveloperAppTest::test_developer_only_sees_own_apps": 0.049, "Tests\\Feature\\DeveloperAppTest::test_developer_can_create_app": 0.007, "Tests\\Feature\\DeveloperAppTest::test_developer_can_store_app": 0.005, "Tests\\Feature\\DeveloperAppTest::test_developer_can_view_app": 0.016, "Tests\\Feature\\DeveloperAppTest::test_developer_cannot_view_other_users_app": 0.012, "Tests\\Feature\\DeveloperAppTest::test_developer_can_edit_app": 0.034, "Tests\\Feature\\DeveloperAppTest::test_developer_can_update_app": 0.245, "Tests\\Feature\\DeveloperAppTest::test_developer_can_delete_app": 0.005, "Tests\\Feature\\DeveloperAppTest::test_app_deletion_cascades_to_api_keys": 0.006, "Tests\\Feature\\DeveloperAppTest::test_app_creation_validates_required_fields": 0.005, "Tests\\Feature\\DeveloperAppTest::test_app_creation_validates_callback_url_format": 0.004, "Tests\\Feature\\DeveloperAppTest::test_app_update_validates_required_fields": 0.004, "Tests\\Feature\\DeveloperAppTest::test_app_show_displays_api_keys": 0.017, "Tests\\Feature\\DeveloperAppTest::test_app_show_displays_statistics": 0.018, "Tests\\Feature\\DeveloperAppTest::test_apps_index_shows_pagination": 0.079, "Tests\\Feature\\DeveloperAppTest::test_app_creation_sets_default_values": 0.009, "Tests\\Feature\\DeveloperAppTest::test_admin_can_view_any_app": 0.016, "Tests\\Feature\\DeveloperAppTest::test_app_index_handles_empty_state": 0.01, "Tests\\Feature\\DeveloperAppTest::test_app_show_handles_inactive_app": 0.011, "Tests\\Feature\\DeveloperDashboardTest::test_developer_can_access_dashboard": 0.017, "Tests\\Feature\\DeveloperDashboardTest::test_admin_can_access_developer_dashboard": 0.021, "Tests\\Feature\\DeveloperDashboardTest::test_non_developer_cannot_access_dashboard": 0.003, "Tests\\Feature\\DeveloperDashboardTest::test_guest_cannot_access_dashboard": 0.004, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_shows_correct_statistics": 0.08, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_shows_recent_apps": 0.016, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_shows_recent_logs": 0.094, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_handles_user_with_no_data": 0.016, "Tests\\Feature\\DeveloperDashboardTest::test_developer_can_access_documentation": 0.027, "Tests\\Feature\\DeveloperDashboardTest::test_developer_can_access_support": 0.009, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_excludes_other_users_data": 0.061, "Tests\\Feature\\DeveloperDashboardTest::test_dashboard_calculates_success_rate_correctly": 0.066, "Tests\\Feature\\PortalTest::test_can_view_home_page": 0.005, "Tests\\Feature\\PortalTest::test_can_view_pricing_page": 0.004, "Tests\\Feature\\PortalTest::test_can_view_support_page": 0.004, "Tests\\Feature\\PortalTest::test_home_page_shows_statistics": 0.044, "Tests\\Feature\\PortalTest::test_home_page_shows_featured_apis": 0.007, "Tests\\Feature\\PortalTest::test_pricing_page_shows_all_plans": 0.004, "Tests\\Feature\\PortalTest::test_support_page_has_contact_form": 0.004, "Tests\\Feature\\PortalTest::test_can_submit_support_form": 0.003, "Tests\\Feature\\PortalTest::test_support_form_validates_required_fields": 0.003, "Tests\\Feature\\PortalTest::test_support_form_validates_email_format": 0.036, "Tests\\Feature\\PortalTest::test_home_page_handles_no_data": 0.006, "Tests\\Feature\\PortalTest::test_home_page_shows_recent_activity": 0.022, "Tests\\Feature\\PortalTest::test_home_page_shows_success_metrics": 0.014, "Tests\\Feature\\PortalTest::test_home_page_shows_average_response_time": 0.007, "Tests\\Feature\\PortalTest::test_pricing_page_shows_feature_comparison": 0.003, "Tests\\Feature\\PortalTest::test_support_page_shows_faq": 0.004, "Tests\\Feature\\PortalTest::test_home_page_performance_with_large_dataset": 2.805, "Tests\\Unit\\MiddlewareTest::test_api_key_middleware_exists": 0.015, "Tests\\Unit\\MiddlewareTest::test_middleware_classes_exist": 0, "Tests\\Unit\\PolicyTest::test_app_policy_create_denies_null_user": 0, "Tests\\Unit\\PolicyTest::test_app_policy_restore_denies_owner": 0.003, "Tests\\Unit\\PolicyTest::test_app_policy_force_delete_denies_owner": 0.003, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_apps": 0.044, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_request_stats": 0.043, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_basic_data": 0.035, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_recent_apps_with_users": 0.031, "Tests\\Feature\\AdminDashboardTest::test_admin_dashboard_shows_weekly_stats": 0.032, "Tests\\Feature\\PortalTest::test_home_page_shows_featured_apis_data": 0.013, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_create_app_with_custom_headers": 0.004, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_create_endpoint_for_app": 0.006, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_view_endpoint": 0.024, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_update_endpoint": 0.011, "Tests\\Feature\\AppEndpointManagementTest::test_developer_can_delete_endpoint": 0.011, "Tests\\Feature\\AppEndpointManagementTest::test_developer_cannot_access_other_users_endpoints": 0.007, "Tests\\Feature\\AppEndpointManagementTest::test_app_headers_are_automatically_included_in_endpoints": 0.008, "Tests\\Feature\\AppEndpointManagementTest::test_relative_target_url_is_combined_with_base_url": 0.005, "Tests\\Feature\\AppEndpointManagementTest::test_absolute_target_url_is_not_modified": 0.007, "Tests\\Feature\\AppEndpointDebugTest::test_debug_endpoint_creation": 0.009, "Tests\\Feature\\AppEndpointDebugTest::test_debug_app_creation_with_headers": 0.008}}