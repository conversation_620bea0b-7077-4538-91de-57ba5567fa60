{"version": 1, "defects": {"Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_app": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_view_their_apps": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_cannot_view_other_users_apps": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_api_key": 7, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_generation_creates_proper_hash": 8, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_regenerate_api_key": 7, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_deactivate_api_key": 7, "Tests\\Feature\\ApiProxyTest::test_admin_can_create_api_proxy": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_requires_authentication_when_configured": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_allows_unauthenticated_access_when_configured": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_validates_http_methods": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_with_valid_api_key": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_logs_requests": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_handles_backend_errors": 7, "Tests\\Feature\\ApiProxyTest::test_api_proxy_method_validation": 7, "Tests\\Feature\\AuthenticationTest::test_developer_middleware_allows_admin_users": 7, "Tests\\Feature\\AuthenticationTest::test_inactive_user_cannot_access_protected_routes": 7, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 7, "Tests\\Feature\\RateLimitingTest::test_admin_can_create_custom_rate_limit": 7, "Tests\\Feature\\RateLimitingTest::test_admin_can_reset_rate_limit_counters": 7, "Tests\\Feature\\RateLimitingTest::test_rate_limit_prevents_duplicate_creation": 7, "Tests\\Feature\\RateLimitingTest::test_rate_limit_validation": 7}, "times": {"Tests\\Unit\\ApiKeyTest::test_api_key_generation": 1.442, "Tests\\Unit\\ApiKeyTest::test_api_key_hashing": 0.003, "Tests\\Unit\\ApiKeyTest::test_api_key_prefix_extraction": 0, "Tests\\Unit\\ApiKeyTest::test_api_key_expiry_detection": 2.519, "Tests\\Unit\\ApiKeyTest::test_api_key_usage_marking": 0.264, "Tests\\Unit\\ApiKeyTest::test_api_key_belongs_to_app": 0.197, "Tests\\Unit\\ApiKeyTest::test_api_key_has_rate_limits_relationship": 0.108, "Tests\\Unit\\ApiKeyTest::test_api_key_has_request_logs_relationship": 0.036, "Tests\\Unit\\ApiKeyTest::test_api_key_casts": 0.081, "Tests\\Unit\\ApiKeyTest::test_api_key_fillable_attributes": 0.001, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_app": 14.743, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_view_their_apps": 0.472, "Tests\\Feature\\ApiKeyManagementTest::test_developer_cannot_view_other_users_apps": 0.501, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_create_api_key": 0.454, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_generation_creates_proper_hash": 0.38, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_regenerate_api_key": 0.432, "Tests\\Feature\\ApiKeyManagementTest::test_developer_can_deactivate_api_key": 0.446, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_validation_methods": 0, "Tests\\Feature\\ApiKeyManagementTest::test_expired_api_key_detection": 0.004, "Tests\\Feature\\ApiKeyManagementTest::test_api_key_usage_tracking": 0.002, "Tests\\Feature\\ApiProxyTest::test_admin_can_create_api_proxy": 0.484, "Tests\\Feature\\ApiProxyTest::test_developer_cannot_create_api_proxy": 0.679, "Tests\\Feature\\ApiProxyTest::test_api_proxy_requires_authentication_when_configured": 0.926, "Tests\\Feature\\ApiProxyTest::test_api_proxy_allows_unauthenticated_access_when_configured": 0.521, "Tests\\Feature\\ApiProxyTest::test_api_proxy_validates_http_methods": 0.007, "Tests\\Feature\\ApiProxyTest::test_api_proxy_with_valid_api_key": 0.017, "Tests\\Feature\\ApiProxyTest::test_api_proxy_logs_requests": 0.043, "Tests\\Feature\\ApiProxyTest::test_api_proxy_returns_404_for_unknown_path": 0.042, "Tests\\Feature\\ApiProxyTest::test_api_proxy_handles_backend_errors": 0.007, "Tests\\Feature\\ApiProxyTest::test_api_proxy_method_validation": 0.001, "Tests\\Feature\\ApiProxyTest::test_api_proxy_target_url_generation": 0.002, "Tests\\Feature\\ApiProxyTest::test_api_proxy_scope_active": 0.04, "Tests\\Feature\\ApiProxyTest::test_api_proxy_find_by_path": 0.001, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 2.404, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 1.814, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.375, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 0.035, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.393, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.312, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 0.079, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.873, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.008, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.195, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 1.051, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 2.299, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 1.861, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.563, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 0.047, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 0.054, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 2.351, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.092, "Tests\\Feature\\AuthenticationTest::test_user_can_register": 0.009, "Tests\\Feature\\AuthenticationTest::test_user_can_login": 0.009, "Tests\\Feature\\AuthenticationTest::test_admin_user_redirects_to_admin_dashboard": 0.083, "Tests\\Feature\\AuthenticationTest::test_developer_user_redirects_to_developer_dashboard": 0.003, "Tests\\Feature\\AuthenticationTest::test_admin_middleware_blocks_non_admin_users": 0.038, "Tests\\Feature\\AuthenticationTest::test_developer_middleware_allows_admin_users": 0.514, "Tests\\Feature\\AuthenticationTest::test_unauthenticated_user_redirected_to_login": 0.002, "Tests\\Feature\\AuthenticationTest::test_inactive_user_cannot_access_protected_routes": 0.005, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.963, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 6.205, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 0.185, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 0.007, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 0.007, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 0.01, "Tests\\Feature\\RateLimitingTest::test_rate_limit_creation": 0.057, "Tests\\Feature\\RateLimitingTest::test_rate_limit_counter_increment": 0.007, "Tests\\Feature\\RateLimitingTest::test_rate_limit_exceeded_detection": 0.006, "Tests\\Feature\\RateLimitingTest::test_rate_limit_counter_reset": 0.006, "Tests\\Feature\\RateLimitingTest::test_admin_can_create_custom_rate_limit": 0.446, "Tests\\Feature\\RateLimitingTest::test_admin_can_reset_rate_limit_counters": 0.429, "Tests\\Feature\\RateLimitingTest::test_developer_cannot_access_rate_limit_management": 0.01, "Tests\\Feature\\RateLimitingTest::test_rate_limit_prevents_duplicate_creation": 0.817, "Tests\\Feature\\RateLimitingTest::test_rate_limit_validation": 0.472}}