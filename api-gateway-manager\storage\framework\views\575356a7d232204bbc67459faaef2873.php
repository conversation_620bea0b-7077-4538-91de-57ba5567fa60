<?php $__env->startSection('sidebar'); ?>
<!-- Sidebar Logo -->
<div class="flex items-center justify-center h-16 px-4 border-b border-secondary-200">
    <div class="flex items-center">
        <div class="h-8 w-8 bg-danger-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">A</span>
        </div>
        <span class="ml-2 text-lg font-semibold text-secondary-900">Admin</span>
    </div>
</div>

<!-- Sidebar Navigation -->
<nav class="flex-1 px-4 py-6 space-y-2">
    <a href="<?php echo e(route('admin.dashboard')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
        Dashboard
    </a>

    <a href="<?php echo e(route('admin.users.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.users.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
        Users
    </a>

    <a href="<?php echo e(route('admin.apps.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.apps.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        Applications
    </a>

    <a href="<?php echo e(route('admin.api-proxies.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.api-proxies.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        API Proxies
    </a>

    <a href="<?php echo e(route('admin.rate-limits.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.rate-limits.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        Rate Limits
    </a>

    <a href="<?php echo e(route('admin.request-logs.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.request-logs.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Request Logs
    </a>

    <a href="<?php echo e(route('admin.analytics.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.analytics.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Analytics
    </a>

    <div class="border-t border-secondary-200 my-4"></div>

    <a href="<?php echo e(route('docs.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        Documentation
    </a>

    <a href="<?php echo e(route('portal.support')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Support
    </a>
</nav>

<!-- Sidebar Footer -->
<div class="px-4 py-4 border-t border-secondary-200">
    <div class="flex items-center">
        <div class="h-8 w-8 rounded-full bg-danger-600 flex items-center justify-center">
            <span class="text-sm font-medium text-white">
                <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

            </span>
        </div>
        <div class="ml-3 flex-1 min-w-0">
            <p class="text-sm font-medium text-secondary-900 truncate">
                <?php echo e(auth()->user()->name); ?>

            </p>
            <p class="text-xs text-secondary-500 truncate">
                Administrator
            </p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('mobile-menu'); ?>
<!-- Mobile Navigation -->
<nav class="space-y-2">
    <a href="<?php echo e(route('admin.dashboard')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
        Dashboard
    </a>

    <a href="<?php echo e(route('admin.users.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.users.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
        Users
    </a>

    <a href="<?php echo e(route('admin.apps.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.apps.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        Applications
    </a>

    <a href="<?php echo e(route('admin.api-proxies.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.api-proxies.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        API Proxies
    </a>

    <a href="<?php echo e(route('admin.rate-limits.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.rate-limits.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        Rate Limits
    </a>

    <a href="<?php echo e(route('admin.request-logs.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.request-logs.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Request Logs
    </a>

    <a href="<?php echo e(route('admin.analytics.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('admin.analytics.*') ? 'bg-danger-100 text-danger-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Analytics
    </a>

    <div class="border-t border-secondary-200 my-4"></div>

    <a href="<?php echo e(route('docs.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        Documentation
    </a>

    <a href="<?php echo e(route('portal.support')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Support
    </a>
</nav>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-title', $pageTitle ?? 'Admin Dashboard'); ?>

<?php $__env->startSection('header-actions'); ?>
<!-- Quick Actions -->
<div class="hidden sm:flex items-center space-x-3">
    <a href="<?php echo e(route('admin.api-proxies.create')); ?>" class="btn-danger btn-sm">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        New Proxy
    </a>
</div>

<!-- Mobile Quick Action -->
<div class="sm:hidden">
    <a href="<?php echo e(route('admin.api-proxies.create')); ?>" class="btn-danger btn-sm">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/layouts/admin.blade.php ENDPATH**/ ?>