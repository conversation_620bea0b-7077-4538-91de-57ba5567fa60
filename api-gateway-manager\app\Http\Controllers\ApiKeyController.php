<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ApiKey;
use App\Models\App;
use Illuminate\Support\Facades\Auth;

class ApiKeyController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();

        // Get all API keys for the user's apps
        $apiKeys = ApiKey::whereHas('app', function($query) use ($user) {
            $query->where('user_id', $user->id);
        })->with(['app'])->orderBy('created_at', 'desc')->paginate(10);

        return view('developer.api-keys.index', compact('apiKeys'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $user = Auth::user();

        // Get the app if specified, otherwise get all user's apps
        if ($request->filled('app_id')) {
            $app = $user->apps()->findOrFail($request->app_id);
            $this->authorize('view', $app);
            $apps = collect([$app]);
        } else {
            $apps = $user->apps()->where('is_active', true)->get();
        }

        return view('developer.api-keys.create', compact('apps'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'app_id' => 'required|exists:apps,id',
            'name' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        // Get the app and authorize
        $app = $user->apps()->findOrFail($request->app_id);
        $this->authorize('update', $app);

        // Generate a new API key
        $keyValue = ApiKey::generateKey();
        $keyHash = ApiKey::hashKey($keyValue);
        $keyPrefix = ApiKey::getKeyPrefix($keyValue);

        $apiKey = $app->apiKeys()->create([
            'name' => $request->name,
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
            'expires_at' => $request->expires_at,
            'is_active' => true,
        ]);

        // Store the plain key in session to show it once
        session()->flash('new_api_key', $keyValue);

        return redirect()->route('developer.api-keys.show', $apiKey)
            ->with('success', 'API key created successfully! Make sure to copy it now as you won\'t be able to see it again.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiKey $apiKey)
    {
        $user = Auth::user();

        // Ensure the API key belongs to one of the user's apps
        if (!$apiKey->app || $apiKey->app->user_id !== $user->id) {
            abort(404);
        }

        $this->authorize('view', $apiKey->app);

        return view('developer.api-keys.show', compact('apiKey'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApiKey $apiKey)
    {
        $user = Auth::user();

        // Ensure the API key belongs to one of the user's apps
        if (!$apiKey->app || $apiKey->app->user_id !== $user->id) {
            abort(404);
        }

        $this->authorize('update', $apiKey->app);

        return view('developer.api-keys.edit', compact('apiKey'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiKey $apiKey)
    {
        $user = Auth::user();

        // Ensure the API key belongs to one of the user's apps
        if (!$apiKey->app || $apiKey->app->user_id !== $user->id) {
            abort(404);
        }

        $this->authorize('update', $apiKey->app);

        $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $apiKey->update($request->only(['name', 'is_active', 'expires_at']));

        return redirect()->route('developer.api-keys.show', $apiKey)
            ->with('success', 'API key updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiKey $apiKey)
    {
        $user = Auth::user();

        // Ensure the API key belongs to one of the user's apps
        if (!$apiKey->app || $apiKey->app->user_id !== $user->id) {
            abort(404);
        }

        $this->authorize('update', $apiKey->app);

        $apiKey->delete();

        return redirect()->route('developer.api-keys.index')
            ->with('success', 'API key deleted successfully!');
    }

    /**
     * Regenerate an API key
     */
    public function regenerate(ApiKey $apiKey)
    {
        $user = Auth::user();

        // Ensure the API key belongs to one of the user's apps
        if (!$apiKey->app || $apiKey->app->user_id !== $user->id) {
            abort(404);
        }

        $this->authorize('update', $apiKey->app);

        // Generate a new API key
        $keyValue = ApiKey::generateKey();
        $keyHash = ApiKey::hashKey($keyValue);
        $keyPrefix = ApiKey::getKeyPrefix($keyValue);

        $apiKey->update([
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
        ]);

        return redirect()->route('developer.api-keys.show', $apiKey)
            ->with('success', 'API key regenerated successfully!')
            ->with('new_api_key', $keyValue);
    }
}
