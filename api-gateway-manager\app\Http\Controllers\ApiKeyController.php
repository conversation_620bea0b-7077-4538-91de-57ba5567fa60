<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ApiKey;
use App\Models\App;
use Illuminate\Support\Facades\Auth;

class ApiKeyController extends Controller
{
    public function __construct()
    {
        $this->middleware('developer');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(App $app)
    {
        $this->authorize('view', $app);

        $apiKeys = $app->apiKeys()->orderBy('created_at', 'desc')->paginate(10);
        return view('api-keys.index', compact('app', 'apiKeys'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(App $app)
    {
        $this->authorize('view', $app);
        return view('api-keys.create', compact('app'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, App $app)
    {
        $this->authorize('update', $app);

        $request->validate([
            'name' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        // Generate a new API key
        $keyValue = ApiKey::generateKey();
        $keyHash = ApiKey::hashKey($keyValue);
        $keyPrefix = ApiKey::getKeyPrefix($keyValue);

        $apiKey = $app->apiKeys()->create([
            'name' => $request->name,
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
            'expires_at' => $request->expires_at,
            'is_active' => true,
        ]);

        // Store the plain key in session to show it once
        session()->flash('new_api_key', $keyValue);

        return redirect()->route('apps.api-keys.show', [$app, $apiKey])
            ->with('success', 'API key created successfully! Make sure to copy it now as you won\'t be able to see it again.');
    }

    /**
     * Display the specified resource.
     */
    public function show(App $app, ApiKey $apiKey)
    {
        $this->authorize('view', $app);

        if ($apiKey->app_id !== $app->id) {
            abort(404);
        }

        return view('api-keys.show', compact('app', 'apiKey'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(App $app, ApiKey $apiKey)
    {
        $this->authorize('update', $app);

        if ($apiKey->app_id !== $app->id) {
            abort(404);
        }

        return view('api-keys.edit', compact('app', 'apiKey'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, App $app, ApiKey $apiKey)
    {
        $this->authorize('update', $app);

        if ($apiKey->app_id !== $app->id) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'is_active' => 'boolean',
            'expires_at' => 'nullable|date|after:now',
        ]);

        $apiKey->update($request->only(['name', 'is_active', 'expires_at']));

        return redirect()->route('apps.api-keys.show', [$app, $apiKey])
            ->with('success', 'API key updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(App $app, ApiKey $apiKey)
    {
        $this->authorize('update', $app);

        if ($apiKey->app_id !== $app->id) {
            abort(404);
        }

        $apiKey->delete();

        return redirect()->route('apps.api-keys.index', $app)
            ->with('success', 'API key deleted successfully!');
    }

    /**
     * Regenerate an API key
     */
    public function regenerate(App $app, ApiKey $apiKey)
    {
        $this->authorize('update', $app);

        if ($apiKey->app_id !== $app->id) {
            abort(404);
        }

        // Generate a new API key
        $keyValue = ApiKey::generateKey();
        $keyHash = ApiKey::hashKey($keyValue);
        $keyPrefix = ApiKey::getKeyPrefix($keyValue);

        $apiKey->update([
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
        ]);

        // Store the plain key in session to show it once
        session()->flash('new_api_key', $keyValue);

        return redirect()->route('apps.api-keys.show', [$app, $apiKey])
            ->with('success', 'API key regenerated successfully! Make sure to copy it now as you won\'t be able to see it again.');
    }
}
