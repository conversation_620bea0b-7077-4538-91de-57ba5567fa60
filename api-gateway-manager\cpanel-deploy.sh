#!/bin/bash

# cPanel Deployment Script for Laravel API Gateway Manager
# This script helps deploy the application on cPanel shared hosting

echo "Starting cPanel deployment for Laravel API Gateway Manager..."

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "Error: artisan file not found. Please run this script from the Laravel root directory."
    exit 1
fi

# Create necessary directories if they don't exist
echo "Creating necessary directories..."
mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views
mkdir -p bootstrap/cache

# Set proper permissions for cPanel
echo "Setting permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
chmod 644 .env

# Clear and cache configuration for production
echo "Optimizing application for production..."
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
php artisan view:clear
php artisan view:cache

# Generate application key if not set
if grep -q "APP_KEY=$" .env; then
    echo "Generating application key..."
    php artisan key:generate
fi

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Seed the database with admin user
echo "Seeding database..."
php artisan db:seed --class=AdminUserSeeder --force

# Create symbolic link for storage (if supported)
if [ ! -L "public/storage" ]; then
    echo "Creating storage symbolic link..."
    php artisan storage:link
fi

# Clear all caches
echo "Clearing caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Re-cache for production
echo "Caching for production..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "Deployment completed successfully!"
echo ""
echo "Next steps:"
echo "1. Update your .env file with production database credentials"
echo "2. Set APP_ENV=production in .env"
echo "3. Set APP_DEBUG=false in .env"
echo "4. Configure your cPanel cron job to run: php /path/to/your/app/artisan schedule:run"
echo "5. Point your domain to the 'public' directory"
echo ""
echo "Default admin credentials:"
echo "Email: <EMAIL>"
echo "Password: password"
echo ""
echo "Please change the admin password after first login!"
