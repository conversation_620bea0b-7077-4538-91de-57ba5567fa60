<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\App;
use App\Models\ApiKey;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ApiKey>
 */
class ApiKeyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $key = ApiKey::generateKey();

        return [
            'app_id' => App::factory(),
            'name' => $this->faker->words(2, true) . ' API Key',
            'key_hash' => ApiKey::hashKey($key),
            'key_prefix' => ApiKey::getKeyPrefix($key),
            'is_active' => true,
            'last_used_at' => null,
            'expires_at' => null,
        ];
    }

    /**
     * Indicate that the API key is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the API key is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
        ]);
    }

    /**
     * Indicate that the API key was recently used.
     */
    public function recentlyUsed(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_used_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }
}
