<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\ApiKey;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = $this->extractApiKey($request);

        if (!$apiKey) {
            return response()->json([
                'error' => 'API key required',
                'message' => 'Please provide a valid API key in the X-API-Key header or api_key parameter'
            ], 401);
        }

        $keyModel = $this->validateApiKey($apiKey);

        if (!$keyModel) {
            return response()->json([
                'error' => 'Invalid API key',
                'message' => 'The provided API key is invalid or inactive'
            ], 401);
        }

        if ($keyModel->isExpired()) {
            return response()->json([
                'error' => 'API key expired',
                'message' => 'The provided API key has expired'
            ], 401);
        }

        // Add the API key model to the request for later use
        $request->attributes->set('api_key', $keyModel);

        // Mark the key as used
        $keyModel->markAsUsed();

        return $next($request);
    }

    /**
     * Extract API key from request
     */
    private function extractApiKey(Request $request): ?string
    {
        // Check X-API-Key header first
        $apiKey = $request->header('X-API-Key');

        // Fall back to api_key parameter
        if (!$apiKey) {
            $apiKey = $request->input('api_key');
        }

        return $apiKey;
    }

    /**
     * Validate API key and return the model if valid
     */
    private function validateApiKey(string $apiKey): ?ApiKey
    {
        $hashedKey = ApiKey::hashKey($apiKey);

        return ApiKey::where('key_hash', $hashedKey)
            ->where('is_active', true)
            ->with(['app'])
            ->first();
    }
}
