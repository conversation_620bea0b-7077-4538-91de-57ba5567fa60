<?php $__env->startSection('title', $apiProxy->name . ' API Documentation - ' . config('app.name')); ?>
<?php $__env->startSection('meta_description', 'Documentation for ' . $apiProxy->name . ' API. Learn about endpoints, parameters, authentication, and examples.'); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="<?php echo e(route('docs.index')); ?>" class="text-secondary-600 hover:text-secondary-900">
                    Documentation
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-secondary-400 mx-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-secondary-500"><?php echo e($apiProxy->name); ?></span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-start justify-between mb-4">
            <div>
                <h1 class="text-3xl font-bold text-secondary-900"><?php echo e($apiProxy->name); ?></h1>
                <p class="mt-2 text-lg text-secondary-600">
                    <?php echo e($apiProxy->description ?: 'API documentation and usage examples.'); ?>

                </p>
            </div>
            <span class="badge-success">Active</span>
        </div>
        
        <!-- Quick Info -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div class="bg-secondary-50 rounded-lg p-4">
                <div class="text-sm font-medium text-secondary-700">Base URL</div>
                <div class="mt-1 text-sm text-secondary-900 font-mono"><?php echo e(config('app.url')); ?>/api</div>
            </div>
            <div class="bg-secondary-50 rounded-lg p-4">
                <div class="text-sm font-medium text-secondary-700">Endpoint</div>
                <div class="mt-1 text-sm text-secondary-900 font-mono"><?php echo e($apiProxy->proxy_path); ?></div>
            </div>
            <div class="bg-secondary-50 rounded-lg p-4">
                <div class="text-sm font-medium text-secondary-700">Authentication</div>
                <div class="mt-1 text-sm <?php echo e($apiProxy->requires_auth ? 'text-warning-600' : 'text-success-600'); ?>">
                    <?php echo e($apiProxy->requires_auth ? 'Required' : 'Not Required'); ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Authentication Section -->
    <?php if($apiProxy->requires_auth): ?>
    <div class="card mb-8">
        <div class="card-header">
            <h2 class="text-xl font-semibold text-secondary-900">Authentication</h2>
        </div>
        <div class="card-body">
            <p class="text-secondary-600 mb-4">
                This API requires authentication using an API key. Include your API key in the request header.
            </p>
            <div class="bg-secondary-900 rounded-lg p-4 overflow-x-auto">
                <code class="text-green-400 text-sm">
                    <span class="text-secondary-400"># Include API key in header</span><br>
                    X-API-Key: agm_your_api_key_here
                </code>
            </div>
            <?php if(auth()->guard()->check()): ?>
                <div class="mt-4">
                    <a href="<?php echo e(route('developer.apps.index')); ?>" class="btn-primary btn-sm">
                        Get API Key
                    </a>
                </div>
            <?php else: ?>
                <div class="mt-4">
                    <a href="<?php echo e(route('register')); ?>" class="btn-primary btn-sm">
                        Sign Up for API Key
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Endpoints Section -->
    <div class="card mb-8">
        <div class="card-header">
            <h2 class="text-xl font-semibold text-secondary-900">Available Methods</h2>
        </div>
        <div class="card-body">
            <div class="space-y-6">
                <?php $__currentLoopData = $apiProxy->allowed_methods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="border border-secondary-200 rounded-lg p-6">
                    <div class="flex items-center mb-4">
                        <span class="badge-<?php echo e($method === 'GET' ? 'success' : ($method === 'POST' ? 'primary' : ($method === 'PUT' || $method === 'PATCH' ? 'warning' : 'danger'))); ?> mr-3">
                            <?php echo e($method); ?>

                        </span>
                        <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($apiProxy->proxy_path); ?></code>
                    </div>
                    
                    <p class="text-secondary-600 mb-4">
                        <?php echo e($method === 'GET' ? 'Retrieve data from the API endpoint.' : 
                           ($method === 'POST' ? 'Create new data via the API endpoint.' : 
                           ($method === 'PUT' ? 'Update existing data via the API endpoint.' : 
                           ($method === 'PATCH' ? 'Partially update existing data via the API endpoint.' : 
                           'Delete data via the API endpoint.')))); ?>

                    </p>
                    
                    <!-- Example Request -->
                    <div class="mb-4">
                        <h4 class="font-medium text-secondary-900 mb-2">Example Request</h4>
                        <div class="bg-secondary-900 rounded-lg p-4 overflow-x-auto">
                            <code class="text-green-400 text-sm">
                                curl -X <?php echo e($method); ?> "<?php echo e(config('app.url')); ?>/api<?php echo e($apiProxy->proxy_path); ?>" \<br>
                                <?php if($apiProxy->requires_auth): ?>
                                &nbsp;&nbsp;-H "X-API-Key: agm_your_api_key_here" \<br>
                                <?php endif; ?>
                                &nbsp;&nbsp;-H "Content-Type: application/json"
                                <?php if(in_array($method, ['POST', 'PUT', 'PATCH'])): ?>
                                \<br>
                                &nbsp;&nbsp;-d '{"key": "value"}'
                                <?php endif; ?>
                            </code>
                        </div>
                    </div>
                    
                    <!-- Request Parameters -->
                    <?php if(in_array($method, ['POST', 'PUT', 'PATCH'])): ?>
                    <div class="mb-4">
                        <h4 class="font-medium text-secondary-900 mb-2">Request Body</h4>
                        <p class="text-sm text-secondary-600 mb-2">
                            Send data as JSON in the request body. The exact structure depends on the backend service.
                        </p>
                        <div class="bg-secondary-50 rounded-lg p-3">
                            <code class="text-sm">
                                {<br>
                                &nbsp;&nbsp;"parameter": "value",<br>
                                &nbsp;&nbsp;"another_parameter": "another_value"<br>
                                }
                            </code>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Response Example -->
                    <div>
                        <h4 class="font-medium text-secondary-900 mb-2">Example Response</h4>
                        <div class="bg-secondary-50 rounded-lg p-3">
                            <code class="text-sm">
                                HTTP/1.1 200 OK<br>
                                Content-Type: application/json<br>
                                <?php if($apiProxy->requires_auth): ?>
                                X-RateLimit-Limit: 1000<br>
                                X-RateLimit-Remaining: 999<br>
                                <?php endif; ?>
                                <br>
                                {<br>
                                &nbsp;&nbsp;"status": "success",<br>
                                &nbsp;&nbsp;"data": {<br>
                                &nbsp;&nbsp;&nbsp;&nbsp;"message": "Response from backend service"<br>
                                &nbsp;&nbsp;}<br>
                                }
                            </code>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Rate Limiting -->
    <?php if($apiProxy->requires_auth): ?>
    <div class="card mb-8">
        <div class="card-header">
            <h2 class="text-xl font-semibold text-secondary-900">Rate Limiting</h2>
        </div>
        <div class="card-body">
            <p class="text-secondary-600 mb-4">
                This API is subject to rate limiting to ensure fair usage. Rate limit information is included in response headers.
            </p>
            
            <div class="overflow-x-auto">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">Header</th>
                            <th class="table-header-cell">Description</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        <tr>
                            <td class="table-cell font-mono text-sm">X-RateLimit-Limit</td>
                            <td class="table-cell">Maximum number of requests allowed per time window</td>
                        </tr>
                        <tr>
                            <td class="table-cell font-mono text-sm">X-RateLimit-Remaining</td>
                            <td class="table-cell">Number of requests remaining in current time window</td>
                        </tr>
                        <tr>
                            <td class="table-cell font-mono text-sm">X-RateLimit-Reset</td>
                            <td class="table-cell">Unix timestamp when the rate limit resets</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Error Responses -->
    <div class="card mb-8">
        <div class="card-header">
            <h2 class="text-xl font-semibold text-secondary-900">Error Responses</h2>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <?php if($apiProxy->requires_auth): ?>
                <div>
                    <h4 class="font-medium text-secondary-900 mb-2">401 Unauthorized</h4>
                    <p class="text-sm text-secondary-600 mb-2">Missing or invalid API key.</p>
                    <div class="bg-secondary-50 rounded-lg p-3">
                        <code class="text-sm">
                            {<br>
                            &nbsp;&nbsp;"error": "unauthorized",<br>
                            &nbsp;&nbsp;"message": "Invalid or missing API key"<br>
                            }
                        </code>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-secondary-900 mb-2">429 Too Many Requests</h4>
                    <p class="text-sm text-secondary-600 mb-2">Rate limit exceeded.</p>
                    <div class="bg-secondary-50 rounded-lg p-3">
                        <code class="text-sm">
                            {<br>
                            &nbsp;&nbsp;"error": "rate_limit_exceeded",<br>
                            &nbsp;&nbsp;"message": "Rate limit exceeded. Try again later."<br>
                            }
                        </code>
                    </div>
                </div>
                <?php endif; ?>
                
                <div>
                    <h4 class="font-medium text-secondary-900 mb-2">500 Internal Server Error</h4>
                    <p class="text-sm text-secondary-600 mb-2">Backend service error.</p>
                    <div class="bg-secondary-50 rounded-lg p-3">
                        <code class="text-sm">
                            {<br>
                            &nbsp;&nbsp;"error": "internal_error",<br>
                            &nbsp;&nbsp;"message": "An error occurred while processing your request"<br>
                            }
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Try It Out -->
    <div class="card">
        <div class="card-body text-center">
            <h2 class="text-xl font-semibold text-secondary-900 mb-4">Try It Out</h2>
            <p class="text-secondary-600 mb-6">
                Test this API interactively using our Swagger UI interface.
            </p>
            <a href="<?php echo e(route('docs.swagger')); ?>#<?php echo e($apiProxy->proxy_path); ?>" class="btn-primary">
                Open Interactive Documentation
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/docs/show.blade.php ENDPATH**/ ?>