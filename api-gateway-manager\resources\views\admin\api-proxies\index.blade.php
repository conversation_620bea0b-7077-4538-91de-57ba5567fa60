@extends('layouts.admin')

@php
    $pageTitle = 'API Proxies';
@endphp

@section('title', 'API Proxies - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                API Proxies
            </h1>
            <p class="mt-2 text-secondary-600">
                Manage backend API services and proxy configurations
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.api-proxies.create') }}" class="btn-danger">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add New Proxy
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-secondary-700 mb-1">Search</label>
                    <input type="text" 
                           id="search"
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search by name or path..."
                           class="form-input">
                </div>
                
                <div class="sm:w-48">
                    <label for="status" class="block text-sm font-medium text-secondary-700 mb-1">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                
                <div class="sm:w-48">
                    <label for="auth" class="block text-sm font-medium text-secondary-700 mb-1">Authentication</label>
                    <select name="auth" id="auth" class="form-select">
                        <option value="">All Types</option>
                        <option value="required" {{ request('auth') === 'required' ? 'selected' : '' }}>Required</option>
                        <option value="optional" {{ request('auth') === 'optional' ? 'selected' : '' }}>Optional</option>
                    </select>
                </div>
                
                <div class="flex space-x-2">
                    <button type="submit" class="btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    @if(request()->hasAny(['search', 'status', 'auth']))
                    <a href="{{ route('admin.api-proxies.index') }}" class="btn-secondary">
                        Clear
                    </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- API Proxies -->
    @if(isset($apiProxies) && $apiProxies->count() > 0)
        <!-- Desktop Table -->
        <div class="hidden lg:block card">
            <div class="data-table-responsive">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">API Proxy</th>
                            <th class="table-header-cell">Path</th>
                            <th class="table-header-cell">Backend URL</th>
                            <th class="table-header-cell">Methods</th>
                            <th class="table-header-cell">Auth</th>
                            <th class="table-header-cell">Status</th>
                            <th class="table-header-cell">Requests</th>
                            <th class="table-header-cell">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        @foreach($apiProxies as $proxy)
                        <tr>
                            <td class="table-cell">
                                <div>
                                    <div class="text-sm font-medium text-secondary-900">{{ $proxy->name }}</div>
                                    <div class="text-sm text-secondary-500 truncate max-w-xs">
                                        {{ $proxy->description ?: 'No description' }}
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell">
                                <code class="text-xs bg-secondary-100 px-2 py-1 rounded">{{ $proxy->proxy_path }}</code>
                            </td>
                            <td class="table-cell">
                                <div class="text-sm text-secondary-900 truncate max-w-xs">{{ $proxy->backend_url }}</div>
                            </td>
                            <td class="table-cell">
                                <div class="flex flex-wrap gap-1">
                                    @foreach($proxy->allowed_methods as $method)
                                        <span class="badge-{{ $method === 'GET' ? 'success' : ($method === 'POST' ? 'primary' : ($method === 'PUT' || $method === 'PATCH' ? 'warning' : 'danger')) }} text-xs">
                                            {{ $method }}
                                        </span>
                                    @endforeach
                                </div>
                            </td>
                            <td class="table-cell">
                                <span class="badge-{{ $proxy->requires_auth ? 'warning' : 'success' }}">
                                    {{ $proxy->requires_auth ? 'Required' : 'Optional' }}
                                </span>
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="status-dot-{{ $proxy->is_active ? 'success' : 'secondary' }}"></div>
                                    <span class="badge-{{ $proxy->is_active ? 'success' : 'secondary' }}">
                                        {{ $proxy->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            </td>
                            <td class="table-cell">
                                <span class="text-sm text-secondary-900">{{ number_format($proxy->total_requests ?? 0) }}</span>
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.api-proxies.show', $proxy) }}" class="action-button-primary">
                                        View
                                    </a>
                                    <a href="{{ route('admin.api-proxies.edit', $proxy) }}" class="action-button-warning">
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.api-proxies.destroy', $proxy) }}" 
                                          onsubmit="return confirm('Are you sure you want to delete this proxy?')"
                                          class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="action-button-danger">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile/Tablet Cards -->
        <div class="lg:hidden mobile-card-list">
            @foreach($apiProxies as $proxy)
            <div class="mobile-card-item">
                <div class="card-body">
                    <div class="mobile-card-header">
                        <div>
                            <h3 class="text-lg font-semibold text-secondary-900">{{ $proxy->name }}</h3>
                            <p class="text-sm text-secondary-500 mt-1">{{ $proxy->description ?: 'No description' }}</p>
                        </div>
                        
                        <!-- Mobile Actions Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-secondary-400 hover:text-secondary-600">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" class="dropdown-menu" style="display: none;">
                                <a href="{{ route('admin.api-proxies.show', $proxy) }}" class="dropdown-item">
                                    View Details
                                </a>
                                <a href="{{ route('admin.api-proxies.edit', $proxy) }}" class="dropdown-item">
                                    Edit Proxy
                                </a>
                                <form method="POST" action="{{ route('admin.api-proxies.destroy', $proxy) }}" 
                                      onsubmit="return confirm('Are you sure you want to delete this proxy?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="dropdown-item-danger w-full text-left">
                                        Delete Proxy
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Proxy Info -->
                    <div class="mobile-card-content">
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Path</span>
                            <code class="text-xs bg-secondary-100 px-2 py-1 rounded">{{ $proxy->proxy_path }}</code>
                        </div>
                        
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Backend URL</span>
                            <span class="mobile-card-value text-xs truncate">{{ $proxy->backend_url }}</span>
                        </div>
                        
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Methods</span>
                            <div class="flex flex-wrap gap-1">
                                @foreach($proxy->allowed_methods as $method)
                                    <span class="badge-{{ $method === 'GET' ? 'success' : ($method === 'POST' ? 'primary' : ($method === 'PUT' || $method === 'PATCH' ? 'warning' : 'danger')) }} text-xs">
                                        {{ $method }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Authentication</span>
                            <span class="badge-{{ $proxy->requires_auth ? 'warning' : 'success' }}">
                                {{ $proxy->requires_auth ? 'Required' : 'Optional' }}
                            </span>
                        </div>
                        
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Status</span>
                            <div class="flex items-center">
                                <div class="status-dot-{{ $proxy->is_active ? 'success' : 'secondary' }}"></div>
                                <span class="badge-{{ $proxy->is_active ? 'success' : 'secondary' }}">
                                    {{ $proxy->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mobile-card-field">
                            <span class="mobile-card-label">Total Requests</span>
                            <span class="mobile-card-value">{{ number_format($proxy->total_requests ?? 0) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if(method_exists($apiProxies, 'links'))
        <div class="mt-8">
            {{ $apiProxies->links() }}
        </div>
        @endif

    @else
        <!-- Empty State -->
        <div class="card">
            <div class="card-body text-center py-12">
                <svg class="h-16 w-16 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                
                @if(request()->hasAny(['search', 'status', 'auth']))
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No proxies found</h3>
                    <p class="text-secondary-600 mb-6">
                        No API proxies match your search criteria. Try adjusting your filters.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('admin.api-proxies.index') }}" class="btn-outline">
                            Clear Filters
                        </a>
                        <a href="{{ route('admin.api-proxies.create') }}" class="btn-danger">
                            Add New Proxy
                        </a>
                    </div>
                @else
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No API proxies configured</h3>
                    <p class="text-secondary-600 mb-6">
                        Get started by creating your first API proxy to expose backend services.
                    </p>
                    <a href="{{ route('admin.api-proxies.create') }}" class="btn-danger">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create First API Proxy
                    </a>
                @endif
            </div>
        </div>
    @endif
</div>
@endsection
