<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ApiProxy;
use Illuminate\Support\Str;

class ApiProxyManagementController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $proxies = ApiProxy::orderBy('created_at', 'desc')->paginate(15);
        return view('admin.api-proxies.index', compact('proxies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.api-proxies.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'proxy_path' => 'required|string|max:255|unique:api_proxies,proxy_path',
            'target_url' => 'required|url|max:255',
            'allowed_methods' => 'required|array|min:1',
            'allowed_methods.*' => 'in:GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS',
            'headers_to_add' => 'nullable|array',
            'headers_to_remove' => 'nullable|array',
            'requires_auth' => 'boolean',
            'timeout' => 'integer|min:1|max:300',
        ]);

        $proxy = ApiProxy::create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'proxy_path' => $request->proxy_path,
            'target_url' => rtrim($request->target_url, '/'),
            'allowed_methods' => $request->allowed_methods,
            'headers_to_add' => $request->headers_to_add ?: null,
            'headers_to_remove' => $request->headers_to_remove ?: null,
            'requires_auth' => $request->boolean('requires_auth'),
            'is_active' => true,
            'timeout' => $request->timeout ?: 30,
        ]);

        return redirect()->route('admin.api-proxies.show', $proxy)
            ->with('success', 'API proxy created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiProxy $apiProxy)
    {
        $apiProxy->load(['requestLogs' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.api-proxies.show', compact('apiProxy'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApiProxy $apiProxy)
    {
        return view('admin.api-proxies.edit', compact('apiProxy'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiProxy $apiProxy)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'proxy_path' => 'required|string|max:255|unique:api_proxies,proxy_path,' . $apiProxy->id,
            'target_url' => 'required|url|max:255',
            'allowed_methods' => 'required|array|min:1',
            'allowed_methods.*' => 'in:GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS',
            'headers_to_add' => 'nullable|array',
            'headers_to_remove' => 'nullable|array',
            'requires_auth' => 'boolean',
            'is_active' => 'boolean',
            'timeout' => 'integer|min:1|max:300',
        ]);

        $apiProxy->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'proxy_path' => $request->proxy_path,
            'target_url' => rtrim($request->target_url, '/'),
            'allowed_methods' => $request->allowed_methods,
            'headers_to_add' => $request->headers_to_add ?: null,
            'headers_to_remove' => $request->headers_to_remove ?: null,
            'requires_auth' => $request->boolean('requires_auth'),
            'is_active' => $request->boolean('is_active'),
            'timeout' => $request->timeout ?: 30,
        ]);

        return redirect()->route('admin.api-proxies.show', $apiProxy)
            ->with('success', 'API proxy updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiProxy $apiProxy)
    {
        $apiProxy->delete();

        return redirect()->route('admin.api-proxies.index')
            ->with('success', 'API proxy deleted successfully!');
    }

    /**
     * Test the API proxy
     */
    public function test(ApiProxy $apiProxy)
    {
        // This could be implemented to test the proxy endpoint
        return response()->json(['message' => 'Test functionality to be implemented']);
    }
}
