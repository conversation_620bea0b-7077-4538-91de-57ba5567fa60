<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiProxy;

class AppEndpointManagementTest extends TestCase
{
    use RefreshDatabase;

    public function test_developer_can_create_app_with_custom_headers()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/apps', [
            'name' => 'Test App',
            'description' => 'A test application',
            'base_url' => 'https://api.example.com',
            'authorization_token' => 'Bearer test-token',
            'custom_headers' => [
                ['name' => 'X-API-Version', 'value' => 'v1'],
                ['name' => 'X-Client-ID', 'value' => 'test-client'],
            ],
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('apps', [
            'name' => 'Test App',
            'user_id' => $user->id,
            'base_url' => 'https://api.example.com',
            'authorization_token' => 'Bearer test-token',
        ]);

        $app = App::where('name', 'Test App')->first();
        $this->assertEquals([
            'X-API-Version' => 'v1',
            'X-Client-ID' => 'test-client',
        ], $app->custom_headers);
    }

    public function test_developer_can_create_endpoint_for_app()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create([
            'user_id' => $user->id,
            'base_url' => 'https://api.example.com',
            'authorization_token' => 'Bearer test-token',
            'custom_headers' => ['X-API-Version' => 'v1'],
        ]);

        $response = $this->actingAs($user)->post("/developer/apps/{$app->id}/endpoints", [
            'name' => 'User API',
            'description' => 'User management endpoint',
            'proxy_path' => '/api/v1/users',
            'target_url' => '/users',
            'allowed_methods' => ['GET', 'POST'],
            'requires_auth' => true,
            'timeout' => 30,
            'headers_to_add' => [
                ['name' => 'X-Custom-Header', 'value' => 'custom-value'],
            ],
            'headers_to_remove' => ['X-Powered-By'],
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('api_proxies', [
            'app_id' => $app->id,
            'name' => 'User API',
            'proxy_path' => '/api/v1/users',
            'target_url' => 'https://api.example.com/users',
            'requires_auth' => true,
        ]);

        $endpoint = ApiProxy::where('name', 'User API')->first();
        $this->assertEquals(['GET', 'POST'], $endpoint->allowed_methods);
        
        // Check that app headers are merged with endpoint headers
        $expectedHeaders = [
            'Authorization' => 'Bearer test-token',
            'X-API-Version' => 'v1',
            'X-Custom-Header' => 'custom-value',
        ];
        $this->assertEquals($expectedHeaders, $endpoint->headers_to_add);
        $this->assertEquals(['X-Powered-By'], $endpoint->headers_to_remove);
    }

    public function test_developer_can_view_endpoint()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $endpoint = ApiProxy::factory()->create([
            'app_id' => $app->id,
            'name' => 'Test Endpoint',
            'proxy_path' => '/api/test',
        ]);

        $response = $this->actingAs($user)->get("/developer/apps/{$app->id}/endpoints/{$endpoint->id}");

        $response->assertOk();
        $response->assertSee('Test Endpoint');
        $response->assertSee('/api/test');
    }

    public function test_developer_can_update_endpoint()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $endpoint = ApiProxy::factory()->create([
            'app_id' => $app->id,
            'name' => 'Original Name',
        ]);

        $response = $this->actingAs($user)->put("/developer/apps/{$app->id}/endpoints/{$endpoint->id}", [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'proxy_path' => '/api/v1/updated',
            'target_url' => 'https://api.example.com/updated',
            'allowed_methods' => ['GET'],
            'requires_auth' => false,
            'is_active' => true,
            'timeout' => 60,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('api_proxies', [
            'id' => $endpoint->id,
            'name' => 'Updated Name',
            'proxy_path' => '/api/v1/updated',
            'requires_auth' => false,
        ]);
    }

    public function test_developer_can_delete_endpoint()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $endpoint = ApiProxy::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user)->delete("/developer/apps/{$app->id}/endpoints/{$endpoint->id}");

        $response->assertRedirect();
        $this->assertDatabaseMissing('api_proxies', ['id' => $endpoint->id]);
    }

    public function test_developer_cannot_access_other_users_endpoints()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        
        $app = App::factory()->create(['user_id' => $user1->id]);
        $endpoint = ApiProxy::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user2)->get("/developer/apps/{$app->id}/endpoints/{$endpoint->id}");
        $response->assertForbidden();
    }

    public function test_app_headers_are_automatically_included_in_endpoints()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create([
            'user_id' => $user->id,
            'authorization_token' => 'Bearer app-token',
            'custom_headers' => [
                'X-App-Header' => 'app-value',
                'X-Version' => '1.0',
            ],
        ]);

        $this->actingAs($user)->post("/developer/apps/{$app->id}/endpoints", [
            'name' => 'Test Endpoint',
            'proxy_path' => '/api/test',
            'target_url' => 'https://api.example.com/test',
            'allowed_methods' => ['GET'],
            'headers_to_add' => [
                ['name' => 'X-Endpoint-Header', 'value' => 'endpoint-value'],
            ],
        ]);

        $endpoint = ApiProxy::where('name', 'Test Endpoint')->first();
        
        $expectedHeaders = [
            'X-App-Header' => 'app-value',
            'X-Version' => '1.0',
            'Authorization' => 'Bearer app-token',
            'X-Endpoint-Header' => 'endpoint-value',
        ];
        
        $this->assertEquals($expectedHeaders, $endpoint->headers_to_add);
    }

    public function test_relative_target_url_is_combined_with_base_url()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create([
            'user_id' => $user->id,
            'base_url' => 'https://api.example.com',
        ]);

        $this->actingAs($user)->post("/developer/apps/{$app->id}/endpoints", [
            'name' => 'Test Endpoint',
            'proxy_path' => '/api/test',
            'target_url' => '/users',
            'allowed_methods' => ['GET'],
        ]);

        $endpoint = ApiProxy::where('name', 'Test Endpoint')->first();
        $this->assertEquals('https://api.example.com/users', $endpoint->target_url);
    }

    public function test_absolute_target_url_is_not_modified()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create([
            'user_id' => $user->id,
            'base_url' => 'https://api.example.com',
        ]);

        $this->actingAs($user)->post("/developer/apps/{$app->id}/endpoints", [
            'name' => 'Test Endpoint',
            'proxy_path' => '/api/test',
            'target_url' => 'https://different-api.com/users',
            'allowed_methods' => ['GET'],
        ]);

        $endpoint = ApiProxy::where('name', 'Test Endpoint')->first();
        $this->assertEquals('https://different-api.com/users', $endpoint->target_url);
    }
}
