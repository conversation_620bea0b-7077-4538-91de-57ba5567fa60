<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\RequestLog;
use App\Models\ApiKey;
use App\Models\ApiProxy;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RequestLogTest extends TestCase
{
    use RefreshDatabase;

    public function test_request_log_belongs_to_api_key()
    {
        $apiKey = ApiKey::factory()->create();
        $requestLog = RequestLog::factory()->create(['api_key_id' => $apiKey->id]);

        $this->assertInstanceOf(ApiKey::class, $requestLog->apiKey);
        $this->assertEquals($apiKey->id, $requestLog->apiKey->id);
    }

    public function test_request_log_belongs_to_api_proxy()
    {
        $apiProxy = ApiProxy::factory()->create();
        $requestLog = RequestLog::factory()->create(['api_proxy_id' => $apiProxy->id]);

        $this->assertInstanceOf(ApiProxy::class, $requestLog->apiProxy);
        $this->assertEquals($apiProxy->id, $requestLog->apiProxy->id);
    }

    public function test_request_log_can_have_null_api_key()
    {
        $requestLog = RequestLog::factory()->create(['api_key_id' => null]);

        $this->assertNull($requestLog->apiKey);
        $this->assertNull($requestLog->api_key_id);
    }

    public function test_request_log_factory_creates_valid_log()
    {
        $requestLog = RequestLog::factory()->create();

        $this->assertNotNull($requestLog->ip_address);
        $this->assertNotNull($requestLog->method);
        $this->assertNotNull($requestLog->path);
        $this->assertIsArray($requestLog->request_headers);
        $this->assertIsInt($requestLog->response_status);
        $this->assertIsArray($requestLog->response_headers);
        $this->assertIsInt($requestLog->response_time_ms);
        $this->assertIsInt($requestLog->response_size_bytes);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $requestLog->requested_at);
        $this->assertInstanceOf(ApiKey::class, $requestLog->apiKey);
        $this->assertInstanceOf(ApiProxy::class, $requestLog->apiProxy);
    }

    public function test_request_log_factory_can_create_successful_log()
    {
        $requestLog = RequestLog::factory()->successful()->create();

        $this->assertGreaterThanOrEqual(200, $requestLog->response_status);
        $this->assertLessThan(300, $requestLog->response_status);
        $this->assertNull($requestLog->error_message);
    }

    public function test_request_log_factory_can_create_failed_log()
    {
        $requestLog = RequestLog::factory()->failed()->create();

        $this->assertGreaterThanOrEqual(400, $requestLog->response_status);
        $this->assertNotNull($requestLog->error_message);
    }

    public function test_request_log_factory_can_create_slow_log()
    {
        $requestLog = RequestLog::factory()->slow()->create();

        $this->assertGreaterThanOrEqual(2000, $requestLog->response_time_ms);
    }

    public function test_request_log_casts_attributes_correctly()
    {
        $requestLog = RequestLog::factory()->create([
            'request_headers' => ['Content-Type' => 'application/json'],
            'response_headers' => ['Content-Type' => 'application/json'],
            'requested_at' => now(),
        ]);

        $this->assertIsArray($requestLog->request_headers);
        $this->assertIsArray($requestLog->response_headers);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $requestLog->requested_at);
    }

    public function test_request_log_fillable_attributes()
    {
        $apiKey = ApiKey::factory()->create();
        $apiProxy = ApiProxy::factory()->create();
        
        $data = [
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'ip_address' => '***********',
            'method' => 'POST',
            'path' => '/test',
            'query_string' => 'param=value',
            'request_headers' => ['Content-Type' => 'application/json'],
            'response_status' => 201,
            'response_headers' => ['Content-Type' => 'application/json'],
            'response_time_ms' => 150,
            'response_size_bytes' => 1024,
            'error_message' => null,
            'requested_at' => now(),
        ];

        $requestLog = RequestLog::create($data);

        $this->assertEquals($apiKey->id, $requestLog->api_key_id);
        $this->assertEquals($apiProxy->id, $requestLog->api_proxy_id);
        $this->assertEquals('***********', $requestLog->ip_address);
        $this->assertEquals('POST', $requestLog->method);
        $this->assertEquals('/test', $requestLog->path);
        $this->assertEquals('param=value', $requestLog->query_string);
        $this->assertEquals(['Content-Type' => 'application/json'], $requestLog->request_headers);
        $this->assertEquals(201, $requestLog->response_status);
        $this->assertEquals(['Content-Type' => 'application/json'], $requestLog->response_headers);
        $this->assertEquals(150, $requestLog->response_time_ms);
        $this->assertEquals(1024, $requestLog->response_size_bytes);
        $this->assertNull($requestLog->error_message);
    }

    public function test_request_log_has_timestamps()
    {
        $requestLog = RequestLog::factory()->create();

        $this->assertNotNull($requestLog->created_at);
        $this->assertNotNull($requestLog->updated_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $requestLog->created_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $requestLog->updated_at);
    }

    public function test_request_log_supports_ipv6()
    {
        $requestLog = RequestLog::factory()->create([
            'ip_address' => '2001:0db8:85a3:0000:0000:8a2e:0370:7334'
        ]);

        $this->assertEquals('2001:0db8:85a3:0000:0000:8a2e:0370:7334', $requestLog->ip_address);
    }
}
