@props([
    'type' => 'line',
    'data' => [],
    'options' => [],
    'height' => '400',
    'responsive' => true,
    'id' => null
])

@php
    $chartId = $id ?? 'chart-' . uniqid();
    $defaultOptions = [
        'responsive' => $responsive,
        'maintainAspectRatio' => false,
        'plugins' => [
            'legend' => [
                'display' => true,
                'position' => 'top'
            ]
        ],
        'scales' => [
            'y' => [
                'beginAtZero' => true,
                'grid' => [
                    'color' => 'rgba(0, 0, 0, 0.1)'
                ]
            ],
            'x' => [
                'grid' => [
                    'color' => 'rgba(0, 0, 0, 0.1)'
                ]
            ]
        ]
    ];
    
    $chartOptions = array_merge_recursive($defaultOptions, $options);
@endphp

<div class="chart-container" style="position: relative; height: {{ $height }}px;">
    <canvas id="{{ $chartId }}" class="chart-canvas"></canvas>
</div>

@once
@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
@endpush
@endonce

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('{{ $chartId }}').getContext('2d');
    
    const chartData = @json($data);
    const chartOptions = @json($chartOptions);
    
    // Mobile-specific adjustments
    if (window.innerWidth < 768) {
        chartOptions.plugins.legend.position = 'bottom';
        chartOptions.plugins.legend.labels = {
            ...chartOptions.plugins.legend.labels,
            boxWidth: 12,
            padding: 10,
            font: {
                size: 12
            }
        };
        
        if (chartOptions.scales) {
            if (chartOptions.scales.x && chartOptions.scales.x.ticks) {
                chartOptions.scales.x.ticks.maxRotation = 45;
                chartOptions.scales.x.ticks.font = { size: 10 };
            }
            if (chartOptions.scales.y && chartOptions.scales.y.ticks) {
                chartOptions.scales.y.ticks.font = { size: 10 };
            }
        }
    }
    
    const chart = new Chart(ctx, {
        type: '{{ $type }}',
        data: chartData,
        options: chartOptions
    });
    
    // Store chart instance for potential updates
    window.charts = window.charts || {};
    window.charts['{{ $chartId }}'] = chart;
    
    // Handle window resize for mobile
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (chart) {
                chart.resize();
            }
        }, 250);
    });
});

// Helper function to update chart data
function updateChart(chartId, newData) {
    if (window.charts && window.charts[chartId]) {
        const chart = window.charts[chartId];
        chart.data = newData;
        chart.update();
    }
}
</script>
@endpush
