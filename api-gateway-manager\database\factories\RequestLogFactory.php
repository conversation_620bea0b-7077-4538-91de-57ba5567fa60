<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\ApiKey;
use App\Models\ApiProxy;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RequestLog>
 */
class RequestLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'api_key_id' => ApiKey::factory(),
            'api_proxy_id' => ApiProxy::factory(),
            'ip_address' => $this->faker->ipv4(),
            'method' => $this->faker->randomElement(['GET', 'POST', 'PUT', 'DELETE']),
            'path' => '/' . $this->faker->word(),
            'query_string' => $this->faker->optional()->word(),
            'request_headers' => ['User-Agent' => 'Test Agent'],
            'response_status' => $this->faker->randomElement([200, 201, 400, 401, 404, 500]),
            'response_headers' => ['Content-Type' => 'application/json'],
            'response_time_ms' => $this->faker->numberBetween(50, 2000),
            'response_size_bytes' => $this->faker->numberBetween(100, 10000),
            'error_message' => null,
            'requested_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ];
    }

    /**
     * Indicate that the request was successful.
     */
    public function successful(): static
    {
        return $this->state(fn (array $attributes) => [
            'response_status' => $this->faker->randomElement([200, 201, 202]),
            'error_message' => null,
        ]);
    }

    /**
     * Indicate that the request failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'response_status' => $this->faker->randomElement([400, 401, 403, 404, 500]),
            'error_message' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the request was slow.
     */
    public function slow(): static
    {
        return $this->state(fn (array $attributes) => [
            'response_time_ms' => $this->faker->numberBetween(2000, 10000),
        ]);
    }
}
