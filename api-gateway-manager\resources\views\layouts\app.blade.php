<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="@yield('meta_description', 'API Gateway Manager - Secure, scalable API management platform')">

    <title>@yield('title', config('app.name', 'API Gateway Manager'))</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Head Content -->
    @stack('head')
</head>
<body class="h-full bg-secondary-50 font-sans antialiased">
    <div id="app" class="min-h-full">
        <!-- Mobile menu overlay -->
        <div x-data="{ mobileMenuOpen: false }" class="relative">
            <!-- Mobile menu -->
            <div x-show="mobileMenuOpen"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-40 lg:hidden"
                 style="display: none;">
                <div class="fixed inset-0 bg-secondary-600 bg-opacity-75" @click="mobileMenuOpen = false"></div>

                <nav class="fixed top-0 left-0 bottom-0 flex flex-col w-5/6 max-w-sm bg-white shadow-xl">
                    <div class="flex items-center justify-between px-4 py-4 border-b border-secondary-200">
                        <div class="flex items-center">
                            <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">AG</span>
                            </div>
                            <span class="ml-2 text-lg font-semibold text-secondary-900">{{ config('app.name') }}</span>
                        </div>
                        <button @click="mobileMenuOpen = false" class="mobile-menu-button">
                            <span class="sr-only">Close menu</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="flex-1 px-4 py-4 overflow-y-auto">
                        @yield('mobile-menu')
                    </div>
                </nav>
            </div>

            <!-- Desktop layout -->
            <div class="lg:flex lg:min-h-screen">
                <!-- Sidebar for desktop -->
                @hasSection('sidebar')
                <div class="hidden lg:flex lg:flex-shrink-0">
                    <div class="flex flex-col w-64 bg-white border-r border-secondary-200">
                        @yield('sidebar')
                    </div>
                </div>
                @endif

                <!-- Main content area -->
                <div class="flex-1 flex flex-col min-h-screen">
                    <!-- Top navigation -->
                    <header class="bg-white shadow-sm border-b border-secondary-200">
                        <div class="px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between items-center h-16">
                                <!-- Mobile menu button -->
                                <div class="flex items-center lg:hidden">
                                    <button @click="mobileMenuOpen = true" class="mobile-menu-button">
                                        <span class="sr-only">Open menu</span>
                                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                                        </svg>
                                    </button>
                                </div>

                                <!-- Logo for mobile and desktop without sidebar -->
                                @if(!View::hasSection('sidebar'))
                                <div class="flex items-center">
                                    <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                                        <span class="text-white font-bold text-sm">AG</span>
                                    </div>
                                    <span class="ml-2 text-lg font-semibold text-secondary-900 hidden sm:block">{{ config('app.name') }}</span>
                                </div>
                                @endif

                                <!-- Page title for mobile -->
                                <div class="flex-1 flex justify-center lg:hidden">
                                    <h1 class="text-lg font-semibold text-secondary-900">@yield('page-title', 'Dashboard')</h1>
                                </div>

                                <!-- Navigation items -->
                                <div class="flex items-center space-x-4">
                                    @yield('header-actions')

                                    @auth
                                    <!-- User menu -->
                                    <div class="relative" x-data="{ userMenuOpen: false }">
                                        <button @click="userMenuOpen = !userMenuOpen"
                                                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            <span class="sr-only">Open user menu</span>
                                            <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                                                <span class="text-sm font-medium text-white">
                                                    {{ substr(auth()->user()->name, 0, 1) }}
                                                </span>
                                            </div>
                                        </button>

                                        <div x-show="userMenuOpen"
                                             @click.away="userMenuOpen = false"
                                             x-transition:enter="transition ease-out duration-200"
                                             x-transition:enter-start="transform opacity-0 scale-95"
                                             x-transition:enter-end="transform opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="transform opacity-100 scale-100"
                                             x-transition:leave-end="transform opacity-0 scale-95"
                                             class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-50"
                                             style="display: none;">
                                            <div class="px-4 py-2 text-sm text-secondary-700 border-b border-secondary-100">
                                                <div class="font-medium">{{ auth()->user()->name }}</div>
                                                <div class="text-secondary-500">{{ auth()->user()->email }}</div>
                                            </div>
                                            <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">Profile</a>
                                            <form method="POST" action="{{ route('logout') }}">
                                                @csrf
                                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                                                    Sign out
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    @else
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('login') }}" class="btn-outline btn-sm">Sign in</a>
                                        <a href="{{ route('register') }}" class="btn-primary btn-sm">Sign up</a>
                                    </div>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Page content -->
                    <main class="flex-1">
                        <!-- Flash messages -->
                        @if(session('success'))
                        <div class="alert-success mx-4 mt-4 sm:mx-6" x-data="{ show: true }" x-show="show" x-transition>
                            <div class="flex justify-between items-center">
                                <span>{{ session('success') }}</span>
                                <button @click="show = false" class="text-success-600 hover:text-success-800">
                                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        @endif

                        @if(session('error'))
                        <div class="alert-danger mx-4 mt-4 sm:mx-6" x-data="{ show: true }" x-show="show" x-transition>
                            <div class="flex justify-between items-center">
                                <span>{{ session('error') }}</span>
                                <button @click="show = false" class="text-danger-600 hover:text-danger-800">
                                    <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        @endif

                        <div class="pb-16 sm:pb-0">
                            @yield('content')
                        </div>
                    </main>

                    <!-- Footer -->
                    <footer class="bg-white border-t border-secondary-200 mt-auto">
                        <div class="px-4 py-4 sm:px-6 lg:px-8">
                            <div class="flex flex-col sm:flex-row justify-between items-center text-sm text-secondary-500">
                                <div class="mb-2 sm:mb-0">
                                    © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
                                </div>
                                <div class="flex space-x-4">
                                    <a href="{{ route('docs.index') }}" class="hover:text-secondary-700">Documentation</a>
                                    <a href="{{ route('portal.support') }}" class="hover:text-secondary-700">Support</a>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    @auth
        @if(request()->routeIs('developer.*') || request()->routeIs('admin.*'))
            <x-mobile-bottom-nav />
        @endif
    @endauth

    <!-- Notification System -->
    <x-notification-system position="top-right" :max-notifications="5" :auto-remove="true" :remove-delay="5000" />

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Mobile Navigation Scripts -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const overlay = document.getElementById('mobile-menu-overlay');

            if (menu && overlay) {
                menu.classList.toggle('hidden');
                overlay.classList.toggle('hidden');
                document.body.classList.toggle('overflow-hidden');
            }
        }

        // Close mobile menu when clicking overlay
        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('mobile-menu-overlay');
            if (overlay) {
                overlay.addEventListener('click', toggleMobileMenu);
            }

            // Close mobile menu on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const menu = document.getElementById('mobile-menu');
                    if (menu && !menu.classList.contains('hidden')) {
                        toggleMobileMenu();
                    }
                }
            });

            // Handle safe area insets for iOS
            if (window.CSS && CSS.supports('padding-top', 'env(safe-area-inset-top)')) {
                document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
                document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
                document.documentElement.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)');
                document.documentElement.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)');
            }
        });

        // Touch feedback for buttons
        document.addEventListener('touchstart', function(e) {
            if (e.target.classList.contains('touch-feedback') ||
                e.target.closest('.touch-feedback')) {
                const element = e.target.classList.contains('touch-feedback') ?
                    e.target : e.target.closest('.touch-feedback');
                element.style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.classList.contains('touch-feedback') ||
                e.target.closest('.touch-feedback')) {
                const element = e.target.classList.contains('touch-feedback') ?
                    e.target : e.target.closest('.touch-feedback');
                setTimeout(() => {
                    element.style.transform = '';
                }, 150);
            }
        });
    </script>
</body>
</html>
