<?php
    $pageTitle = 'Documentation';
?>

<?php $__env->startSection('title', 'Documentation - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Developer Documentation</h1>
        <p class="mt-2 text-secondary-600">Everything you need to integrate with our API Gateway</p>
    </div>

    <!-- Quick Start Guide -->
    <div class="card mb-8">
        <div class="card-header">
            <h2 class="text-lg font-semibold text-secondary-900">Quick Start Guide</h2>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold">1</span>
                    </div>
                    <h3 class="font-medium text-secondary-900 mb-2">Create an App</h3>
                    <p class="text-sm text-secondary-600 mb-4">Set up your application in the developer portal</p>
                    <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-sm btn-primary">Create App</a>
                </div>
                <div class="text-center">
                    <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold">2</span>
                    </div>
                    <h3 class="font-medium text-secondary-900 mb-2">Generate API Key</h3>
                    <p class="text-sm text-secondary-600 mb-4">Create secure authentication keys for your app</p>
                    <a href="<?php echo e(route('developer.api-keys.create')); ?>" class="btn-sm btn-primary">Create Key</a>
                </div>
                <div class="text-center">
                    <div class="h-12 w-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold">3</span>
                    </div>
                    <h3 class="font-medium text-secondary-900 mb-2">Make API Calls</h3>
                    <p class="text-sm text-secondary-600 mb-4">Start integrating with our API endpoints</p>
                    <a href="#authentication" class="btn-sm btn-outline">View Examples</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Documentation Sections -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Authentication -->
            <div class="card" id="authentication">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">Authentication</h2>
                </div>
                <div class="card-body">
                    <p class="text-secondary-600 mb-4">
                        All API requests must include your API key in the request headers for authentication.
                    </p>
                    
                    <h3 class="font-medium text-secondary-900 mb-2">Header Authentication</h3>
                    <div class="bg-secondary-900 rounded-lg p-4 mb-4">
                        <code class="text-green-400 text-sm">
                            curl -H "X-API-Key: your_api_key_here" \<br>
                            &nbsp;&nbsp;&nbsp;&nbsp; https://<?php echo e(request()->getHost()); ?>/api/your-endpoint
                        </code>
                    </div>

                    <h3 class="font-medium text-secondary-900 mb-2">Query Parameter Authentication</h3>
                    <div class="bg-secondary-900 rounded-lg p-4 mb-4">
                        <code class="text-green-400 text-sm">
                            curl "https://<?php echo e(request()->getHost()); ?>/api/your-endpoint?api_key=your_api_key_here"
                        </code>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-blue-800">Security Best Practice</h4>
                                <p class="mt-1 text-sm text-blue-700">
                                    Always use HTTPS and never expose your API keys in client-side code or public repositories.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rate Limiting -->
            <div class="card" id="rate-limiting">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">Rate Limiting</h2>
                </div>
                <div class="card-body">
                    <p class="text-secondary-600 mb-4">
                        API requests are subject to rate limiting to ensure fair usage and system stability.
                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="bg-secondary-50 rounded-lg p-4">
                            <h4 class="font-medium text-secondary-900 mb-1">Free Tier</h4>
                            <p class="text-sm text-secondary-600">1,000 requests per hour</p>
                        </div>
                        <div class="bg-secondary-50 rounded-lg p-4">
                            <h4 class="font-medium text-secondary-900 mb-1">Professional</h4>
                            <p class="text-sm text-secondary-600">10,000 requests per hour</p>
                        </div>
                    </div>

                    <h3 class="font-medium text-secondary-900 mb-2">Rate Limit Headers</h3>
                    <div class="bg-secondary-900 rounded-lg p-4">
                        <code class="text-green-400 text-sm">
                            X-RateLimit-Limit: 1000<br>
                            X-RateLimit-Remaining: 999<br>
                            X-RateLimit-Reset: 1640995200
                        </code>
                    </div>
                </div>
            </div>

            <!-- Error Handling -->
            <div class="card" id="errors">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">Error Handling</h2>
                </div>
                <div class="card-body">
                    <p class="text-secondary-600 mb-4">
                        Our API uses conventional HTTP response codes to indicate the success or failure of requests.
                    </p>
                    
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <span class="badge-success text-xs mr-3">200</span>
                            <span class="text-sm text-secondary-600">OK - Request successful</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-warning text-xs mr-3">400</span>
                            <span class="text-sm text-secondary-600">Bad Request - Invalid request parameters</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-danger text-xs mr-3">401</span>
                            <span class="text-sm text-secondary-600">Unauthorized - Invalid or missing API key</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-danger text-xs mr-3">429</span>
                            <span class="text-sm text-secondary-600">Too Many Requests - Rate limit exceeded</span>
                        </div>
                        <div class="flex items-center">
                            <span class="badge-danger text-xs mr-3">500</span>
                            <span class="text-sm text-secondary-600">Internal Server Error - Something went wrong</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Table of Contents -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Table of Contents</h3>
                </div>
                <div class="card-body">
                    <nav class="space-y-2">
                        <a href="#authentication" class="block text-sm text-primary-600 hover:text-primary-500">Authentication</a>
                        <a href="#rate-limiting" class="block text-sm text-primary-600 hover:text-primary-500">Rate Limiting</a>
                        <a href="#errors" class="block text-sm text-primary-600 hover:text-primary-500">Error Handling</a>
                    </nav>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Quick Links</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-3">
                        <a href="<?php echo e(route('developer.apps.index')); ?>" class="flex items-center text-sm text-secondary-600 hover:text-secondary-900">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            Manage Apps
                        </a>
                        <a href="<?php echo e(route('developer.api-keys.index')); ?>" class="flex items-center text-sm text-secondary-600 hover:text-secondary-900">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                            </svg>
                            API Keys
                        </a>
                        <a href="<?php echo e(route('developer.analytics.index')); ?>" class="flex items-center text-sm text-secondary-600 hover:text-secondary-900">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Analytics
                        </a>
                        <a href="<?php echo e(route('docs.index')); ?>" class="flex items-center text-sm text-secondary-600 hover:text-secondary-900">
                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                            API Reference
                        </a>
                    </div>
                </div>
            </div>

            <!-- Need Help? -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">Need Help?</h3>
                </div>
                <div class="card-body">
                    <p class="text-sm text-secondary-600 mb-4">
                        Can't find what you're looking for? We're here to help!
                    </p>
                    <a href="<?php echo e(route('developer.support')); ?>" class="btn-primary btn-sm w-full">
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/documentation.blade.php ENDPATH**/ ?>