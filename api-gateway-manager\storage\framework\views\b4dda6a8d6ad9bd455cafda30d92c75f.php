<?php
    $pageTitle = 'My Apps';
?>

<?php $__env->startSection('title', 'My Apps - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                My Applications
            </h1>
            <p class="mt-2 text-secondary-600">
                Manage your applications and their API keys
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create New App
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search apps by name or description..."
                           class="form-input">
                </div>
                <div class="flex gap-2">
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                    <button type="submit" class="btn-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Apps Grid -->
    <?php if(isset($apps) && $apps->count() > 0): ?>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <?php $__currentLoopData = $apps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $app): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card hover:shadow-lg transition-shadow">
                <div class="card-body">
                    <!-- App Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                <span class="text-primary-600 font-bold text-lg">
                                    <?php echo e(substr($app->name, 0, 2)); ?>

                                </span>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-semibold text-secondary-900"><?php echo e($app->name); ?></h3>
                                <span class="badge-<?php echo e($app->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($app->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                            </div>
                        </div>
                        
                        <!-- Actions Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-secondary-400 hover:text-secondary-600">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-10"
                                 style="display: none;">
                                <a href="<?php echo e(route('developer.apps.show', $app)); ?>" 
                                   class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                                    View Details
                                </a>
                                <a href="<?php echo e(route('developer.apps.edit', $app)); ?>" 
                                   class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                                    Edit App
                                </a>
                                <form method="POST" action="<?php echo e(route('developer.apps.destroy', $app)); ?>" 
                                      onsubmit="return confirm('Are you sure you want to delete this app?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" 
                                            class="block w-full text-left px-4 py-2 text-sm text-danger-700 hover:bg-danger-50">
                                        Delete App
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- App Description -->
                    <p class="text-secondary-600 text-sm mb-4 line-clamp-2">
                        <?php echo e($app->description ?: 'No description provided.'); ?>

                    </p>

                    <!-- App Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="text-center p-3 bg-secondary-50 rounded-lg">
                            <div class="text-lg font-bold text-secondary-900"><?php echo e($app->api_keys_count ?? 0); ?></div>
                            <div class="text-xs text-secondary-600">API Keys</div>
                        </div>
                        <div class="text-center p-3 bg-secondary-50 rounded-lg">
                            <div class="text-lg font-bold text-secondary-900"><?php echo e(number_format($app->total_requests ?? 0)); ?></div>
                            <div class="text-xs text-secondary-600">Total Requests</div>
                        </div>
                    </div>

                    <!-- App Actions -->
                    <div class="flex gap-2">
                        <a href="<?php echo e(route('developer.apps.show', $app)); ?>" class="btn-outline btn-sm flex-1">
                            View Details
                        </a>
                        <a href="<?php echo e(route('developer.api-keys.create', ['app_id' => $app->id])); ?>" class="btn-primary btn-sm flex-1">
                            Add Key
                        </a>
                    </div>

                    <!-- App Meta -->
                    <div class="mt-4 pt-4 border-t border-secondary-200">
                        <div class="flex justify-between text-xs text-secondary-500">
                            <span>Created <?php echo e($app->created_at->diffForHumans()); ?></span>
                            <span>Updated <?php echo e($app->updated_at->diffForHumans()); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if(method_exists($apps, 'links')): ?>
        <div class="mt-8">
            <?php echo e($apps->links()); ?>

        </div>
        <?php endif; ?>

    <?php else: ?>
        <!-- Empty State -->
        <div class="card">
            <div class="card-body text-center py-12">
                <svg class="h-16 w-16 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                
                <?php if(request()->has('search') || request()->has('status')): ?>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No apps found</h3>
                    <p class="text-secondary-600 mb-6">
                        No applications match your search criteria. Try adjusting your filters.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?php echo e(route('developer.apps.index')); ?>" class="btn-outline">
                            Clear Filters
                        </a>
                        <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-primary">
                            Create New App
                        </a>
                    </div>
                <?php else: ?>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No applications yet</h3>
                    <p class="text-secondary-600 mb-6">
                        Get started by creating your first application. Apps help you organize your API keys and track usage.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create Your First App
                        </a>
                        <a href="<?php echo e(route('portal.getting-started')); ?>" class="btn-outline">
                            Getting Started Guide
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/apps/index.blade.php ENDPATH**/ ?>