<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ApiProxy extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'proxy_path',
        'target_url',
        'allowed_methods',
        'headers_to_add',
        'headers_to_remove',
        'requires_auth',
        'is_active',
        'timeout',
    ];

    protected function casts(): array
    {
        return [
            'allowed_methods' => 'array',
            'headers_to_add' => 'array',
            'headers_to_remove' => 'array',
            'requires_auth' => 'boolean',
            'is_active' => 'boolean',
            'timeout' => 'integer',
        ];
    }

    /**
     * Get the rate limits for this API proxy
     */
    public function rateLimits()
    {
        return $this->hasMany(RateLimit::class);
    }

    /**
     * Get the request logs for this API proxy
     */
    public function requestLogs()
    {
        return $this->hasMany(RequestLog::class);
    }

    /**
     * Check if a method is allowed for this proxy
     */
    public function isMethodAllowed(string $method): bool
    {
        return in_array(strtoupper($method), $this->allowed_methods);
    }

    /**
     * Get the full target URL for a given path
     */
    public function getTargetUrl(string $path = ''): string
    {
        return rtrim($this->target_url, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Scope to get active proxies
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Find proxy by path
     */
    public static function findByPath(string $path): ?self
    {
        return static::active()
            ->where('proxy_path', $path)
            ->first();
    }
}
