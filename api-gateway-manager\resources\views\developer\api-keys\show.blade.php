@extends('layouts.developer')

@php
    $pageTitle = 'API Key Details';
@endphp

@section('title', 'API Key Details - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">{{ $apiKey->name }}</h1>
                <p class="mt-2 text-secondary-600">API key for {{ $apiKey->app->name }}</p>
            </div>
            <div class="flex space-x-4">
                <a href="{{ route('developer.api-keys.edit', $apiKey) }}" class="btn-secondary">
                    Edit
                </a>
                <a href="{{ route('developer.api-keys.index') }}" class="btn-outline">
                    Back to API Keys
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-4xl">

    @if(session('new_api_key'))
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <svg class="h-6 w-6 text-green-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-green-800">API Key Created Successfully!</h3>
                    <p class="mt-2 text-sm text-green-700">
                        Your new API key is shown below. <strong>Copy it now</strong> as you won't be able to see it again for security reasons.
                    </p>
                    <div class="mt-4 p-4 bg-white border border-green-300 rounded-lg">
                        <div class="flex items-center justify-between">
                            <code class="text-lg font-mono text-secondary-900 break-all">{{ session('new_api_key') }}</code>
                            <button onclick="copyToClipboard('{{ session('new_api_key') }}')" class="btn-sm btn-primary ml-4">
                                Copy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- API Key Details -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">API Key Details</h2>
            </div>
            <div class="card-body">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Name</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $apiKey->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Application</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $apiKey->app->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Key Prefix</dt>
                        <dd class="mt-1">
                            <code class="text-sm bg-secondary-100 px-2 py-1 rounded">{{ $apiKey->key_prefix }}...</code>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Status</dt>
                        <dd class="mt-1">
                            @if($apiKey->is_active && (!$apiKey->expires_at || $apiKey->expires_at->isFuture()))
                                <span class="badge-success">Active</span>
                            @elseif($apiKey->expires_at && $apiKey->expires_at->isPast())
                                <span class="badge-warning">Expired</span>
                            @else
                                <span class="badge-secondary">Inactive</span>
                            @endif
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Created</dt>
                        <dd class="mt-1 text-sm text-secondary-900">{{ $apiKey->created_at->format('F j, Y \a\t g:i A') }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Expires</dt>
                        <dd class="mt-1 text-sm text-secondary-900">
                            @if($apiKey->expires_at)
                                {{ $apiKey->expires_at->format('F j, Y \a\t g:i A') }}
                            @else
                                Never
                            @endif
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Usage Statistics</h2>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Total Requests</span>
                        <span class="text-lg font-semibold text-secondary-900">{{ $apiKey->requestLogs()->count() }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Last Used</span>
                        <span class="text-sm text-secondary-900">
                            @if($apiKey->last_used_at)
                                {{ $apiKey->last_used_at->diffForHumans() }}
                            @else
                                Never
                            @endif
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Success Rate</span>
                        <span class="text-sm text-secondary-900">
                            @php
                                $total = $apiKey->requestLogs()->count();
                                $successful = $apiKey->requestLogs()->whereBetween('response_status', [200, 299])->count();
                                $rate = $total > 0 ? round(($successful / $total) * 100, 1) : 0;
                            @endphp
                            {{ $rate }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Recent Activity</h2>
            </div>
            <div class="card-body p-0">
                @php
                    $recentLogs = $apiKey->requestLogs()->latest()->limit(10)->get();
                @endphp
                
                @if($recentLogs->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Path</th>
                                    <th>Status</th>
                                    <th>Response Time</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentLogs as $log)
                                    <tr>
                                        <td class="table-cell">
                                            <span class="badge-secondary text-xs">{{ $log->method }}</span>
                                        </td>
                                        <td class="table-cell">
                                            <code class="text-xs">{{ $log->path }}</code>
                                        </td>
                                        <td class="table-cell">
                                            <span class="badge-{{ $log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning') }} text-xs">
                                                {{ $log->response_status }}
                                            </span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm">{{ $log->response_time_ms }}ms</span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm text-secondary-600">{{ $log->requested_at->diffForHumans() }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-secondary-500">No recent activity</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-primary');
        
        setTimeout(function() {
            button.textContent = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-primary');
        }, 2000);
    });
}
</script>
@endsection
