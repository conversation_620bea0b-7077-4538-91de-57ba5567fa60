<?php
    $pageTitle = 'API Key Details';
?>

<?php $__env->startSection('title', 'API Key Details - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl"><?php echo e($apiKey->name); ?></h1>
                <p class="mt-2 text-secondary-600">API key for <?php echo e($apiKey->app->name); ?></p>
            </div>
            <div class="flex space-x-4">
                <a href="<?php echo e(route('developer.api-keys.edit', $apiKey)); ?>" class="btn-secondary">
                    Edit
                </a>
                <a href="<?php echo e(route('developer.api-keys.index')); ?>" class="btn-outline">
                    Back to API Keys
                </a>
            </div>
        </div>
    </div>

    <div class="max-w-4xl">

    <?php if(session('new_api_key')): ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div class="flex">
                <svg class="h-6 w-6 text-green-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-green-800">API Key Created Successfully!</h3>
                    <p class="mt-2 text-sm text-green-700">
                        Your new API key is shown below. <strong>Copy it now</strong> as you won't be able to see it again for security reasons.
                    </p>
                    <div class="mt-4 p-4 bg-white border border-green-300 rounded-lg">
                        <div class="flex items-center justify-between">
                            <code class="text-lg font-mono text-secondary-900 break-all"><?php echo e(session('new_api_key')); ?></code>
                            <button onclick="copyToClipboard('<?php echo e(session('new_api_key')); ?>')" class="btn-sm btn-primary ml-4">
                                Copy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- API Key Details -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">API Key Details</h2>
            </div>
            <div class="card-body">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Name</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($apiKey->name); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Application</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($apiKey->app->name); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Key Prefix</dt>
                        <dd class="mt-1">
                            <code class="text-sm bg-secondary-100 px-2 py-1 rounded"><?php echo e($apiKey->key_prefix); ?>...</code>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Status</dt>
                        <dd class="mt-1">
                            <?php if($apiKey->is_active && (!$apiKey->expires_at || $apiKey->expires_at->isFuture())): ?>
                                <span class="badge-success">Active</span>
                            <?php elseif($apiKey->expires_at && $apiKey->expires_at->isPast()): ?>
                                <span class="badge-warning">Expired</span>
                            <?php else: ?>
                                <span class="badge-secondary">Inactive</span>
                            <?php endif; ?>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Created</dt>
                        <dd class="mt-1 text-sm text-secondary-900"><?php echo e($apiKey->created_at->format('F j, Y \a\t g:i A')); ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-secondary-500">Expires</dt>
                        <dd class="mt-1 text-sm text-secondary-900">
                            <?php if($apiKey->expires_at): ?>
                                <?php echo e($apiKey->expires_at->format('F j, Y \a\t g:i A')); ?>

                            <?php else: ?>
                                Never
                            <?php endif; ?>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Usage Statistics</h2>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Total Requests</span>
                        <span class="text-lg font-semibold text-secondary-900"><?php echo e($apiKey->requestLogs()->count()); ?></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Last Used</span>
                        <span class="text-sm text-secondary-900">
                            <?php if($apiKey->last_used_at): ?>
                                <?php echo e($apiKey->last_used_at->diffForHumans()); ?>

                            <?php else: ?>
                                Never
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-secondary-500">Success Rate</span>
                        <span class="text-sm text-secondary-900">
                            <?php
                                $total = $apiKey->requestLogs()->count();
                                $successful = $apiKey->requestLogs()->whereBetween('response_status', [200, 299])->count();
                                $rate = $total > 0 ? round(($successful / $total) * 100, 1) : 0;
                            ?>
                            <?php echo e($rate); ?>%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mt-8">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Recent Activity</h2>
            </div>
            <div class="card-body p-0">
                <?php
                    $recentLogs = $apiKey->requestLogs()->latest()->limit(10)->get();
                ?>
                
                <?php if($recentLogs->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Method</th>
                                    <th>Path</th>
                                    <th>Status</th>
                                    <th>Response Time</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $recentLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="table-cell">
                                            <span class="badge-secondary text-xs"><?php echo e($log->method); ?></span>
                                        </td>
                                        <td class="table-cell">
                                            <code class="text-xs"><?php echo e($log->path); ?></code>
                                        </td>
                                        <td class="table-cell">
                                            <span class="badge-<?php echo e($log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning')); ?> text-xs">
                                                <?php echo e($log->response_status); ?>

                                            </span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm"><?php echo e($log->response_time_ms); ?>ms</span>
                                        </td>
                                        <td class="table-cell">
                                            <span class="text-sm text-secondary-600"><?php echo e($log->requested_at->diffForHumans()); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-secondary-500">No recent activity</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-primary');
        
        setTimeout(function() {
            button.textContent = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-primary');
        }, 2000);
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/api-keys/show.blade.php ENDPATH**/ ?>