<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>API Documentation - {{ config('app.name') }}</title>
    
    <!-- Swagger UI CSS -->
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        
        *, *:before, *:after {
            box-sizing: inherit;
        }
        
        body {
            margin: 0;
            background: #fafafa;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
        }
        
        .swagger-ui .topbar {
            background-color: #3b82f6;
            padding: 10px 0;
        }
        
        .swagger-ui .topbar .download-url-wrapper {
            display: none;
        }
        
        .swagger-ui .topbar .link {
            color: white;
            font-weight: bold;
            text-decoration: none;
            font-size: 1.2em;
        }
        
        .swagger-ui .topbar .link:hover {
            color: #dbeafe;
        }
        
        .custom-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .custom-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .custom-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .custom-nav {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 0;
            text-align: center;
        }
        
        .custom-nav a {
            color: #6b7280;
            text-decoration: none;
            margin: 0 1rem;
            font-weight: 500;
        }
        
        .custom-nav a:hover {
            color: #3b82f6;
        }
        
        .swagger-ui .info {
            margin: 20px 0;
        }
        
        .swagger-ui .scheme-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .custom-header h1 {
                font-size: 1.5rem;
            }
            
            .custom-nav {
                padding: 0.5rem 0;
            }
            
            .custom-nav a {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Custom Header -->
    <div class="custom-header">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
            <h1>{{ config('app.name') }} API Documentation</h1>
            <p>Interactive API documentation and testing interface</p>
        </div>
    </div>
    
    <!-- Navigation -->
    <div class="custom-nav">
        <a href="{{ route('portal.index') }}">← Back to Portal</a>
        <a href="{{ route('docs.index') }}">Documentation</a>
        <a href="{{ route('portal.getting-started') }}">Getting Started</a>
        <a href="{{ route('portal.support') }}">Support</a>
    </div>
    
    <!-- Swagger UI Container -->
    <div id="swagger-ui"></div>
    
    <!-- Swagger UI JavaScript -->
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    
    <script>
        window.onload = function() {
            // Build a system
            const ui = SwaggerUIBundle({
                url: '{{ route("docs.openapi") }}',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                validatorUrl: null,
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    // Add CSRF token to requests if needed
                    const csrfToken = document.querySelector('meta[name="csrf-token"]');
                    if (csrfToken) {
                        request.headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
                    }
                    return request;
                },
                responseInterceptor: function(response) {
                    // Handle responses
                    return response;
                },
                onComplete: function() {
                    // Hide the top bar download URL input
                    const topbar = document.querySelector('.swagger-ui .topbar');
                    if (topbar) {
                        const downloadWrapper = topbar.querySelector('.download-url-wrapper');
                        if (downloadWrapper) {
                            downloadWrapper.style.display = 'none';
                        }
                    }
                    
                    // Add custom branding to topbar
                    const topbarLink = document.querySelector('.swagger-ui .topbar .link');
                    if (topbarLink) {
                        topbarLink.textContent = '{{ config("app.name") }} API';
                        topbarLink.href = '{{ route("portal.index") }}';
                    }
                },
                docExpansion: 'list',
                apisSorter: 'alpha',
                operationsSorter: 'alpha',
                filter: true,
                showRequestHeaders: true,
                showCommonExtensions: true,
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                displayRequestDuration: true,
                requestSnippetsEnabled: true,
                requestSnippets: {
                    generators: {
                        "curl_bash": {
                            title: "cURL (bash)",
                            syntax: "bash"
                        },
                        "curl_powershell": {
                            title: "cURL (PowerShell)",
                            syntax: "powershell"
                        },
                        "curl_cmd": {
                            title: "cURL (CMD)",
                            syntax: "bash"
                        }
                    },
                    defaultExpanded: false,
                    languages: null
                }
            });
            
            // Add authentication helper
            window.ui = ui;
            
            // Add custom CSS for mobile responsiveness
            const style = document.createElement('style');
            style.textContent = `
                @media (max-width: 768px) {
                    .swagger-ui .wrapper {
                        padding: 0 10px;
                    }
                    
                    .swagger-ui .info {
                        margin: 10px 0;
                    }
                    
                    .swagger-ui .scheme-container {
                        margin: 10px 0;
                        padding: 10px;
                    }
                    
                    .swagger-ui .opblock {
                        margin: 10px 0;
                    }
                    
                    .swagger-ui .opblock .opblock-summary {
                        padding: 10px;
                    }
                    
                    .swagger-ui .parameters-container {
                        padding: 10px;
                    }
                    
                    .swagger-ui .responses-wrapper {
                        padding: 10px;
                    }
                    
                    .swagger-ui .model-box {
                        padding: 10px;
                    }
                    
                    .swagger-ui table {
                        font-size: 12px;
                    }
                    
                    .swagger-ui .parameter__name {
                        font-size: 12px;
                    }
                    
                    .swagger-ui .parameter__type {
                        font-size: 11px;
                    }
                    
                    .swagger-ui .btn {
                        padding: 8px 12px;
                        font-size: 12px;
                    }
                    
                    .swagger-ui .execute-wrapper {
                        padding: 10px;
                    }
                    
                    .swagger-ui .responses-table {
                        overflow-x: auto;
                    }
                    
                    .swagger-ui .response-col_status {
                        min-width: 60px;
                    }
                    
                    .swagger-ui .response-col_description {
                        min-width: 120px;
                    }
                }
                
                /* Custom scrollbar for code blocks */
                .swagger-ui .highlight-code {
                    overflow-x: auto;
                }
                
                .swagger-ui .highlight-code::-webkit-scrollbar {
                    height: 6px;
                }
                
                .swagger-ui .highlight-code::-webkit-scrollbar-track {
                    background: #f1f1f1;
                }
                
                .swagger-ui .highlight-code::-webkit-scrollbar-thumb {
                    background: #888;
                    border-radius: 3px;
                }
                
                .swagger-ui .highlight-code::-webkit-scrollbar-thumb:hover {
                    background: #555;
                }
            `;
            document.head.appendChild(style);
        };
    </script>
</body>
</html>
