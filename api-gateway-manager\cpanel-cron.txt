# cPanel Cron Job Configuration for Laravel API Gateway Manager
# 
# Add this cron job in your cPanel -> Cron Jobs section
# This will run the Laravel scheduler every minute
#
# Minute: *
# Hour: *
# Day: *
# Month: *
# Weekday: *
# Command: /usr/local/bin/php /home/<USER>/public_html/artisan schedule:run >> /dev/null 2>&1
#
# Replace 'yourusername' with your actual cPanel username
# Replace '/home/<USER>/public_html/' with the actual path to your Laravel installation
#
# Alternative command if the above doesn't work:
# /usr/bin/php /home/<USER>/public_html/artisan schedule:run >> /dev/null 2>&1
#
# Or if you want to log the output:
# /usr/local/bin/php /home/<USER>/public_html/artisan schedule:run >> /home/<USER>/cron.log 2>&1

# Example cron job entry (copy the line below to cPanel cron jobs):
* * * * * /usr/local/bin/php /home/<USER>/public_html/artisan schedule:run >> /dev/null 2>&1

# Additional maintenance cron jobs you might want to add:

# Clean up old request logs daily at 2 AM
0 2 * * * /usr/local/bin/php /home/<USER>/public_html/artisan app:cleanup-logs >> /dev/null 2>&1

# Clear expired sessions weekly on Sunday at 3 AM
0 3 * * 0 /usr/local/bin/php /home/<USER>/public_html/artisan session:gc >> /dev/null 2>&1

# Backup database daily at 1 AM (if you have backup script)
# 0 1 * * * /usr/local/bin/php /home/<USER>/public_html/artisan backup:run >> /dev/null 2>&1
