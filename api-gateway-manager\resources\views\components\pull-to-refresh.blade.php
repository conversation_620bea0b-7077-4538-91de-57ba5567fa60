@props([
    'refreshUrl' => null,
    'threshold' => 80,
    'message' => 'Pull to refresh'
])

<div class="pull-to-refresh" x-data="pullToRefresh()" x-init="init()">
    <!-- Pull to Refresh Indicator -->
    <div class="pull-to-refresh-indicator" :class="{ 'active': showIndicator }">
        <div class="flex items-center justify-center">
            <svg class="h-5 w-5 mr-2 animate-spin" :class="{ 'opacity-0': !isRefreshing }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span x-text="refreshMessage">{{ $message }}</span>
        </div>
    </div>
    
    <!-- Content -->
    <div class="pull-to-refresh-content">
        {{ $slot }}
    </div>
</div>

@push('scripts')
<script>
function pullToRefresh() {
    return {
        startY: 0,
        currentY: 0,
        pullDistance: 0,
        threshold: {{ $threshold }},
        showIndicator: false,
        isRefreshing: false,
        refreshMessage: '{{ $message }}',
        refreshUrl: '{{ $refreshUrl }}',
        
        init() {
            const container = this.$el;
            
            // Touch events
            container.addEventListener('touchstart', this.handleStart.bind(this), { passive: true });
            container.addEventListener('touchmove', this.handleMove.bind(this), { passive: false });
            container.addEventListener('touchend', this.handleEnd.bind(this), { passive: true });
            
            // Mouse events for desktop testing
            container.addEventListener('mousedown', this.handleStart.bind(this));
            container.addEventListener('mousemove', this.handleMove.bind(this));
            container.addEventListener('mouseup', this.handleEnd.bind(this));
        },
        
        handleStart(e) {
            // Only trigger if we're at the top of the page
            if (window.scrollY > 0) return;
            
            this.startY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;
            this.isDragging = true;
        },
        
        handleMove(e) {
            if (!this.isDragging || window.scrollY > 0) return;
            
            this.currentY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;
            this.pullDistance = Math.max(0, this.currentY - this.startY);
            
            if (this.pullDistance > 20) {
                e.preventDefault();
                this.showIndicator = true;
                
                // Update message based on pull distance
                if (this.pullDistance >= this.threshold) {
                    this.refreshMessage = 'Release to refresh';
                } else {
                    this.refreshMessage = '{{ $message }}';
                }
                
                // Apply visual feedback
                const indicator = this.$el.querySelector('.pull-to-refresh-indicator');
                const progress = Math.min(this.pullDistance / this.threshold, 1);
                indicator.style.opacity = progress;
            }
        },
        
        handleEnd(e) {
            if (!this.isDragging) return;
            
            this.isDragging = false;
            
            if (this.pullDistance >= this.threshold && !this.isRefreshing) {
                this.refresh();
            } else {
                this.reset();
            }
        },
        
        async refresh() {
            this.isRefreshing = true;
            this.refreshMessage = 'Refreshing...';
            
            try {
                if (this.refreshUrl) {
                    // Perform AJAX refresh
                    const response = await fetch(this.refreshUrl, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.text();
                        // Update content or trigger custom event
                        this.$dispatch('refresh-complete', { data });
                    }
                } else {
                    // Simple page reload
                    window.location.reload();
                    return;
                }
            } catch (error) {
                console.error('Refresh failed:', error);
                this.$dispatch('refresh-error', { error });
            }
            
            // Reset after a short delay
            setTimeout(() => {
                this.reset();
            }, 500);
        },
        
        reset() {
            this.showIndicator = false;
            this.isRefreshing = false;
            this.pullDistance = 0;
            this.refreshMessage = '{{ $message }}';
            
            const indicator = this.$el.querySelector('.pull-to-refresh-indicator');
            indicator.style.opacity = '';
        }
    }
}
</script>
@endpush
