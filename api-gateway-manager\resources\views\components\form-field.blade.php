@props([
    'name',
    'label' => null,
    'type' => 'text',
    'value' => null,
    'placeholder' => null,
    'required' => false,
    'disabled' => false,
    'readonly' => false,
    'help' => null,
    'options' => [],
    'rows' => 4,
    'multiple' => false,
    'accept' => null,
    'min' => null,
    'max' => null,
    'step' => null,
    'pattern' => null,
    'autocomplete' => null,
    'size' => 'default' // default, sm, lg
])

@php
    $fieldId = $name . '_' . uniqid();
    $hasError = $errors->has($name);
    $oldValue = old($name, $value);
    
    $inputClasses = 'form-input';
    if ($hasError) {
        $inputClasses .= ' border-danger-300 focus:border-danger-500 focus:ring-danger-500';
    }
    
    if ($size === 'sm') {
        $inputClasses .= ' text-sm py-2';
    } elseif ($size === 'lg') {
        $inputClasses .= ' text-lg py-3';
    }
    
    // Mobile optimizations
    $inputClasses .= ' mobile-form-input';
@endphp

<div class="mobile-form-group">
    @if($label)
    <label for="{{ $fieldId }}" class="mobile-form-label {{ $required ? 'required' : '' }}">
        {{ $label }}
        @if($required)
        <span class="text-danger-500 ml-1">*</span>
        @endif
    </label>
    @endif

    @if($type === 'textarea')
        <textarea 
            id="{{ $fieldId }}"
            name="{{ $name }}"
            rows="{{ $rows }}"
            class="mobile-form-textarea {{ $inputClasses }}"
            @if($placeholder) placeholder="{{ $placeholder }}" @endif
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($readonly) readonly @endif
            @if($autocomplete) autocomplete="{{ $autocomplete }}" @endif
            {{ $attributes }}
        >{{ $oldValue }}</textarea>
    
    @elseif($type === 'select')
        <select 
            id="{{ $fieldId }}"
            name="{{ $name }}{{ $multiple ? '[]' : '' }}"
            class="mobile-form-select {{ $inputClasses }}"
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($multiple) multiple @endif
            {{ $attributes }}
        >
            @if(!$multiple && !$required)
            <option value="">{{ $placeholder ?: 'Select an option...' }}</option>
            @endif
            
            @foreach($options as $optionValue => $optionLabel)
                @if(is_array($optionLabel))
                    <optgroup label="{{ $optionValue }}">
                        @foreach($optionLabel as $subValue => $subLabel)
                            <option value="{{ $subValue }}" 
                                {{ (is_array($oldValue) ? in_array($subValue, $oldValue) : $oldValue == $subValue) ? 'selected' : '' }}>
                                {{ $subLabel }}
                            </option>
                        @endforeach
                    </optgroup>
                @else
                    <option value="{{ $optionValue }}" 
                        {{ (is_array($oldValue) ? in_array($optionValue, $oldValue) : $oldValue == $optionValue) ? 'selected' : '' }}>
                        {{ $optionLabel }}
                    </option>
                @endif
            @endforeach
        </select>
    
    @elseif($type === 'checkbox')
        <div class="flex items-center">
            <input 
                type="checkbox"
                id="{{ $fieldId }}"
                name="{{ $name }}"
                value="1"
                class="form-checkbox {{ $hasError ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                @if($oldValue) checked @endif
                @if($required) required @endif
                @if($disabled) disabled @endif
                @if($readonly) readonly @endif
                {{ $attributes }}
            >
            @if($label)
            <label for="{{ $fieldId }}" class="ml-2 text-sm text-secondary-700">
                {{ $label }}
                @if($required)
                <span class="text-danger-500 ml-1">*</span>
                @endif
            </label>
            @endif
        </div>
    
    @elseif($type === 'radio')
        <div class="space-y-2">
            @foreach($options as $optionValue => $optionLabel)
            <div class="flex items-center">
                <input 
                    type="radio"
                    id="{{ $fieldId }}_{{ $loop->index }}"
                    name="{{ $name }}"
                    value="{{ $optionValue }}"
                    class="form-radio {{ $hasError ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                    @if($oldValue == $optionValue) checked @endif
                    @if($required) required @endif
                    @if($disabled) disabled @endif
                    {{ $attributes }}
                >
                <label for="{{ $fieldId }}_{{ $loop->index }}" class="ml-2 text-sm text-secondary-700">
                    {{ $optionLabel }}
                </label>
            </div>
            @endforeach
        </div>
    
    @elseif($type === 'file')
        <input 
            type="file"
            id="{{ $fieldId }}"
            name="{{ $name }}{{ $multiple ? '[]' : '' }}"
            class="form-file {{ $hasError ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
            @if($accept) accept="{{ $accept }}" @endif
            @if($multiple) multiple @endif
            @if($required) required @endif
            @if($disabled) disabled @endif
            {{ $attributes }}
        >
    
    @else
        <input 
            type="{{ $type }}"
            id="{{ $fieldId }}"
            name="{{ $name }}"
            value="{{ $oldValue }}"
            class="{{ $inputClasses }}"
            @if($placeholder) placeholder="{{ $placeholder }}" @endif
            @if($required) required @endif
            @if($disabled) disabled @endif
            @if($readonly) readonly @endif
            @if($min) min="{{ $min }}" @endif
            @if($max) max="{{ $max }}" @endif
            @if($step) step="{{ $step }}" @endif
            @if($pattern) pattern="{{ $pattern }}" @endif
            @if($autocomplete) autocomplete="{{ $autocomplete }}" @endif
            {{ $attributes }}
        >
    @endif

    @if($hasError)
    <div class="mt-2 text-sm text-danger-600">
        {{ $errors->first($name) }}
    </div>
    @endif

    @if($help)
    <div class="mt-2 text-sm text-secondary-500">
        {{ $help }}
    </div>
    @endif
</div>

@push('styles')
<style>
.required::after {
    content: ' *';
    color: #ef4444;
}

/* Mobile form optimizations */
@media (max-width: 768px) {
    .mobile-form-input,
    .mobile-form-select,
    .mobile-form-textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 12px;
        min-height: 44px; /* Touch target size */
    }
    
    .mobile-form-label {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
    }
    
    .mobile-form-group {
        margin-bottom: 24px;
    }
}

/* Focus styles for better accessibility */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom file input styling */
.form-file {
    display: block;
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: #ffffff;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
}

.form-file:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox and radio styling */
.form-checkbox,
.form-radio {
    width: 16px;
    height: 16px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background-color: #ffffff;
    color: #3b82f6;
}

.form-radio {
    border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.form-checkbox:focus,
.form-radio:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (max-width: 768px) {
    .form-checkbox,
    .form-radio {
        width: 20px;
        height: 20px;
    }
}
</style>
@endpush
