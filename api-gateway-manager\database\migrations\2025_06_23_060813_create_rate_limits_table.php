<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rate_limits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_key_id')->constrained()->onDelete('cascade');
            $table->foreignId('api_proxy_id')->constrained()->onDelete('cascade');
            $table->integer('requests_per_minute')->default(60);
            $table->integer('requests_per_hour')->default(1000);
            $table->integer('requests_per_day')->default(10000);
            $table->integer('current_minute_count')->default(0);
            $table->integer('current_hour_count')->default(0);
            $table->integer('current_day_count')->default(0);
            $table->timestamp('minute_reset_at')->nullable();
            $table->timestamp('hour_reset_at')->nullable();
            $table->timestamp('day_reset_at')->nullable();
            $table->timestamps();

            $table->unique(['api_key_id', 'api_proxy_id']);
            $table->index(['api_key_id', 'minute_reset_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rate_limits');
    }
};
