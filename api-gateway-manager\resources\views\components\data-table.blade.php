@props([
    'headers' => [],
    'rows' => [],
    'searchable' => false,
    'sortable' => false,
    'paginated' => false,
    'mobileCards' => true,
    'actions' => [],
    'emptyMessage' => 'No data available',
    'emptyIcon' => null,
    'striped' => true,
    'hover' => true,
    'compact' => false
])

@php
    $tableId = 'table-' . uniqid();
    $tableClasses = 'table';
    if ($striped) $tableClasses .= ' table-striped';
    if ($hover) $tableClasses .= ' table-hover';
    if ($compact) $tableClasses .= ' table-compact';
@endphp

<div class="data-table-container" x-data="dataTable()" x-init="init()">
    @if($searchable)
    <!-- Search Bar -->
    <div class="mb-4">
        <div class="relative">
            <input 
                type="text" 
                x-model="searchTerm"
                @input="filterRows()"
                placeholder="Search..."
                class="form-input pl-10"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
        </div>
    </div>
    @endif

    <!-- Desktop Table -->
    <div class="desktop-up">
        <div class="card">
            <div class="data-table-responsive">
                <table class="{{ $tableClasses }}" id="{{ $tableId }}">
                    <thead class="table-header">
                        <tr>
                            @foreach($headers as $header)
                            <th class="table-header-cell {{ $sortable ? 'sortable' : '' }}" 
                                @if($sortable) @click="sort('{{ $header['key'] ?? $loop->index }}')" @endif>
                                <div class="flex items-center justify-between">
                                    <span>{{ $header['label'] ?? $header }}</span>
                                    @if($sortable)
                                    <svg class="h-4 w-4 text-secondary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4" />
                                    </svg>
                                    @endif
                                </div>
                            </th>
                            @endforeach
                            @if(!empty($actions))
                            <th class="table-header-cell">Actions</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        <template x-for="(row, index) in filteredRows" :key="index">
                            <tr>
                                @foreach($headers as $headerIndex => $header)
                                <td class="table-cell">
                                    <span x-html="getCellValue(row, '{{ $header['key'] ?? $headerIndex }}')"></span>
                                </td>
                                @endforeach
                                @if(!empty($actions))
                                <td class="table-cell">
                                    <div class="flex items-center space-x-2">
                                        @foreach($actions as $action)
                                        @if($action['type'] === 'link')
                                        <a :href="getActionUrl('{{ $action['url'] }}', row)" 
                                           class="action-button-{{ $action['style'] ?? 'primary' }}">
                                            {{ $action['label'] }}
                                        </a>
                                        @elseif($action['type'] === 'button')
                                        <button @click="{{ $action['onclick'] ?? '' }}" 
                                                class="action-button-{{ $action['style'] ?? 'primary' }}">
                                            {{ $action['label'] }}
                                        </button>
                                        @endif
                                        @endforeach
                                    </div>
                                </td>
                                @endif
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Mobile Cards -->
    @if($mobileCards)
    <div class="mobile-tablet">
        <div class="mobile-card-list">
            <template x-for="(row, index) in filteredRows" :key="index">
                <div class="mobile-card-item">
                    <div class="card-body">
                        <div class="mobile-card-content">
                            @foreach($headers as $headerIndex => $header)
                            @if(!($header['hideOnMobile'] ?? false))
                            <div class="mobile-card-field">
                                <span class="mobile-card-label">{{ $header['label'] ?? $header }}</span>
                                <span class="mobile-card-value" x-html="getCellValue(row, '{{ $header['key'] ?? $headerIndex }}')"></span>
                            </div>
                            @endif
                            @endforeach
                            
                            @if(!empty($actions))
                            <div class="mt-4 pt-4 border-t border-secondary-200">
                                <div class="flex flex-wrap gap-2">
                                    @foreach($actions as $action)
                                    @if($action['type'] === 'link')
                                    <a :href="getActionUrl('{{ $action['url'] }}', row)" 
                                       class="btn-{{ $action['style'] ?? 'primary' }} btn-sm">
                                        {{ $action['label'] }}
                                    </a>
                                    @elseif($action['type'] === 'button')
                                    <button @click="{{ $action['onclick'] ?? '' }}" 
                                            class="btn-{{ $action['style'] ?? 'primary' }} btn-sm">
                                        {{ $action['label'] }}
                                    </button>
                                    @endif
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
    @endif

    <!-- Empty State -->
    <div x-show="filteredRows.length === 0" class="card">
        <div class="card-body text-center py-12">
            @if($emptyIcon)
            {!! $emptyIcon !!}
            @else
            <svg class="h-16 w-16 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            @endif
            <h3 class="text-lg font-semibold text-secondary-900 mb-2">{{ $emptyMessage }}</h3>
            <p class="text-secondary-600">
                @if($searchable && isset($searchTerm))
                No results match your search criteria.
                @else
                Data will appear here when available.
                @endif
            </p>
        </div>
    </div>

    @if($paginated)
    <!-- Pagination -->
    <div class="mt-6" x-show="totalPages > 1">
        <nav class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <button @click="previousPage()" 
                        :disabled="currentPage === 1"
                        class="btn-outline btn-sm"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                    Previous
                </button>
                <button @click="nextPage()" 
                        :disabled="currentPage === totalPages"
                        class="btn-outline btn-sm"
                        :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }">
                    Next
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-secondary-700">
                        Showing <span x-text="startItem"></span> to <span x-text="endItem"></span> of <span x-text="totalItems"></span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button @click="previousPage()" 
                                :disabled="currentPage === 1"
                                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                                :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        
                        <template x-for="page in visiblePages" :key="page">
                            <button @click="goToPage(page)"
                                    :class="page === currentPage ? 'bg-primary-50 border-primary-500 text-primary-600' : 'bg-white border-secondary-300 text-secondary-500 hover:bg-secondary-50'"
                                    class="relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        
                        <button @click="nextPage()" 
                                :disabled="currentPage === totalPages"
                                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-secondary-300 bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50"
                                :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }">
                            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </nav>
                </div>
            </div>
        </nav>
    </div>
    @endif
</div>

@push('scripts')
<script>
function dataTable() {
    return {
        rows: @json($rows),
        filteredRows: @json($rows),
        searchTerm: '',
        sortColumn: null,
        sortDirection: 'asc',
        currentPage: 1,
        itemsPerPage: 10,
        
        init() {
            this.filteredRows = this.rows;
        },
        
        filterRows() {
            if (!this.searchTerm) {
                this.filteredRows = this.rows;
                return;
            }
            
            const term = this.searchTerm.toLowerCase();
            this.filteredRows = this.rows.filter(row => {
                return Object.values(row).some(value => 
                    String(value).toLowerCase().includes(term)
                );
            });
            this.currentPage = 1;
        },
        
        sort(column) {
            if (this.sortColumn === column) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = column;
                this.sortDirection = 'asc';
            }
            
            this.filteredRows.sort((a, b) => {
                const aVal = this.getCellValue(a, column);
                const bVal = this.getCellValue(b, column);
                
                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        },
        
        getCellValue(row, key) {
            return row[key] || '';
        },
        
        getActionUrl(template, row) {
            return template.replace(/\{(\w+)\}/g, (match, key) => row[key] || '');
        },
        
        get totalPages() {
            return Math.ceil(this.filteredRows.length / this.itemsPerPage);
        },
        
        get totalItems() {
            return this.filteredRows.length;
        },
        
        get startItem() {
            return (this.currentPage - 1) * this.itemsPerPage + 1;
        },
        
        get endItem() {
            return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        },
        
        get visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
        },
        
        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },
        
        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },
        
        goToPage(page) {
            this.currentPage = page;
        }
    }
}
</script>
@endpush

@push('styles')
<style>
.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-striped tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-compact .table-cell,
.table-compact .table-header-cell {
    padding: 8px 12px;
}

@media (max-width: 768px) {
    .data-table-responsive {
        font-size: 14px;
    }
    
    .mobile-card-field {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .mobile-card-field:last-child {
        border-bottom: none;
    }
    
    .mobile-card-label {
        font-weight: 500;
        color: #6b7280;
        font-size: 14px;
    }
    
    .mobile-card-value {
        font-weight: 500;
        color: #111827;
        font-size: 14px;
        text-align: right;
        max-width: 60%;
        word-break: break-word;
    }
}
</style>
@endpush
