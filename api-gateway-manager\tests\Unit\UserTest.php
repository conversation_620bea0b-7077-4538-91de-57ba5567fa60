<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_has_default_role_developer()
    {
        $user = User::factory()->create();
        
        $this->assertEquals('developer', $user->role);
        $this->assertTrue($user->isDeveloper());
        $this->assertFalse($user->isAdmin());
    }

    public function test_user_can_be_admin()
    {
        $user = User::factory()->create(['role' => 'admin']);
        
        $this->assertEquals('admin', $user->role);
        $this->assertTrue($user->isAdmin());
        $this->assertFalse($user->isDeveloper());
    }

    public function test_user_can_be_developer()
    {
        $user = User::factory()->create(['role' => 'developer']);
        
        $this->assertEquals('developer', $user->role);
        $this->assertTrue($user->isDeveloper());
        $this->assertFalse($user->isAdmin());
    }

    public function test_user_has_apps_relationship()
    {
        $user = User::factory()->create();
        $app = App::factory()->create(['user_id' => $user->id]);
        
        $this->assertTrue($user->apps()->exists());
        $this->assertEquals(1, $user->apps()->count());
        $this->assertEquals($app->id, $user->apps()->first()->id);
    }

    public function test_user_can_have_multiple_apps()
    {
        $user = User::factory()->create();
        App::factory()->count(3)->create(['user_id' => $user->id]);
        
        $this->assertEquals(3, $user->apps()->count());
    }

    public function test_user_factory_creates_valid_user()
    {
        $user = User::factory()->create();
        
        $this->assertNotNull($user->name);
        $this->assertNotNull($user->email);
        $this->assertNotNull($user->password);
        $this->assertNotNull($user->email_verified_at);
        $this->assertEquals('developer', $user->role);
        $this->assertTrue($user->is_active);
    }

    public function test_user_factory_can_create_unverified_user()
    {
        $user = User::factory()->unverified()->create();
        
        $this->assertNull($user->email_verified_at);
    }

    public function test_user_casts_attributes_correctly()
    {
        $user = User::factory()->create([
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $user->email_verified_at);
        $this->assertIsBool($user->is_active);
    }

    public function test_user_hidden_attributes()
    {
        $user = User::factory()->create();
        $array = $user->toArray();
        
        $this->assertArrayNotHasKey('password', $array);
        $this->assertArrayNotHasKey('remember_token', $array);
    }

    public function test_user_fillable_attributes()
    {
        $data = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'admin',
            'is_active' => false,
        ];
        
        $user = User::factory()->create($data);
        
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('admin', $user->role);
        $this->assertFalse($user->is_active);
    }
}
