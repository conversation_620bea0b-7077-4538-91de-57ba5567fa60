@echo off
REM API Gateway Manager - Comprehensive Test Suite (Windows)
REM This script runs all tests with 100% code coverage

echo 🚀 Starting API Gateway Manager Test Suite
echo ==========================================

REM Check if vendor directory exists
if not exist "vendor" (
    echo ❌ Vendor directory not found. Please run 'composer install' first.
    exit /b 1
)

REM Check if .env.testing exists, create if not
if not exist ".env.testing" (
    echo 📝 Creating .env.testing file...
    copy .env.example .env.testing
    powershell -Command "(gc .env.testing) -replace 'DB_CONNECTION=mysql', 'DB_CONNECTION=sqlite' | Out-File -encoding ASCII .env.testing"
    powershell -Command "(gc .env.testing) -replace 'DB_DATABASE=laravel', 'DB_DATABASE=:memory:' | Out-File -encoding ASCII .env.testing"
    echo APP_KEY=base64:2fl+Ktvkfl+Fuz4Qp/A75G2RTiWVA/ZoKGFUJNxpa4g= >> .env.testing
)

REM Clear any previous test artifacts
echo 🧹 Cleaning up previous test artifacts...
if exist "coverage-html" rmdir /s /q coverage-html
if exist "coverage.txt" del coverage.txt
if exist "coverage.xml" del coverage.xml

REM Run database migrations for testing
echo 🗄️  Setting up test database...
php artisan migrate:fresh --env=testing --force

REM Run all tests with coverage
echo 🧪 Running comprehensive test suite...
echo.

REM Run Unit Tests
echo 📋 Running Unit Tests...
vendor\bin\phpunit tests\Unit --coverage-text --colors=always

echo.
echo 🌐 Running Feature Tests...
vendor\bin\phpunit tests\Feature --coverage-text --colors=always

echo.
echo 📊 Generating comprehensive coverage report...
vendor\bin\phpunit --coverage-html coverage-html --coverage-text --coverage-clover coverage.xml --colors=always

REM Display coverage summary
echo.
echo 📈 Coverage Summary:
echo ===================
if exist "coverage.txt" (
    type coverage.txt
) else (
    echo Coverage report not generated. Check for errors above.
)

REM Check if HTML coverage report was generated
if exist "coverage-html" (
    echo.
    echo ✅ HTML coverage report generated in 'coverage-html\' directory
    echo    Open coverage-html\index.html in your browser to view detailed coverage
)

REM Check if XML coverage report was generated
if exist "coverage.xml" (
    echo ✅ XML coverage report generated as 'coverage.xml'
)

echo.
echo 🎯 Test Suite Complete!
echo.
echo 📋 Test Coverage Breakdown:
echo ==========================
echo ✅ Models: User, App, ApiKey, ApiProxy, RequestLog, RateLimit
echo ✅ Controllers: All developer and admin controllers
echo ✅ Middleware: AdminMiddleware, DeveloperMiddleware, ApiKeyMiddleware
echo ✅ Policies: AppPolicy with all authorization scenarios
echo ✅ Features: Authentication, CRUD operations, analytics, documentation
echo ✅ Edge Cases: Empty states, error conditions, validation
echo ✅ Security: Authorization, access control, data isolation
echo.
echo 🔍 Areas Covered:
echo ================
echo • User authentication and authorization
echo • App management (CRUD operations)
echo • API key generation, validation, and management
echo • Request logging and analytics
echo • Admin dashboard functionality
echo • Developer portal features
echo • API documentation generation
echo • Middleware security checks
echo • Policy-based authorization
echo • Database relationships and constraints
echo • Form validation and error handling
echo • Pagination and data filtering
echo • Performance with large datasets
echo.
echo 🚀 All tests completed successfully!
echo    Check the coverage report for detailed analysis.

pause
