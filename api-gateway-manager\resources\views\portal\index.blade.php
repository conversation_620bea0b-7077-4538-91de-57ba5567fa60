@extends('layouts.guest')

@section('title', 'API Gateway Manager - Secure, Scalable API Management')
@section('meta_description', 'Powerful API gateway solution for modern applications. Manage API keys, rate limiting, analytics, and more with our developer-friendly platform.')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-br from-primary-600 to-primary-800 text-white">
    <div class="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8 lg:py-24">
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
                <span class="block">Powerful API</span>
                <span class="block text-primary-200">Management Platform</span>
            </h1>
            <p class="mt-6 max-w-2xl mx-auto text-xl text-primary-100">
                Secure, scalable, and simple API gateway solution for modern applications. 
                Manage your APIs with confidence.
            </p>
            <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('register') }}" class="btn-primary btn-lg bg-white text-primary-600 hover:bg-primary-50">
                    Get Started Free
                </a>
                <a href="{{ route('docs.index') }}" class="btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary-600">
                    View Documentation
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-secondary-900 sm:text-4xl">
                Everything you need to manage APIs
            </h2>
            <p class="mt-4 text-lg text-secondary-600">
                Built for developers, designed for scale
            </p>
        </div>

        <div class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Feature 1 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">API Key Management</h3>
                    <p class="text-secondary-600">
                        Generate, manage, and secure API keys with advanced authentication and authorization controls.
                    </p>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">Analytics & Monitoring</h3>
                    <p class="text-secondary-600">
                        Real-time analytics, usage tracking, and performance monitoring for all your APIs.
                    </p>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">Rate Limiting</h3>
                    <p class="text-secondary-600">
                        Protect your APIs with configurable rate limiting and quota management.
                    </p>
                </div>
            </div>

            <!-- Feature 4 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-danger-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-danger-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">Security First</h3>
                    <p class="text-secondary-600">
                        Enterprise-grade security with encryption, validation, and threat detection.
                    </p>
                </div>
            </div>

            <!-- Feature 5 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">Developer Portal</h3>
                    <p class="text-secondary-600">
                        Self-service portal for developers with documentation, testing tools, and analytics.
                    </p>
                </div>
            </div>

            <!-- Feature 6 -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">High Performance</h3>
                    <p class="text-secondary-600">
                        Optimized for speed and scalability with efficient request routing and caching.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="bg-secondary-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-secondary-900 mb-8">
                Trusted by developers worldwide
            </h2>
        </div>
        
        <div class="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div class="text-center">
                <div class="text-3xl font-bold text-primary-600">{{ $stats['total_apis'] ?? '10+' }}</div>
                <div class="text-sm text-secondary-600 mt-1">Active APIs</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-primary-600">{{ $stats['total_developers'] ?? '50+' }}</div>
                <div class="text-sm text-secondary-600 mt-1">Developers</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-primary-600">99.9%</div>
                <div class="text-sm text-secondary-600 mt-1">Uptime</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-primary-600">24/7</div>
                <div class="text-sm text-secondary-600 mt-1">Support</div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="bg-primary-600">
    <div class="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-white sm:text-4xl">
                Ready to get started?
            </h2>
            <p class="mt-4 text-lg text-primary-100">
                Join thousands of developers using our API management platform
            </p>
            <div class="mt-8">
                <a href="{{ route('register') }}" class="btn-primary btn-lg bg-white text-primary-600 hover:bg-primary-50">
                    Start Free Trial
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
