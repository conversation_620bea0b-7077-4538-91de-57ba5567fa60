<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ApiProxy;
use App\Models\User;

class PortalController extends Controller
{
    /**
     * Show the developer portal home page
     */
    public function index()
    {
        $stats = [
            'total_apis' => ApiProxy::where('is_active', true)->count(),
            'total_developers' => User::where('role', 'developer')->where('is_active', true)->count(),
        ];

        $featuredApis = ApiProxy::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->take(6)
            ->get();

        return view('portal.index', compact('stats', 'featuredApis'));
    }

    /**
     * Show the getting started guide
     */
    public function gettingStarted()
    {
        return view('portal.getting-started');
    }

    /**
     * Show the API reference
     */
    public function apiReference()
    {
        $apiProxies = ApiProxy::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('portal.api-reference', compact('apiProxies'));
    }

    /**
     * Show pricing information
     */
    public function pricing()
    {
        return view('portal.pricing');
    }

    /**
     * Show support information
     */
    public function support()
    {
        return view('portal.support');
    }

    public function submitSupport(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'subject' => 'required|string|max:255',
            'priority' => 'nullable|string|in:low,medium,high,urgent',
            'message' => 'required|string|max:5000',
        ]);

        // Here you would typically save to database or send email
        // For now, we'll just redirect back with success message

        return redirect()->route('portal.support')->with('success', 'Thank you for your message! We\'ll get back to you within 2 hours.');
    }
}
