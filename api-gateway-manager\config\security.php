<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related configuration options for the
    | API Gateway Manager application.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API Key Settings
    |--------------------------------------------------------------------------
    */
    'api_keys' => [
        'length' => 32,
        'prefix' => 'agm_',
        'hash_algorithm' => 'sha256',
        'expiry_days' => null, // null = no expiry by default
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => env('API_RATE_LIMIT_ENABLED', true),
        'defaults' => [
            'requests_per_minute' => env('API_RATE_LIMIT_DEFAULT_PER_MINUTE', 60),
            'requests_per_hour' => env('API_RATE_LIMIT_DEFAULT_PER_HOUR', 1000),
            'requests_per_day' => env('API_RATE_LIMIT_DEFAULT_PER_DAY', 10000),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Request Validation
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'max_request_size' => 10 * 1024 * 1024, // 10MB
        'allowed_content_types' => [
            'application/json',
            'application/x-www-form-urlencoded',
            'multipart/form-data',
        ],
        'max_header_length' => 1000,
        'max_header_name_length' => 100,
    ],

    /*
    |--------------------------------------------------------------------------
    | Request Logging
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('REQUEST_LOGGING_ENABLED', true),
        'retention_days' => env('REQUEST_LOGGING_RETENTION_DAYS', 30),
        'log_headers' => true,
        'log_body' => false, // Don't log request/response bodies for privacy
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    */
    'headers' => [
        'enabled' => env('SECURE_HEADERS_ENABLED', true),
        'hsts_max_age' => 31536000, // 1 year
        'csp_policy' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
            'style-src' => "'self' 'unsafe-inline' https://cdn.jsdelivr.net",
            'img-src' => "'self' data: https:",
            'font-src' => "'self' https://cdn.jsdelivr.net",
            'connect-src' => "'self'",
            'frame-ancestors' => "'none'",
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | CORS Settings
    |--------------------------------------------------------------------------
    */
    'cors' => [
        'enabled' => env('CORS_ENABLED', true),
        'allowed_origins' => explode(',', env('CORS_ALLOWED_ORIGINS', '*')),
        'allowed_methods' => explode(',', env('CORS_ALLOWED_METHODS', 'GET,POST,PUT,PATCH,DELETE,OPTIONS')),
        'allowed_headers' => explode(',', env('CORS_ALLOWED_HEADERS', 'Content-Type,X-API-Key,Authorization,X-Requested-With')),
        'max_age' => 86400, // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Filtering
    |--------------------------------------------------------------------------
    */
    'ip_filtering' => [
        'enabled' => env('IP_FILTERING_ENABLED', false),
        'whitelist' => explode(',', env('IP_WHITELIST', '')),
        'blacklist' => explode(',', env('IP_BLACKLIST', '')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Suspicious Activity Detection
    |--------------------------------------------------------------------------
    */
    'threat_detection' => [
        'enabled' => true,
        'patterns' => [
            'xss' => [
                '/<script[^>]*>.*?<\/script>/i',
                '/javascript:/i',
                '/vbscript:/i',
                '/onload\s*=/i',
                '/onerror\s*=/i',
                '/onclick\s*=/i',
            ],
            'sql_injection' => [
                '/union\s+select/i',
                '/drop\s+table/i',
                '/delete\s+from/i',
                '/insert\s+into/i',
                '/update\s+.*set/i',
                '/exec\s*\(/i',
            ],
            'path_traversal' => [
                '/\.\.\//i',
                '/\.\.\\\/i',
                '/%2e%2e%2f/i',
                '/%2e%2e%5c/i',
            ],
        ],
        'max_violations_per_hour' => 10,
        'block_duration_minutes' => 60,
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption Settings
    |--------------------------------------------------------------------------
    */
    'encryption' => [
        'api_key_encryption' => true,
        'log_encryption' => false,
        'sensitive_fields' => [
            'password',
            'api_key',
            'secret',
            'token',
            'authorization',
        ],
    ],

];
