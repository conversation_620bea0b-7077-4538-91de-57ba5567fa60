@extends('layouts.developer')

@php
    $pageTitle = 'Edit App';
@endphp

@section('title', 'Edit ' . $app->name . ' - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('developer.apps.show', $app) }}" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                    Edit Application
                </h1>
                <p class="text-secondary-600">{{ $app->name }}</p>
            </div>
        </div>
        <p class="text-secondary-600">
            Update the configuration and settings for your application.
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <form method="POST" action="{{ route('developer.apps.update', $app) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- App Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                    Application Name <span class="text-danger-500">*</span>
                </label>
                <input type="text"
                       id="name"
                       name="name"
                       required
                       value="{{ old('name', $app->name) }}"
                       class="form-input @error('name') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Enter a descriptive name for your app">
                @error('name')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Choose a clear, descriptive name that identifies your application.
                </p>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                    Description
                </label>
                <textarea id="description"
                          name="description"
                          rows="4"
                          class="form-textarea @error('description') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                          placeholder="Describe what your application does and how it will use the API...">{{ old('description', $app->description) }}</textarea>
                @error('description')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Provide a brief description of your application and its purpose.
                </p>
            </div>

            <!-- Callback URL -->
            <div>
                <label for="callback_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Callback URL
                </label>
                <input type="url"
                       id="callback_url"
                       name="callback_url"
                       value="{{ old('callback_url', $app->callback_url) }}"
                       class="form-input @error('callback_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="https://your-app.com/callback">
                @error('callback_url')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. URL where users will be redirected after authentication (if applicable).
                </p>
            </div>

            <!-- Base URL -->
            <div>
                <label for="base_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Base URL
                </label>
                <input type="url"
                       id="base_url"
                       name="base_url"
                       value="{{ old('base_url', $app->base_url) }}"
                       class="form-input @error('base_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="https://api.your-service.com">
                @error('base_url')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Base URL for your API endpoints. This will be prepended to relative endpoint URLs.
                </p>
            </div>

            <!-- Authorization Token -->
            <div>
                <label for="authorization_token" class="block text-sm font-medium text-secondary-700 mb-2">
                    Authorization Token
                </label>
                <input type="text"
                       id="authorization_token"
                       name="authorization_token"
                       value="{{ old('authorization_token', $app->authorization_token) }}"
                       class="form-input @error('authorization_token') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Bearer your-token-here">
                @error('authorization_token')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Authorization token to be automatically added to all endpoint requests.
                </p>
            </div>

            <!-- Custom Headers -->
            <div x-data="{ headers: {{ $app->custom_headers ? json_encode(array_map(fn($value, $key) => ['name' => $key, 'value' => $value], $app->custom_headers, array_keys($app->custom_headers))) : '[]' }} }">
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                    Custom Headers
                </label>
                <div class="space-y-3">
                    <template x-for="(header, index) in headers" :key="index">
                        <div class="flex gap-3 items-start">
                            <div class="flex-1">
                                <input type="text"
                                       :name="`custom_headers[${index}][name]`"
                                       x-model="header.name"
                                       class="form-input"
                                       placeholder="Header name (e.g., X-API-Version)">
                            </div>
                            <div class="flex-1">
                                <input type="text"
                                       :name="`custom_headers[${index}][value]`"
                                       x-model="header.value"
                                       class="form-input"
                                       placeholder="Header value">
                            </div>
                            <button type="button"
                                    @click="headers.splice(index, 1)"
                                    class="btn-sm btn-outline text-danger-600 hover:text-danger-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button type="button"
                            @click="headers.push({ name: '', value: '' })"
                            class="btn-sm btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Header
                    </button>
                </div>
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Custom headers to be automatically added to all endpoint requests.
                </p>
            </div>

            <!-- App Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox"
                           id="is_active"
                           name="is_active"
                           value="1"
                           {{ old('is_active', $app->is_active) ? 'checked' : '' }}
                           class="form-checkbox">
                    <label for="is_active" class="ml-2 block text-sm text-secondary-700">
                        Active application
                    </label>
                </div>
                <p class="mt-2 text-sm text-secondary-500">
                    Active applications can generate and use API keys. Inactive applications cannot make API calls.
                </p>
            </div>

            <!-- Important Notes -->
            <div class="bg-info-50 border border-info-200 rounded-lg p-4">
                <div class="flex">
                    <svg class="h-5 w-5 text-info-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-info-800">Important Notes</h3>
                        <div class="mt-2 text-sm text-info-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Deactivating this app will immediately stop all API requests</li>
                                <li>All API keys associated with this app will also be disabled</li>
                                <li>You can reactivate the app at any time</li>
                                <li>Analytics and logs will be preserved</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-secondary-200">
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Update Application
                </button>
                <a href="{{ route('developer.apps.show', $app) }}" class="btn-outline">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- App Statistics -->
    <div class="mt-12 max-w-2xl">
        <div class="card">
            <div class="card-header">
                <h2 class="text-lg font-semibold text-secondary-900">App Statistics</h2>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-600">{{ $app->apiKeys->count() }}</div>
                        <div class="text-sm text-secondary-600">API Keys</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-success-600">
                            {{ number_format($app->apiKeys->sum(function($key) { return $key->requestLogs->count(); })) }}
                        </div>
                        <div class="text-sm text-secondary-600">Total Requests</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-warning-600">
                            @php
                                $totalRequests = $app->apiKeys->sum(function($key) { return $key->requestLogs->count(); });
                                $successfulRequests = $app->apiKeys->sum(function($key) {
                                    return $key->requestLogs->whereBetween('response_status', [200, 299])->count();
                                });
                                $successRate = $totalRequests > 0 ? round(($successfulRequests / $totalRequests) * 100, 1) : 100;
                            @endphp
                            {{ $successRate }}%
                        </div>
                        <div class="text-sm text-secondary-600">Success Rate</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-info-600">
                            {{ $app->apiKeys->where('is_active', true)->count() }}
                        </div>
                        <div class="text-sm text-secondary-600">Active Keys</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mt-8 max-w-2xl">
        <div class="card border-danger-200">
            <div class="card-header bg-danger-50">
                <h3 class="text-lg font-medium text-danger-900">Danger Zone</h3>
            </div>
            <div class="card-body">
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                    <div>
                        <h4 class="text-sm font-medium text-secondary-900">Delete Application</h4>
                        <p class="text-sm text-secondary-600 mt-1">
                            Permanently delete this application and all associated API keys. This action cannot be undone and will immediately stop all API requests.
                        </p>
                    </div>
                    <form action="{{ route('developer.apps.destroy', $app) }}" method="POST" class="flex-shrink-0">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-danger" onclick="return confirm('Are you sure you want to delete this application? This action cannot be undone and will delete all associated API keys.')">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Application
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
</div>
@endsection
