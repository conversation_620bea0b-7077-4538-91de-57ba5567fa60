@extends('layouts.developer')

@php
    $pageTitle = 'Edit App';
@endphp

@section('title', 'Edit ' . $app->name . ' - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Edit App</h1>
                <p class="mt-2 text-secondary-600">Update settings for {{ $app->name }}</p>
            </div>
            <a href="{{ route('developer.apps.show', $app) }}" class="btn-outline">
                Back to App
            </a>
        </div>
    </div>

    <div class="max-w-3xl">
        <!-- Edit Form -->
        <div class="card">
            <div class="card-body">
                <form action="{{ route('developer.apps.update', $app) }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="form-group">
                        <label for="name" class="form-label">App Name *</label>
                        <input type="text" name="name" id="name" class="form-input" value="{{ old('name', $app->name) }}" required>
                        <p class="form-help">A descriptive name for your application</p>
                        @error('name')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" rows="3" class="form-input" placeholder="Describe what this app does...">{{ old('description', $app->description) }}</textarea>
                        <p class="form-help">Optional description to help you identify this app</p>
                        @error('description')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="1" class="form-radio" {{ old('is_active', $app->is_active) ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-secondary-700">Active - App can receive API requests</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="is_active" value="0" class="form-radio" {{ !old('is_active', $app->is_active) ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-secondary-700">Inactive - App is disabled</span>
                            </label>
                        </div>
                        @error('is_active')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-blue-800">Important Notes</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Deactivating this app will immediately stop all API requests</li>
                                        <li>All API keys associated with this app will also be disabled</li>
                                        <li>You can reactivate the app at any time</li>
                                        <li>Analytics and logs will be preserved</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <a href="{{ route('developer.apps.show', $app) }}" class="btn-outline">
                            Cancel
                        </a>
                        <button type="submit" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Update App
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- App Statistics -->
        <div class="mt-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="text-lg font-semibold text-secondary-900">App Statistics</h2>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-600">{{ $app->apiKeys->count() }}</div>
                            <div class="text-sm text-secondary-600">API Keys</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-success-600">
                                {{ number_format($app->apiKeys->sum(function($key) { return $key->requestLogs->count(); })) }}
                            </div>
                            <div class="text-sm text-secondary-600">Total Requests</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-warning-600">
                                @php
                                    $totalRequests = $app->apiKeys->sum(function($key) { return $key->requestLogs->count(); });
                                    $successfulRequests = $app->apiKeys->sum(function($key) { 
                                        return $key->requestLogs->whereBetween('response_status', [200, 299])->count(); 
                                    });
                                    $successRate = $totalRequests > 0 ? round(($successfulRequests / $totalRequests) * 100, 1) : 100;
                                @endphp
                                {{ $successRate }}%
                            </div>
                            <div class="text-sm text-secondary-600">Success Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-danger-600">
                                {{ $app->apiKeys->where('is_active', true)->count() }}
                            </div>
                            <div class="text-sm text-secondary-600">Active Keys</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Danger Zone -->
        <div class="mt-8">
            <div class="card border-danger-200">
                <div class="card-header bg-danger-50">
                    <h3 class="text-lg font-medium text-danger-900">Danger Zone</h3>
                </div>
                <div class="card-body">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="text-sm font-medium text-secondary-900">Delete App</h4>
                            <p class="text-sm text-secondary-600 mt-1">
                                Permanently delete this app and all associated API keys. This action cannot be undone and will immediately stop all API requests.
                            </p>
                        </div>
                        <form action="{{ route('developer.apps.destroy', $app) }}" method="POST" class="ml-4">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-danger" onclick="return confirm('Are you sure you want to delete this app? This action cannot be undone and will delete all associated API keys.')">
                                Delete App
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
