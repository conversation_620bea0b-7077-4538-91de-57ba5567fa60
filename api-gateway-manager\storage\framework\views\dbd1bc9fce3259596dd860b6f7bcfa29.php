<?php $__env->startSection('title', '404 - Page Not Found'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Illustration -->
        <div class="mx-auto">
            <svg class="h-32 w-32 sm:h-40 sm:w-40 text-primary-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33l-.147-.15C7.07 11.46 9.4 10 12 10c2.6 0 4.93 1.46 6.227 2.52l-.147.15A7.962 7.962 0 0112 15z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
        </div>

        <!-- Error Code -->
        <div>
            <h1 class="text-6xl sm:text-8xl font-bold text-secondary-900">404</h1>
            <h2 class="mt-4 text-2xl sm:text-3xl font-bold text-secondary-900">Page Not Found</h2>
            <p class="mt-4 text-lg text-secondary-600">
                Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you entered the wrong URL.
            </p>
        </div>

        <!-- Actions -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="history.back()" class="w-full sm:w-auto btn-outline">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
            
            <?php if(auth()->guard()->check()): ?>
                <?php if(auth()->user()->role === 'admin'): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="w-full sm:w-auto btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Admin Dashboard
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('developer.dashboard')); ?>" class="w-full sm:w-auto btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Dashboard
                    </a>
                <?php endif; ?>
            <?php else: ?>
                <a href="<?php echo e(route('portal.index')); ?>" class="w-full sm:w-auto btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Home
                </a>
            <?php endif; ?>
        </div>

        <!-- Help Links -->
        <div class="pt-8 border-t border-secondary-200">
            <p class="text-sm text-secondary-500 mb-4">Need help? Try these resources:</p>
            <div class="flex flex-col sm:flex-row sm:justify-center space-y-2 sm:space-y-0 sm:space-x-6">
                <a href="<?php echo e(route('docs.index')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                    📚 Documentation
                </a>
                <a href="<?php echo e(route('portal.support')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                    💬 Support
                </a>
                <a href="<?php echo e(route('portal.getting-started')); ?>" class="text-sm text-primary-600 hover:text-primary-500">
                    🚀 Getting Started
                </a>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Track 404 errors for analytics
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_not_found', {
        'page_location': window.location.href,
        'page_referrer': document.referrer
    });
}

// Auto-redirect after 30 seconds if user is inactive
let redirectTimer;
let userActive = false;

function resetTimer() {
    userActive = true;
    clearTimeout(redirectTimer);
}

function startRedirectTimer() {
    if (!userActive) {
        redirectTimer = setTimeout(() => {
            <?php if(auth()->guard()->check()): ?>
                <?php if(auth()->user()->role === 'admin'): ?>
                    window.location.href = '<?php echo e(route("admin.dashboard")); ?>';
                <?php else: ?>
                    window.location.href = '<?php echo e(route("developer.dashboard")); ?>';
                <?php endif; ?>
            <?php else: ?>
                window.location.href = '<?php echo e(route("portal.index")); ?>';
            <?php endif; ?>
        }, 30000);
    }
}

// Listen for user activity
document.addEventListener('mousemove', resetTimer);
document.addEventListener('keypress', resetTimer);
document.addEventListener('click', resetTimer);
document.addEventListener('scroll', resetTimer);

// Start the timer
setTimeout(startRedirectTimer, 1000);
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/errors/404.blade.php ENDPATH**/ ?>