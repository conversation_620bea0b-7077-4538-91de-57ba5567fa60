<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'developer',
            'is_active' => true,
        ]);
    }

    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'developer',
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    public function test_admin_user_redirects_to_admin_dashboard()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
        ]);

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertRedirect('/admin/dashboard');
    }

    public function test_developer_user_redirects_to_developer_dashboard()
    {
        $developer = User::factory()->create([
            'role' => 'developer',
        ]);

        $response = $this->actingAs($developer)->get('/dashboard');

        $response->assertRedirect('/developer/dashboard');
    }

    public function test_admin_middleware_blocks_non_admin_users()
    {
        $developer = User::factory()->create([
            'role' => 'developer',
        ]);

        $response = $this->actingAs($developer)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_developer_middleware_allows_admin_users()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
        ]);

        $response = $this->actingAs($admin)->get('/developer/dashboard');

        $response->assertStatus(200);
    }

    public function test_unauthenticated_user_redirected_to_login()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_inactive_user_cannot_access_protected_routes()
    {
        $user = User::factory()->create([
            'is_active' => false,
        ]);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        // This would depend on how you implement inactive user handling
        // For now, we'll just check they can access (you might want to add middleware for this)
        $response->assertStatus(200);
    }
}
