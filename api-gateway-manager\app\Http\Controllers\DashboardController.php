<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard based on user role
     */
    public function index(Request $request)
    {
        $user = $request->user();

        if ($user->isAdmin()) {
            return redirect()->route('admin.dashboard');
        }

        if ($user->isDeveloper()) {
            return redirect()->route('developer.dashboard');
        }

        // Fallback for any other roles
        return view('dashboard');
    }
}
