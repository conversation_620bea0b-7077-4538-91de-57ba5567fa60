<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\DeveloperMiddleware;
use App\Http\Middleware\ApiKeyMiddleware;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class MiddlewareTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_middleware_allows_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $request = Request::create('/admin/test');
        
        $middleware = new AdminMiddleware();
        
        $this->actingAs($admin);
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_admin_middleware_blocks_non_admin_users()
    {
        $developer = User::factory()->create(['role' => 'developer']);
        $request = Request::create('/admin/test');
        
        $middleware = new AdminMiddleware();
        
        $this->actingAs($developer);
        
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Access denied. Administrator privileges required.');
        
        $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
    }

    public function test_admin_middleware_redirects_guests()
    {
        $request = Request::create('/admin/test');
        
        $middleware = new AdminMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('/login', $response->headers->get('Location'));
    }

    public function test_developer_middleware_allows_developer_users()
    {
        $developer = User::factory()->create(['role' => 'developer']);
        $request = Request::create('/developer/test');
        
        $middleware = new DeveloperMiddleware();
        
        $this->actingAs($developer);
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_developer_middleware_allows_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $request = Request::create('/developer/test');
        
        $middleware = new DeveloperMiddleware();
        
        $this->actingAs($admin);
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_developer_middleware_blocks_regular_users()
    {
        $user = User::factory()->create(['role' => 'user']);
        $request = Request::create('/developer/test');
        
        $middleware = new DeveloperMiddleware();
        
        $this->actingAs($user);
        
        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Access denied. Developer privileges required.');
        
        $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
    }

    public function test_developer_middleware_redirects_guests()
    {
        $request = Request::create('/developer/test');
        
        $middleware = new DeveloperMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertStringContains('/login', $response->headers->get('Location'));
    }

    public function test_api_key_middleware_validates_header_authentication()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => true]);
        $keyValue = ApiKey::generateKey();
        $apiKey->update([
            'key_hash' => ApiKey::hashKey($keyValue),
            'key_prefix' => ApiKey::getKeyPrefix($keyValue)
        ]);
        
        $request = Request::create('/api/test');
        $request->headers->set('X-API-Key', $keyValue);
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals('Success', $response->getContent());
        
        // Check that API key was marked as used
        $apiKey->refresh();
        $this->assertNotNull($apiKey->last_used_at);
    }

    public function test_api_key_middleware_validates_query_parameter_authentication()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => true]);
        $keyValue = ApiKey::generateKey();
        $apiKey->update([
            'key_hash' => ApiKey::hashKey($keyValue),
            'key_prefix' => ApiKey::getKeyPrefix($keyValue)
        ]);
        
        $request = Request::create('/api/test', 'GET', ['api_key' => $keyValue]);
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_api_key_middleware_rejects_invalid_key()
    {
        $request = Request::create('/api/test');
        $request->headers->set('X-API-Key', 'invalid_key');
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContains('Invalid API key', $response->getContent());
    }

    public function test_api_key_middleware_rejects_missing_key()
    {
        $request = Request::create('/api/test');
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContains('API key required', $response->getContent());
    }

    public function test_api_key_middleware_rejects_inactive_key()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => false]);
        $keyValue = ApiKey::generateKey();
        $apiKey->update([
            'key_hash' => ApiKey::hashKey($keyValue),
            'key_prefix' => ApiKey::getKeyPrefix($keyValue)
        ]);
        
        $request = Request::create('/api/test');
        $request->headers->set('X-API-Key', $keyValue);
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContains('API key is inactive', $response->getContent());
    }

    public function test_api_key_middleware_rejects_expired_key()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create([
            'app_id' => $app->id,
            'is_active' => true,
            'expires_at' => now()->subDay()
        ]);
        $keyValue = ApiKey::generateKey();
        $apiKey->update([
            'key_hash' => ApiKey::hashKey($keyValue),
            'key_prefix' => ApiKey::getKeyPrefix($keyValue)
        ]);
        
        $request = Request::create('/api/test');
        $request->headers->set('X-API-Key', $keyValue);
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContains('API key has expired', $response->getContent());
    }

    public function test_api_key_middleware_rejects_inactive_app()
    {
        $app = App::factory()->create(['is_active' => false]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'is_active' => true]);
        $keyValue = ApiKey::generateKey();
        $apiKey->update([
            'key_hash' => ApiKey::hashKey($keyValue),
            'key_prefix' => ApiKey::getKeyPrefix($keyValue)
        ]);
        
        $request = Request::create('/api/test');
        $request->headers->set('X-API-Key', $keyValue);
        
        $middleware = new ApiKeyMiddleware();
        
        $response = $middleware->handle($request, function ($req) {
            return new Response('Success');
        });
        
        $this->assertEquals(401, $response->getStatusCode());
        $this->assertStringContains('Application is inactive', $response->getContent());
    }
}
