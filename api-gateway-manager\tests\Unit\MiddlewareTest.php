<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\DeveloperMiddleware;
use App\Http\Middleware\ApiKeyMiddleware;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

class MiddlewareTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_middleware_allows_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
    }

    public function test_admin_middleware_blocks_non_admin_users()
    {
        $developer = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($developer)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_admin_middleware_redirects_guests()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_developer_middleware_allows_developer_users()
    {
        $developer = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($developer)->get('/developer/dashboard');

        $response->assertStatus(200);
    }

    public function test_developer_middleware_allows_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/developer/dashboard');

        $response->assertStatus(200);
    }

    public function test_developer_middleware_blocks_regular_users()
    {
        // Since we only have admin and developer roles, we'll test with a different approach
        // We'll test that unauthenticated users are redirected
        $response = $this->get('/developer/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_developer_middleware_redirects_guests()
    {
        $response = $this->get('/developer/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_api_key_middleware_exists()
    {
        $middleware = new ApiKeyMiddleware();
        $this->assertInstanceOf(ApiKeyMiddleware::class, $middleware);
    }

    public function test_middleware_classes_exist()
    {
        $this->assertTrue(class_exists(AdminMiddleware::class));
        $this->assertTrue(class_exists(DeveloperMiddleware::class));
        $this->assertTrue(class_exists(ApiKeyMiddleware::class));
    }
}
