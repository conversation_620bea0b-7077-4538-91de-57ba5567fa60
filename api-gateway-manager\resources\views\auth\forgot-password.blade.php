@extends('layouts.guest')

@section('title', 'Reset Password - ' . config('app.name'))
@section('meta_description', 'Reset your API Gateway Manager password. Enter your email to receive a password reset link.')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Reset your password
        </h2>
        <p class="mt-2 text-sm text-secondary-600">
            Enter your email address and we'll send you a link to reset your password
        </p>
    </div>

    <!-- Session Status -->
    @if (session('status'))
        <div class="alert-success">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-success-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                {{ session('status') }}
            </div>
        </div>
    @endif

    <!-- Reset Form -->
    <form method="POST" action="{{ route('password.email') }}" class="space-y-6">
        @csrf

        <!-- Email Address -->
        <div>
            <label for="email" class="block text-sm font-medium text-secondary-700 mb-2">
                Email address
            </label>
            <input id="email"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   autofocus
                   value="{{ old('email') }}"
                   class="form-input @error('email') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                   placeholder="Enter your email address">
            @error('email')
                <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Submit Button -->
        <div>
            <button type="submit" class="btn-primary w-full">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Send reset link
            </button>
        </div>

        <!-- Back to login -->
        <div class="text-center">
            <a href="{{ route('login') }}" class="text-sm text-primary-600 hover:text-primary-500 font-medium flex items-center justify-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to sign in
            </a>
        </div>
    </form>
</div>
@endsection
