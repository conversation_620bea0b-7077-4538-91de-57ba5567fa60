<?php

namespace App\Http\Controllers\Developer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RequestLog;
use App\Models\ApiKey;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class AnalyticsController extends Controller
{


    /**
     * Show developer analytics dashboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        // Get API key IDs for this user's apps
        $apiKeyIds = $user->apps()->with('apiKeys')->get()
            ->pluck('apiKeys')->flatten()->pluck('id');

        if ($apiKeyIds->isEmpty()) {
            return view('developer.analytics.index', [
                'stats' => [],
                'hasData' => false,
                'days' => $days
            ]);
        }

        // Overall statistics for this developer
        $stats = [
            'total_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)->count(),
            'successful_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)
                ->where('response_status', '<', 400)->count(),
            'error_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)
                ->where('response_status', '>=', 400)->count(),
            'avg_response_time' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)->avg('response_time_ms'),
        ];

        // Requests by day
        $requestsByDay = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->selectRaw('DATE(requested_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Usage by API key
        $usageByApiKey = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->select('api_key_id')
            ->selectRaw('COUNT(*) as request_count')
            ->selectRaw('AVG(response_time_ms) as avg_response_time')
            ->with(['apiKey.app'])
            ->groupBy('api_key_id')
            ->orderBy('request_count', 'desc')
            ->get();

        // Status code distribution
        $statusCodes = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->selectRaw('
                CASE
                    WHEN response_status < 300 THEN "2xx Success"
                    WHEN response_status < 400 THEN "3xx Redirect"
                    WHEN response_status < 500 THEN "4xx Client Error"
                    ELSE "5xx Server Error"
                END as status_group,
                COUNT(*) as count
            ')
            ->groupBy('status_group')
            ->get();

        // Most used endpoints
        $topEndpoints = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->select('path')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('AVG(response_time_ms) as avg_response_time')
            ->groupBy('path')
            ->orderBy('count', 'desc')
            ->take(10)
            ->get();

        return view('developer.analytics.index', compact(
            'stats',
            'requestsByDay',
            'usageByApiKey',
            'statusCodes',
            'topEndpoints',
            'days'
        ))->with('hasData', true);
    }

    /**
     * Show analytics for a specific app
     */
    public function app(Request $request, $appId)
    {
        $user = Auth::user();
        $app = $user->apps()->findOrFail($appId);
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $apiKeyIds = $app->apiKeys->pluck('id');

        if ($apiKeyIds->isEmpty()) {
            return view('developer.analytics.app', [
                'app' => $app,
                'stats' => [],
                'hasData' => false,
                'days' => $days
            ]);
        }

        // App-specific statistics
        $stats = [
            'total_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)->count(),
            'successful_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)
                ->where('response_status', '<', 400)->count(),
            'error_requests' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)
                ->where('response_status', '>=', 400)->count(),
            'avg_response_time' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)->avg('response_time_ms'),
            'unique_ips' => RequestLog::whereIn('api_key_id', $apiKeyIds)
                ->where('requested_at', '>=', $startDate)
                ->distinct('ip_address')->count('ip_address'),
        ];

        // Recent logs for this app
        $recentLogs = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->with(['apiKey', 'apiProxy'])
            ->orderBy('requested_at', 'desc')
            ->take(20)
            ->get();

        // Usage by API key for this app
        $usageByApiKey = RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->select('api_key_id')
            ->selectRaw('COUNT(*) as request_count')
            ->selectRaw('AVG(response_time_ms) as avg_response_time')
            ->with('apiKey')
            ->groupBy('api_key_id')
            ->orderBy('request_count', 'desc')
            ->get();

        return view('developer.analytics.app', compact(
            'app',
            'stats',
            'recentLogs',
            'usageByApiKey',
            'days'
        ))->with('hasData', true);
    }

    /**
     * Get analytics data as JSON for charts
     */
    public function data(Request $request)
    {
        $user = Auth::user();
        $days = $request->input('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $apiKeyIds = $user->apps()->with('apiKeys')->get()
            ->pluck('apiKeys')->flatten()->pluck('id');

        if ($apiKeyIds->isEmpty()) {
            return response()->json([]);
        }

        $data = [
            'requests_by_hour' => $this->getRequestsByHour($apiKeyIds, $startDate),
            'error_rates' => $this->getErrorRates($apiKeyIds, $startDate),
        ];

        return response()->json($data);
    }

    /**
     * Get requests grouped by hour for developer's API keys
     */
    private function getRequestsByHour($apiKeyIds, Carbon $startDate)
    {
        return RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->selectRaw('HOUR(requested_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();
    }

    /**
     * Get error rates for developer's API keys
     */
    private function getErrorRates($apiKeyIds, Carbon $startDate)
    {
        return RequestLog::whereIn('api_key_id', $apiKeyIds)
            ->where('requested_at', '>=', $startDate)
            ->select('api_key_id')
            ->selectRaw('
                COUNT(*) as total_requests,
                SUM(CASE WHEN response_status >= 400 THEN 1 ELSE 0 END) as error_requests,
                (SUM(CASE WHEN response_status >= 400 THEN 1 ELSE 0 END) / COUNT(*) * 100) as error_rate
            ')
            ->with('apiKey.app')
            ->groupBy('api_key_id')
            ->having('total_requests', '>', 5)
            ->orderBy('error_rate', 'desc')
            ->get();
    }
}
