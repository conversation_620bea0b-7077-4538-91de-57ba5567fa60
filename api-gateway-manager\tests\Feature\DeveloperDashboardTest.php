<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\RequestLog;

class DeveloperDashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_developer_can_access_dashboard()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('developer.dashboard');
    }

    public function test_admin_can_access_developer_dashboard()
    {
        $user = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
    }

    public function test_non_developer_cannot_access_dashboard()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_dashboard()
    {
        $response = $this->get('/developer/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_dashboard_shows_correct_statistics()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app1 = App::factory()->create(['user_id' => $user->id]);
        $app2 = App::factory()->create(['user_id' => $user->id]);
        
        $apiKey1 = ApiKey::factory()->create(['app_id' => $app1->id]);
        $apiKey2 = ApiKey::factory()->create(['app_id' => $app2->id]);
        
        RequestLog::factory()->count(5)->create(['api_key_id' => $apiKey1->id]);
        RequestLog::factory()->count(3)->create(['api_key_id' => $apiKey2->id]);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(2, $stats['total_apps']);
        $this->assertEquals(2, $stats['total_api_keys']);
        $this->assertEquals(8, $stats['total_requests']);
    }

    public function test_dashboard_shows_recent_apps()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app1 = App::factory()->create(['user_id' => $user->id, 'name' => 'Recent App']);
        $app2 = App::factory()->create(['user_id' => $user->id, 'name' => 'Older App']);
        
        // Make app1 more recent
        $app1->touch();

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recent_apps');
        $response->assertSee('Recent App');
        $response->assertSee('Older App');
    }

    public function test_dashboard_shows_recent_logs()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        RequestLog::factory()->count(15)->create(['api_key_id' => $apiKey->id]);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recent_logs');
        
        $recentLogs = $response->viewData('recent_logs');
        $this->assertCount(10, $recentLogs); // Should limit to 10
    }

    public function test_dashboard_handles_user_with_no_data()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(0, $stats['total_apps']);
        $this->assertEquals(0, $stats['total_api_keys']);
        $this->assertEquals(0, $stats['total_requests']);
    }

    public function test_developer_can_access_documentation()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/documentation');

        $response->assertStatus(200);
        $response->assertViewIs('developer.documentation');
    }

    public function test_developer_can_access_support()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->get('/developer/support');

        $response->assertStatus(200);
        $response->assertViewIs('developer.support');
    }

    public function test_dashboard_excludes_other_users_data()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        
        $app1 = App::factory()->create(['user_id' => $user1->id]);
        $app2 = App::factory()->create(['user_id' => $user2->id]);
        
        $apiKey1 = ApiKey::factory()->create(['app_id' => $app1->id]);
        $apiKey2 = ApiKey::factory()->create(['app_id' => $app2->id]);
        
        RequestLog::factory()->count(5)->create(['api_key_id' => $apiKey1->id]);
        RequestLog::factory()->count(3)->create(['api_key_id' => $apiKey2->id]);

        $response = $this->actingAs($user1)->get('/developer/dashboard');

        $response->assertStatus(200);
        
        $stats = $response->viewData('stats');
        $this->assertEquals(1, $stats['total_apps']); // Only user1's app
        $this->assertEquals(1, $stats['total_api_keys']); // Only user1's API key
        $this->assertEquals(5, $stats['total_requests']); // Only user1's requests
    }

    public function test_dashboard_calculates_success_rate_correctly()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        // Create 7 successful and 3 failed requests
        RequestLog::factory()->count(7)->successful()->create(['api_key_id' => $apiKey->id]);
        RequestLog::factory()->count(3)->failed()->create(['api_key_id' => $apiKey->id]);

        $response = $this->actingAs($user)->get('/developer/dashboard');

        $response->assertStatus(200);
        
        $stats = $response->viewData('stats');
        $this->assertEquals(70.0, $stats['success_rate']); // 7/10 * 100 = 70%
    }
}
