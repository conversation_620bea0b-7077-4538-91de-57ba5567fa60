@props([
    'errors' => null,
    'success' => null,
    'title' => null,
    'dismissible' => true
])

@php
    $errors = $errors ?? $errors ?? session('errors');
    $success = $success ?? session('success');
    $hasErrors = $errors && $errors->any();
    $hasSuccess = !empty($success);
@endphp

@if($hasErrors || $hasSuccess)
<div class="form-validation-container mb-6" x-data="{ show: true }" x-show="show">
    @if($hasErrors)
    <!-- Error <PERSON>ert -->
    <div class="bg-danger-50 border border-danger-200 rounded-lg p-4">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-danger-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-danger-800">
                    {{ $title ?: 'There were some errors with your submission' }}
                </h3>
                <div class="mt-2 text-sm text-danger-700">
                    @if($errors->count() === 1)
                        <p>{{ $errors->first() }}</p>
                    @else
                        <ul class="list-disc list-inside space-y-1">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>
            @if($dismissible)
            <div class="ml-auto pl-3">
                <div class="-mx-1.5 -my-1.5">
                    <button @click="show = false" 
                            class="inline-flex bg-danger-50 rounded-md p-1.5 text-danger-500 hover:bg-danger-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-danger-50 focus:ring-danger-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif

    @if($hasSuccess)
    <!-- Success Alert -->
    <div class="bg-success-50 border border-success-200 rounded-lg p-4">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-success-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-success-800">
                    Success!
                </h3>
                <div class="mt-2 text-sm text-success-700">
                    <p>{{ $success }}</p>
                </div>
            </div>
            @if($dismissible)
            <div class="ml-auto pl-3">
                <div class="-mx-1.5 -my-1.5">
                    <button @click="show = false" 
                            class="inline-flex bg-success-50 rounded-md p-1.5 text-success-500 hover:bg-success-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-success-50 focus:ring-success-600">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            @endif
        </div>
    </div>
    @endif
</div>
@endif

@push('scripts')
<script>
// Auto-dismiss success messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const successAlerts = document.querySelectorAll('.bg-success-50');
    successAlerts.forEach(alert => {
        setTimeout(() => {
            const component = alert.closest('[x-data]');
            if (component && component.__x) {
                component.__x.$data.show = false;
            }
        }, 5000);
    });
});

// Form validation helpers
window.FormValidation = {
    // Real-time validation
    validateField(field, rules) {
        const value = field.value.trim();
        const errors = [];
        
        rules.forEach(rule => {
            switch(rule.type) {
                case 'required':
                    if (!value) {
                        errors.push(rule.message || `${field.name} is required`);
                    }
                    break;
                    
                case 'email':
                    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                        errors.push(rule.message || 'Please enter a valid email address');
                    }
                    break;
                    
                case 'min':
                    if (value && value.length < rule.value) {
                        errors.push(rule.message || `Minimum ${rule.value} characters required`);
                    }
                    break;
                    
                case 'max':
                    if (value && value.length > rule.value) {
                        errors.push(rule.message || `Maximum ${rule.value} characters allowed`);
                    }
                    break;
                    
                case 'pattern':
                    if (value && !new RegExp(rule.value).test(value)) {
                        errors.push(rule.message || 'Invalid format');
                    }
                    break;
                    
                case 'url':
                    if (value && !/^https?:\/\/.+/.test(value)) {
                        errors.push(rule.message || 'Please enter a valid URL');
                    }
                    break;
                    
                case 'numeric':
                    if (value && !/^\d+$/.test(value)) {
                        errors.push(rule.message || 'Please enter a valid number');
                    }
                    break;
            }
        });
        
        return errors;
    },
    
    // Show field error
    showFieldError(field, errors) {
        this.clearFieldError(field);
        
        if (errors.length > 0) {
            field.classList.add('border-danger-300', 'focus:border-danger-500', 'focus:ring-danger-500');
            field.classList.remove('border-secondary-300', 'focus:border-primary-500', 'focus:ring-primary-500');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'mt-2 text-sm text-danger-600 field-error';
            errorDiv.textContent = errors[0];
            
            field.parentNode.appendChild(errorDiv);
        }
    },
    
    // Clear field error
    clearFieldError(field) {
        field.classList.remove('border-danger-300', 'focus:border-danger-500', 'focus:ring-danger-500');
        field.classList.add('border-secondary-300', 'focus:border-primary-500', 'focus:ring-primary-500');
        
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    },
    
    // Validate entire form
    validateForm(form) {
        const fields = form.querySelectorAll('[data-validation]');
        let isValid = true;
        
        fields.forEach(field => {
            const rules = JSON.parse(field.dataset.validation);
            const errors = this.validateField(field, rules);
            
            if (errors.length > 0) {
                isValid = false;
                this.showFieldError(field, errors);
            } else {
                this.clearFieldError(field);
            }
        });
        
        return isValid;
    }
};

// Auto-setup validation on forms with data-validate attribute
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        // Real-time validation
        const fields = form.querySelectorAll('[data-validation]');
        fields.forEach(field => {
            field.addEventListener('blur', function() {
                const rules = JSON.parse(this.dataset.validation);
                const errors = FormValidation.validateField(this, rules);
                FormValidation.showFieldError(this, errors);
            });
            
            field.addEventListener('input', function() {
                // Clear errors on input
                FormValidation.clearFieldError(this);
            });
        });
        
        // Form submission validation
        form.addEventListener('submit', function(e) {
            if (!FormValidation.validateForm(this)) {
                e.preventDefault();
                
                // Scroll to first error
                const firstError = this.querySelector('.border-danger-300');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstError.focus();
                }
            }
        });
    });
});
</script>
@endpush

@push('styles')
<style>
/* Form validation animations */
.field-error {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Validation state transitions */
.form-input,
.form-select,
.form-textarea {
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* Focus states for better accessibility */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    ring: 2px;
    ring-offset: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .form-validation-container {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 0;
    }
    
    .form-validation-container > div {
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

/* Loading state for forms */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
@endpush
