@extends('layouts.admin')

@php
    $pageTitle = 'User Management';
@endphp

@section('title', 'User Management - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                User Management
            </h1>
            <p class="mt-2 text-secondary-600">
                Manage user accounts, roles, and permissions
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.users.create') }}" class="btn-danger">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
                Add New User
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-secondary-700 mb-1">Search</label>
                    <input type="text" 
                           id="search"
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search by name or email..."
                           class="form-input">
                </div>
                
                <div class="sm:w-48">
                    <label for="role" class="block text-sm font-medium text-secondary-700 mb-1">Role</label>
                    <select name="role" id="role" class="form-select">
                        <option value="">All Roles</option>
                        <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                        <option value="developer" {{ request('role') === 'developer' ? 'selected' : '' }}>Developer</option>
                    </select>
                </div>
                
                <div class="sm:w-48">
                    <label for="status" class="block text-sm font-medium text-secondary-700 mb-1">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                
                <div class="flex space-x-2">
                    <button type="submit" class="btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    @if(request()->hasAny(['search', 'role', 'status']))
                    <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                        Clear
                    </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    @if(isset($users) && $users->count() > 0)
        <!-- Desktop Table -->
        <div class="hidden md:block card">
            <div class="overflow-x-auto">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell">User</th>
                            <th class="table-header-cell">Role</th>
                            <th class="table-header-cell">Status</th>
                            <th class="table-header-cell">Apps</th>
                            <th class="table-header-cell">Last Login</th>
                            <th class="table-header-cell">Joined</th>
                            <th class="table-header-cell">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        @foreach($users as $user)
                        <tr>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                        <span class="text-primary-600 font-semibold text-sm">
                                            {{ substr($user->name, 0, 2) }}
                                        </span>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-secondary-900">{{ $user->name }}</div>
                                        <div class="text-sm text-secondary-500">{{ $user->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell">
                                <span class="badge-{{ $user->role === 'admin' ? 'danger' : 'primary' }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </td>
                            <td class="table-cell">
                                <span class="badge-{{ $user->is_active ? 'success' : 'secondary' }}">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td class="table-cell">
                                <span class="text-sm text-secondary-900">{{ $user->apps_count ?? 0 }}</span>
                            </td>
                            <td class="table-cell">
                                <span class="text-sm text-secondary-500">
                                    {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}
                                </span>
                            </td>
                            <td class="table-cell">
                                <span class="text-sm text-secondary-500">{{ $user->created_at->format('M j, Y') }}</span>
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.users.show', $user) }}" 
                                       class="text-primary-600 hover:text-primary-900 text-sm">
                                        View
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user) }}" 
                                       class="text-warning-600 hover:text-warning-900 text-sm">
                                        Edit
                                    </a>
                                    @if($user->id !== auth()->id())
                                    <form method="POST" action="{{ route('admin.users.destroy', $user) }}" 
                                          onsubmit="return confirm('Are you sure you want to delete this user?')"
                                          class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-danger-600 hover:text-danger-900 text-sm">
                                            Delete
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Cards -->
        <div class="md:hidden space-y-4">
            @foreach($users as $user)
            <div class="card">
                <div class="card-body">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                                <span class="text-primary-600 font-semibold">
                                    {{ substr($user->name, 0, 2) }}
                                </span>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-semibold text-secondary-900">{{ $user->name }}</h3>
                                <p class="text-sm text-secondary-500">{{ $user->email }}</p>
                            </div>
                        </div>
                        
                        <!-- Mobile Actions Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-secondary-400 hover:text-secondary-600">
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" 
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-10"
                                 style="display: none;">
                                <a href="{{ route('admin.users.show', $user) }}" 
                                   class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                                    View Details
                                </a>
                                <a href="{{ route('admin.users.edit', $user) }}" 
                                   class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                                    Edit User
                                </a>
                                @if($user->id !== auth()->id())
                                <form method="POST" action="{{ route('admin.users.destroy', $user) }}" 
                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="block w-full text-left px-4 py-2 text-sm text-danger-700 hover:bg-danger-50">
                                        Delete User
                                    </button>
                                </form>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- User Info -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <span class="text-sm text-secondary-600">Role</span>
                            <div class="mt-1">
                                <span class="badge-{{ $user->role === 'admin' ? 'danger' : 'primary' }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="text-sm text-secondary-600">Status</span>
                            <div class="mt-1">
                                <span class="badge-{{ $user->is_active ? 'success' : 'secondary' }}">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <span class="text-sm text-secondary-600">Apps</span>
                            <div class="mt-1 text-sm font-medium text-secondary-900">{{ $user->apps_count ?? 0 }}</div>
                        </div>
                        <div>
                            <span class="text-sm text-secondary-600">Joined</span>
                            <div class="mt-1 text-sm text-secondary-900">{{ $user->created_at->format('M j, Y') }}</div>
                        </div>
                    </div>

                    <!-- Last Login -->
                    <div class="text-sm text-secondary-500">
                        Last login: {{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if(method_exists($users, 'links'))
        <div class="mt-8">
            {{ $users->links() }}
        </div>
        @endif

    @else
        <!-- Empty State -->
        <div class="card">
            <div class="card-body text-center py-12">
                <svg class="h-16 w-16 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                
                @if(request()->hasAny(['search', 'role', 'status']))
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No users found</h3>
                    <p class="text-secondary-600 mb-6">
                        No users match your search criteria. Try adjusting your filters.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('admin.users.index') }}" class="btn-outline">
                            Clear Filters
                        </a>
                        <a href="{{ route('admin.users.create') }}" class="btn-danger">
                            Add New User
                        </a>
                    </div>
                @else
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No users yet</h3>
                    <p class="text-secondary-600 mb-6">
                        Get started by creating the first user account.
                    </p>
                    <a href="{{ route('admin.users.create') }}" class="btn-danger">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                        </svg>
                        Create First User
                    </a>
                @endif
            </div>
        </div>
    @endif
</div>
@endsection
