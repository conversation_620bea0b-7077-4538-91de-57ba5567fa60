<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['currentRoute' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['currentRoute' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $currentRoute = $currentRoute ?? request()->route()->getName();
?>

<!-- Bottom Navigation for Mobile -->
<div class="bottom-nav mobile-only safe-area-bottom">
    <div class="bottom-nav-container">
        <!-- Dashboard -->
        <a href="<?php echo e(route('developer.dashboard')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'developer.dashboard') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
            </svg>
            <span class="bottom-nav-label">Dashboard</span>
        </a>

        <!-- Apps -->
        <a href="<?php echo e(route('developer.apps.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'developer.apps') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span class="bottom-nav-label">Apps</span>
        </a>

        <!-- API Keys -->
        <a href="<?php echo e(route('developer.api-keys.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'developer.api-keys') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
            <span class="bottom-nav-label">Keys</span>
        </a>

        <!-- Analytics -->
        <a href="<?php echo e(route('developer.analytics.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'developer.analytics') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span class="bottom-nav-label">Analytics</span>
        </a>

        <!-- More Menu -->
        <div class="bottom-nav-item" x-data="{ open: false }">
            <button @click="open = !open" class="flex flex-col items-center justify-center w-full h-full">
                <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
                <span class="bottom-nav-label">More</span>
            </button>
            
            <!-- More Menu Dropdown -->
            <div x-show="open" 
                 @click.away="open = false"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="transform opacity-0 scale-95"
                 x-transition:enter-end="transform opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="transform opacity-100 scale-100"
                 x-transition:leave-end="transform opacity-0 scale-95"
                 class="absolute bottom-full right-4 mb-2 w-48 bg-white rounded-lg shadow-lg py-2 ring-1 ring-black ring-opacity-5"
                 style="display: none;">
                <a href="<?php echo e(route('docs.index')); ?>" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                    <svg class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    Documentation
                </a>
                <a href="<?php echo e(route('portal.support')); ?>" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                    <svg class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    Support
                </a>
                <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                    <svg class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Profile
                </a>
                <div class="border-t border-secondary-200 my-1"></div>
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100">
                        <svg class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        Sign Out
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Admin Bottom Navigation -->
<?php if(auth()->check() && auth()->user()->role === 'admin'): ?>
<div class="bottom-nav mobile-only safe-area-bottom" x-data="{ showAdmin: false }">
    <div class="bottom-nav-container">
        <!-- Dashboard -->
        <a href="<?php echo e(route('admin.dashboard')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'admin.dashboard') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
            </svg>
            <span class="bottom-nav-label">Admin</span>
        </a>

        <!-- Users -->
        <a href="<?php echo e(route('admin.users.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'admin.users') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <span class="bottom-nav-label">Users</span>
        </a>

        <!-- APIs -->
        <a href="<?php echo e(route('admin.api-proxies.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'admin.api-proxies') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span class="bottom-nav-label">APIs</span>
        </a>

        <!-- Logs -->
        <a href="<?php echo e(route('admin.request-logs.index')); ?>" 
           class="bottom-nav-item <?php echo e(str_starts_with($currentRoute, 'admin.request-logs') ? 'active' : ''); ?>">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="bottom-nav-label">Logs</span>
        </a>

        <!-- Switch to Developer -->
        <button @click="showAdmin = !showAdmin" class="bottom-nav-item">
            <svg class="bottom-nav-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            <span class="bottom-nav-label">Switch</span>
        </button>
    </div>
</div>
<?php endif; ?>

<!-- Floating Action Button -->
<?php if(str_starts_with($currentRoute, 'developer.apps')): ?>
<a href="<?php echo e(route('developer.apps.create')); ?>" class="fab mobile-only">
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
</a>
<?php elseif(str_starts_with($currentRoute, 'developer.api-keys')): ?>
<a href="<?php echo e(route('developer.api-keys.create')); ?>" class="fab mobile-only">
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
</a>
<?php elseif(str_starts_with($currentRoute, 'admin.api-proxies')): ?>
<a href="<?php echo e(route('admin.api-proxies.create')); ?>" class="fab mobile-only">
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
    </svg>
</a>
<?php elseif(str_starts_with($currentRoute, 'admin.users')): ?>
<a href="<?php echo e(route('admin.users.create')); ?>" class="fab mobile-only">
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
    </svg>
</a>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/components/mobile-bottom-nav.blade.php ENDPATH**/ ?>