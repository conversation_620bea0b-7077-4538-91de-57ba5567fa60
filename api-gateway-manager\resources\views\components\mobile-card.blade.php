@props([
    'title' => null,
    'subtitle' => null,
    'image' => null,
    'badge' => null,
    'badgeColor' => 'primary',
    'actions' => [],
    'swipeActions' => false,
    'clickable' => false,
    'href' => null
])

@php
    $cardClasses = 'card';
    if ($clickable || $href) {
        $cardClasses .= ' hover:shadow-lg transition-shadow cursor-pointer touch-feedback';
    }
    if ($swipeActions) {
        $cardClasses .= ' swipe-container';
    }
@endphp

<div class="{{ $cardClasses }}" @if($href) onclick="window.location.href='{{ $href }}'" @endif>
    @if($swipeActions)
    <!-- Swipe Actions (hidden by default) -->
    <div class="swipe-actions">
        @foreach($actions as $action)
            @if($action['type'] === 'edit')
            <a href="{{ $action['url'] }}" class="swipe-action-edit">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
            </a>
            @elseif($action['type'] === 'delete')
            <form method="POST" action="{{ $action['url'] }}" onsubmit="return confirm('{{ $action['confirm'] ?? 'Are you sure?' }}')" class="swipe-action-delete">
                @csrf
                @method('DELETE')
                <button type="submit">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </form>
            @endif
        @endforeach
    </div>
    @endif

    <div class="card-body {{ $swipeActions ? 'swipe-content' : '' }}">
        <!-- Header Section -->
        @if($title || $subtitle || $image || $badge)
        <div class="flex items-start justify-between mb-4">
            <div class="flex items-center flex-1 min-w-0">
                @if($image)
                <div class="flex-shrink-0 mr-3">
                    @if(is_string($image))
                    <img src="{{ $image }}" alt="{{ $title }}" class="h-12 w-12 rounded-lg object-cover">
                    @else
                    <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        {!! $image !!}
                    </div>
                    @endif
                </div>
                @endif
                
                <div class="flex-1 min-w-0">
                    @if($title)
                    <h3 class="text-lg font-semibold text-secondary-900 truncate">{{ $title }}</h3>
                    @endif
                    @if($subtitle)
                    <p class="text-sm text-secondary-500 truncate">{{ $subtitle }}</p>
                    @endif
                </div>
            </div>
            
            <div class="flex items-center space-x-2 ml-3">
                @if($badge)
                <span class="badge-{{ $badgeColor }} whitespace-nowrap">{{ $badge }}</span>
                @endif
                
                @if(!empty($actions) && !$swipeActions)
                <!-- Actions Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="text-secondary-400 hover:text-secondary-600 p-1">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                        </svg>
                    </button>
                    <div x-show="open" @click.away="open = false" class="dropdown-menu" style="display: none;">
                        @foreach($actions as $action)
                        @if($action['type'] === 'link')
                        <a href="{{ $action['url'] }}" class="dropdown-item">
                            @if(isset($action['icon']))
                            {!! $action['icon'] !!}
                            @endif
                            {{ $action['label'] }}
                        </a>
                        @elseif($action['type'] === 'button')
                        <button onclick="{{ $action['onclick'] }}" class="dropdown-item w-full text-left">
                            @if(isset($action['icon']))
                            {!! $action['icon'] !!}
                            @endif
                            {{ $action['label'] }}
                        </button>
                        @elseif($action['type'] === 'delete')
                        <form method="POST" action="{{ $action['url'] }}" onsubmit="return confirm('{{ $action['confirm'] ?? 'Are you sure?' }}')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="dropdown-item-danger w-full text-left">
                                @if(isset($action['icon']))
                                {!! $action['icon'] !!}
                                @endif
                                {{ $action['label'] }}
                            </button>
                        </form>
                        @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Content Section -->
        <div class="space-y-3">
            {{ $slot }}
        </div>
    </div>
</div>

@push('scripts')
<script>
// Swipe gesture handling for mobile
document.addEventListener('DOMContentLoaded', function() {
    const swipeContainers = document.querySelectorAll('.swipe-container');
    
    swipeContainers.forEach(container => {
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        const swipeContent = container.querySelector('.swipe-content');
        const swipeActions = container.querySelector('.swipe-actions');
        
        if (!swipeContent || !swipeActions) return;
        
        // Touch events
        container.addEventListener('touchstart', handleStart, { passive: true });
        container.addEventListener('touchmove', handleMove, { passive: false });
        container.addEventListener('touchend', handleEnd, { passive: true });
        
        // Mouse events for desktop testing
        container.addEventListener('mousedown', handleStart);
        container.addEventListener('mousemove', handleMove);
        container.addEventListener('mouseup', handleEnd);
        container.addEventListener('mouseleave', handleEnd);
        
        function handleStart(e) {
            startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            isDragging = true;
            container.style.userSelect = 'none';
        }
        
        function handleMove(e) {
            if (!isDragging) return;
            
            e.preventDefault();
            currentX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const deltaX = startX - currentX;
            
            if (deltaX > 0) {
                // Swiping left - show actions
                const translateX = Math.min(deltaX, 120);
                swipeContent.style.transform = `translateX(-${translateX}px)`;
            } else {
                // Swiping right - hide actions
                swipeContent.style.transform = 'translateX(0)';
            }
        }
        
        function handleEnd(e) {
            if (!isDragging) return;
            
            isDragging = false;
            container.style.userSelect = '';
            
            const deltaX = startX - currentX;
            
            if (deltaX > 60) {
                // Show actions
                swipeContent.style.transform = 'translateX(-120px)';
            } else {
                // Hide actions
                swipeContent.style.transform = 'translateX(0)';
            }
        }
        
        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                swipeContent.style.transform = 'translateX(0)';
            }
        });
    });
});
</script>
@endpush
