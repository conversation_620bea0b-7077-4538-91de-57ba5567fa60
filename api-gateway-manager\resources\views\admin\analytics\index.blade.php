@extends('layouts.admin')

@php
    $pageTitle = 'System Analytics';
@endphp

@section('title', 'System Analytics - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            System Analytics
        </h1>
        <p class="mt-2 text-secondary-600">
            Monitor overall system performance, usage patterns, and health metrics.
        </p>
    </div>

    <!-- Time Range Filter -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="flex flex-col sm:flex-row sm:items-end sm:space-x-4 space-y-4 sm:space-y-0">
                <div class="flex-1">
                    <label for="date_range" class="block text-sm font-medium text-secondary-700 mb-1">Time Range</label>
                    <select name="date_range" id="date_range" class="form-select" onchange="this.form.submit()">
                        <option value="24h" {{ request('date_range', '24h') === '24h' ? 'selected' : '' }}>Last 24 hours</option>
                        <option value="7d" {{ request('date_range') === '7d' ? 'selected' : '' }}>Last 7 days</option>
                        <option value="30d" {{ request('date_range') === '30d' ? 'selected' : '' }}>Last 30 days</option>
                        <option value="90d" {{ request('date_range') === '90d' ? 'selected' : '' }}>Last 90 days</option>
                    </select>
                </div>
                
                <div class="sm:w-48">
                    <label for="api_filter" class="block text-sm font-medium text-secondary-700 mb-1">API Proxy</label>
                    <select name="api_filter" id="api_filter" class="form-select" onchange="this.form.submit()">
                        <option value="">All APIs</option>
                        @if(isset($apiProxies))
                            @foreach($apiProxies as $proxy)
                                <option value="{{ $proxy->id }}" {{ request('api_filter') == $proxy->id ? 'selected' : '' }}>
                                    {{ $proxy->name }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- System Overview -->
    <div class="grid grid-cols-2 gap-4 sm:grid-cols-4 lg:grid-cols-6 mb-8">
        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-primary-600">{{ number_format($systemMetrics['total_requests'] ?? 0) }}</div>
                <div class="text-xs text-secondary-600 mt-1">Total Requests</div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-success-600">{{ number_format($systemMetrics['active_users'] ?? 0) }}</div>
                <div class="text-xs text-secondary-600 mt-1">Active Users</div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-warning-600">{{ number_format($systemMetrics['active_apps'] ?? 0) }}</div>
                <div class="text-xs text-secondary-600 mt-1">Active Apps</div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-danger-600">{{ number_format($systemMetrics['api_proxies'] ?? 0) }}</div>
                <div class="text-xs text-secondary-600 mt-1">API Proxies</div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-secondary-600">{{ number_format($systemMetrics['avg_response_time'] ?? 0) }}ms</div>
                <div class="text-xs text-secondary-600 mt-1">Avg Response</div>
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-xl font-bold text-{{ $systemMetrics['error_rate'] > 5 ? 'danger' : 'success' }}-600">
                    {{ number_format($systemMetrics['error_rate'] ?? 0, 1) }}%
                </div>
                <div class="text-xs text-secondary-600 mt-1">Error Rate</div>
            </div>
        </div>
    </div>

    <!-- Main Charts -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <!-- System Load -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">System Load</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="line"
                    :data="$systemLoadChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => ['display' => true]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Requests per minute'
                                ]
                            ]
                        ]
                    ]"
                    height="300"
                    id="system-load-chart" />
            </div>
        </div>

        <!-- Response Time Distribution -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Response Time Distribution</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="bar"
                    :data="$responseTimeDistChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Number of requests'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Response time (ms)'
                                ]
                            ]
                        ]
                    ]"
                    height="300"
                    id="response-dist-chart" />
            </div>
        </div>
    </div>

    <!-- API Usage and Errors -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <!-- API Usage by Proxy -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">API Usage by Proxy</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="doughnut"
                    :data="$apiUsageChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => [
                                'position' => 'bottom'
                            ]
                        ]
                    ]"
                    height="300"
                    id="api-usage-chart" />
            </div>
        </div>

        <!-- Error Breakdown -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Error Breakdown</h3>
            </div>
            <div class="card-body">
                @if(isset($errorBreakdown) && count($errorBreakdown) > 0)
                    <div class="space-y-4">
                        @foreach($errorBreakdown as $error)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="badge-danger text-xs mr-2">{{ $error['status_code'] }}</span>
                                <div>
                                    <div class="text-sm font-medium text-secondary-900">{{ $error['message'] }}</div>
                                    <div class="text-xs text-secondary-500">{{ $error['count'] }} occurrences</div>
                                </div>
                            </div>
                            <div class="text-sm font-medium text-secondary-900">
                                {{ number_format($error['percentage'], 1) }}%
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-success-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No errors detected</h3>
                        <p class="text-sm text-secondary-500">System is running smoothly</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Top Users and Rate Limiting -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <!-- Top Users by Requests -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Top Users by Requests</h3>
            </div>
            <div class="card-body">
                @if(isset($topUsers) && count($topUsers) > 0)
                    <div class="space-y-4">
                        @foreach($topUsers as $user)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-primary-600 font-semibold text-xs">
                                        {{ substr($user['name'], 0, 2) }}
                                    </span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-secondary-900">{{ $user['name'] }}</div>
                                    <div class="text-xs text-secondary-500">{{ $user['email'] }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-secondary-900">{{ number_format($user['requests']) }}</div>
                                <div class="text-xs text-secondary-500">requests</div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No user activity</h3>
                        <p class="text-sm text-secondary-500">User activity will appear here</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Rate Limiting Status -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Rate Limiting Status</h3>
            </div>
            <div class="card-body">
                @if(isset($rateLimitStatus) && count($rateLimitStatus) > 0)
                    <div class="space-y-4">
                        @foreach($rateLimitStatus as $status)
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="text-sm font-medium text-secondary-900">{{ $status['name'] }}</div>
                                <div class="text-xs text-secondary-500">{{ $status['limit'] }} requests/{{ $status['window'] }}</div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-20 bg-secondary-200 rounded-full h-2 mr-2">
                                    <div class="bg-{{ $status['usage_percentage'] > 80 ? 'danger' : ($status['usage_percentage'] > 60 ? 'warning' : 'success') }}-500 h-2 rounded-full" 
                                         style="width: {{ $status['usage_percentage'] }}%"></div>
                                </div>
                                <span class="text-xs text-secondary-600">{{ $status['usage_percentage'] }}%</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No rate limits configured</h3>
                        <p class="text-sm text-secondary-500">Configure rate limits to monitor usage</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-secondary-900">System Health</h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <div class="text-center">
                    <div class="h-16 w-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="h-8 w-8 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <div class="text-sm font-medium text-secondary-900">API Gateway</div>
                    <div class="text-xs text-success-600 mt-1">Operational</div>
                </div>

                <div class="text-center">
                    <div class="h-16 w-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="h-8 w-8 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                        </svg>
                    </div>
                    <div class="text-sm font-medium text-secondary-900">Database</div>
                    <div class="text-xs text-success-600 mt-1">Connected</div>
                </div>

                <div class="text-center">
                    <div class="h-16 w-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="h-8 w-8 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="text-sm font-medium text-secondary-900">Cache</div>
                    <div class="text-xs text-warning-600 mt-1">File-based</div>
                </div>

                <div class="text-center">
                    <div class="h-16 w-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="h-8 w-8 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="text-sm font-medium text-secondary-900">Security</div>
                    <div class="text-xs text-success-600 mt-1">Active</div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-refresh system analytics every 60 seconds
let refreshInterval;

function startAutoRefresh() {
    refreshInterval = setInterval(function() {
        if (!document.hidden) {
            refreshSystemAnalytics();
        }
    }, 60000);
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

async function refreshSystemAnalytics() {
    try {
        const params = new URLSearchParams(window.location.search);
        const response = await fetch(window.location.pathname + '?' + params.toString(), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            if (data.systemMetrics) {
                updateSystemMetrics(data.systemMetrics);
            }
            
            if (data.charts) {
                updateSystemCharts(data.charts);
            }
        }
    } catch (error) {
        console.error('Failed to refresh system analytics:', error);
    }
}

function updateSystemMetrics(metrics) {
    Object.keys(metrics).forEach(key => {
        const element = document.querySelector(`[data-metric="${key}"]`);
        if (element) {
            element.textContent = metrics[key];
        }
    });
}

function updateSystemCharts(charts) {
    if (charts.systemLoad && window.charts['system-load-chart']) {
        updateChart('system-load-chart', charts.systemLoad);
    }
    
    if (charts.responseTimeDist && window.charts['response-dist-chart']) {
        updateChart('response-dist-chart', charts.responseTimeDist);
    }
    
    if (charts.apiUsage && window.charts['api-usage-chart']) {
        updateChart('api-usage-chart', charts.apiUsage);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
    }
});

window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
</script>
@endpush
@endsection
