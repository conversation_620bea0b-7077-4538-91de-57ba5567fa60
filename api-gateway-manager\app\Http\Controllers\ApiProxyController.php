<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ApiProxy;
use App\Models\ApiKey;
use App\Models\RateLimit;
use App\Models\RequestLog;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ApiProxyController extends Controller
{
    private Client $httpClient;

    public function __construct()
    {
        $this->httpClient = new Client([
            'timeout' => 30,
            'connect_timeout' => 10,
        ]);
    }

    /**
     * Handle API proxy requests
     */
    public function handle(Request $request, string $path = ''): JsonResponse
    {
        $startTime = microtime(true);
        $apiKey = null;
        $apiProxy = null;
        $responseStatus = 500;
        $responseHeaders = [];
        $errorMessage = null;

        try {
            // Find the API proxy by path
            $fullPath = '/' . ltrim($path, '/');
            $apiProxy = ApiProxy::findByPath($fullPath);

            if (!$apiProxy) {
                $responseStatus = 404;
                $errorMessage = 'API endpoint not found';
                return $this->errorResponse('API endpoint not found', 404);
            }

            // Check if method is allowed
            if (!$apiProxy->isMethodAllowed($request->method())) {
                $responseStatus = 405;
                $errorMessage = 'Method not allowed';
                return $this->errorResponse('Method not allowed', 405);
            }

            // Authenticate if required
            if ($apiProxy->requires_auth) {
                $apiKey = $this->authenticateRequest($request);
                if (!$apiKey) {
                    $responseStatus = 401;
                    $errorMessage = 'Authentication required';
                    return $this->errorResponse('Authentication required', 401);
                }

                // Check rate limits
                $rateLimitResult = $this->checkRateLimit($apiKey, $apiProxy);
                if ($rateLimitResult !== true) {
                    $responseStatus = 429;
                    $errorMessage = 'Rate limit exceeded';
                    return $rateLimitResult;
                }
            }

            // Forward the request
            $response = $this->forwardRequest($request, $apiProxy, $path);
            $responseStatus = $response->getStatusCode();
            $responseHeaders = $response->getHeaders();

            return response()->json(
                json_decode($response->getBody()->getContents(), true),
                $responseStatus,
                $this->processResponseHeaders($responseHeaders, $apiProxy)
            );

        } catch (RequestException $e) {
            $responseStatus = $e->getResponse() ? $e->getResponse()->getStatusCode() : 500;
            $errorMessage = $e->getMessage();
            Log::error('API Proxy Request Exception', [
                'path' => $path,
                'error' => $e->getMessage(),
                'api_proxy_id' => $apiProxy?->id,
            ]);

            return $this->errorResponse('Backend service error', $responseStatus);

        } catch (\Exception $e) {
            $responseStatus = 500;
            $errorMessage = $e->getMessage();
            Log::error('API Proxy General Exception', [
                'path' => $path,
                'error' => $e->getMessage(),
                'api_proxy_id' => $apiProxy?->id,
            ]);

            return $this->errorResponse('Internal server error', 500);

        } finally {
            // Log the request
            if ($apiProxy) {
                $this->logRequest(
                    $request,
                    $apiProxy,
                    $apiKey,
                    $responseStatus,
                    $responseHeaders,
                    microtime(true) - $startTime,
                    $errorMessage
                );
            }
        }
    }

    /**
     * Authenticate the request using API key
     */
    private function authenticateRequest(Request $request): ?ApiKey
    {
        $apiKeyValue = $request->header('X-API-Key') ?? $request->input('api_key');

        if (!$apiKeyValue) {
            return null;
        }

        $hashedKey = ApiKey::hashKey($apiKeyValue);
        $apiKey = ApiKey::where('key_hash', $hashedKey)
            ->where('is_active', true)
            ->with(['app'])
            ->first();

        if (!$apiKey || $apiKey->isExpired()) {
            return null;
        }

        $apiKey->markAsUsed();
        return $apiKey;
    }

    /**
     * Check rate limits for the API key and proxy
     */
    private function checkRateLimit(ApiKey $apiKey, ApiProxy $apiProxy)
    {
        $rateLimit = RateLimit::getOrCreate($apiKey->id, $apiProxy->id);

        if ($rateLimit->isExceeded()) {
            return $this->errorResponse('Rate limit exceeded', 429);
        }

        $rateLimit->incrementCounters();
        return true;
    }

    /**
     * Forward the request to the backend service
     */
    private function forwardRequest(Request $request, ApiProxy $apiProxy, string $path)
    {
        $targetUrl = $apiProxy->getTargetUrl($path);

        // Prepare headers
        $headers = $this->prepareHeaders($request, $apiProxy);

        // Prepare request options
        $options = [
            'headers' => $headers,
            'timeout' => $apiProxy->timeout,
            'http_errors' => false, // Don't throw exceptions for HTTP error status codes
        ];

        // Add body for POST, PUT, PATCH requests
        if (in_array($request->method(), ['POST', 'PUT', 'PATCH'])) {
            $contentType = $request->header('Content-Type', 'application/json');

            if (str_contains($contentType, 'application/json')) {
                $options['json'] = $request->json()->all();
            } else {
                $options['form_params'] = $request->all();
            }
        }

        // Add query parameters
        if ($request->query()) {
            $options['query'] = $request->query();
        }

        return $this->httpClient->request($request->method(), $targetUrl, $options);
    }

    /**
     * Prepare headers for the backend request
     */
    private function prepareHeaders(Request $request, ApiProxy $apiProxy): array
    {
        $headers = [];

        // Copy original headers (excluding some)
        $excludeHeaders = ['host', 'x-api-key', 'authorization'];
        foreach ($request->headers->all() as $name => $values) {
            if (!in_array(strtolower($name), $excludeHeaders)) {
                $headers[$name] = $values[0];
            }
        }

        // Add custom headers
        if ($apiProxy->headers_to_add) {
            foreach ($apiProxy->headers_to_add as $name => $value) {
                $headers[$name] = $value;
            }
        }

        // Remove specified headers
        if ($apiProxy->headers_to_remove) {
            foreach ($apiProxy->headers_to_remove as $headerName) {
                unset($headers[strtolower($headerName)]);
            }
        }

        return $headers;
    }

    /**
     * Process response headers
     */
    private function processResponseHeaders(array $headers, ApiProxy $apiProxy): array
    {
        $processedHeaders = [];

        // Add CORS headers for browser compatibility
        $processedHeaders['Access-Control-Allow-Origin'] = '*';
        $processedHeaders['Access-Control-Allow-Methods'] = implode(', ', $apiProxy->allowed_methods);
        $processedHeaders['Access-Control-Allow-Headers'] = 'Content-Type, X-API-Key, Authorization';

        return $processedHeaders;
    }

    /**
     * Log the API request
     */
    private function logRequest(
        Request $request,
        ApiProxy $apiProxy,
        ?ApiKey $apiKey,
        int $responseStatus,
        array $responseHeaders,
        float $responseTime,
        ?string $errorMessage
    ): void {
        RequestLog::createLog([
            'api_key_id' => $apiKey?->id,
            'api_proxy_id' => $apiProxy->id,
            'ip_address' => $request->ip(),
            'method' => $request->method(),
            'path' => $request->path(),
            'query_string' => $request->getQueryString(),
            'request_headers' => $request->headers->all(),
            'response_status' => $responseStatus,
            'response_headers' => $responseHeaders,
            'response_time_ms' => round($responseTime * 1000),
            'response_size_bytes' => null, // Could be calculated if needed
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Return an error response
     */
    private function errorResponse(string $message, int $status): JsonResponse
    {
        return response()->json([
            'error' => true,
            'message' => $message,
            'status' => $status,
        ], $status);
    }
}
