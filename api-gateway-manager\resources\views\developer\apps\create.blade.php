@extends('layouts.developer')

@php
    $pageTitle = 'Create New App';
@endphp

@section('title', 'Create New App - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('developer.apps.index') }}" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                Create New Application
            </h1>
        </div>
        <p class="text-secondary-600">
            Create a new application to organize your API keys and track usage.
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <form method="POST" action="{{ route('developer.apps.store') }}" class="space-y-6">
            @csrf

            <!-- App Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                    Application Name <span class="text-danger-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required
                       value="{{ old('name') }}"
                       class="form-input @error('name') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Enter a descriptive name for your app">
                @error('name')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Choose a clear, descriptive name that identifies your application.
                </p>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          class="form-textarea @error('description') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                          placeholder="Describe what your application does and how it will use the API...">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Provide a brief description of your application and its purpose.
                </p>
            </div>

            <!-- Callback URL -->
            <div>
                <label for="callback_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Callback URL
                </label>
                <input type="url" 
                       id="callback_url" 
                       name="callback_url"
                       value="{{ old('callback_url') }}"
                       class="form-input @error('callback_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="https://your-app.com/callback">
                @error('callback_url')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. URL where users will be redirected after authentication (if applicable).
                </p>
            </div>

            <!-- Base URL -->
            <div>
                <label for="base_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Base URL
                </label>
                <input type="url"
                       id="base_url"
                       name="base_url"
                       value="{{ old('base_url') }}"
                       class="form-input @error('base_url') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="https://api.your-service.com">
                @error('base_url')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Base URL for your API endpoints. This will be prepended to relative endpoint URLs.
                </p>
            </div>

            <!-- Authorization Token -->
            <div>
                <label for="authorization_token" class="block text-sm font-medium text-secondary-700 mb-2">
                    Authorization Token
                </label>
                <input type="text"
                       id="authorization_token"
                       name="authorization_token"
                       value="{{ old('authorization_token') }}"
                       class="form-input @error('authorization_token') border-danger-300 focus:border-danger-500 focus:ring-danger-500 @enderror"
                       placeholder="Bearer your-token-here">
                @error('authorization_token')
                    <p class="mt-2 text-sm text-danger-600">{{ $message }}</p>
                @enderror
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Authorization token to be automatically added to all endpoint requests.
                </p>
            </div>

            <!-- Custom Headers -->
            <div x-data="{ headers: {{ old('custom_headers') ? json_encode(old('custom_headers')) : '[]' }} }">
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                    Custom Headers
                </label>
                <div class="space-y-3">
                    <template x-for="(header, index) in headers" :key="index">
                        <div class="flex gap-3 items-start">
                            <div class="flex-1">
                                <input type="text"
                                       :name="`custom_headers[${index}][name]`"
                                       x-model="header.name"
                                       class="form-input"
                                       placeholder="Header name (e.g., X-API-Version)">
                            </div>
                            <div class="flex-1">
                                <input type="text"
                                       :name="`custom_headers[${index}][value]`"
                                       x-model="header.value"
                                       class="form-input"
                                       placeholder="Header value">
                            </div>
                            <button type="button"
                                    @click="headers.splice(index, 1)"
                                    class="btn-sm btn-outline text-danger-600 hover:text-danger-700">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </template>
                    <button type="button"
                            @click="headers.push({ name: '', value: '' })"
                            class="btn-sm btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Header
                    </button>
                </div>
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Custom headers to be automatically added to all endpoint requests.
                </p>
            </div>

            <!-- App Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           {{ old('is_active', true) ? 'checked' : '' }}
                           class="form-checkbox">
                    <label for="is_active" class="ml-2 block text-sm text-secondary-700">
                        Active application
                    </label>
                </div>
                <p class="mt-2 text-sm text-secondary-500">
                    Active applications can generate and use API keys. Inactive applications cannot make API calls.
                </p>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-secondary-200">
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Application
                </button>
                <a href="{{ route('developer.apps.index') }}" class="btn-outline">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-12 max-w-2xl">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold text-secondary-900 mb-4">
                    What happens next?
                </h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">1</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Application Created</h4>
                            <p class="text-sm text-secondary-600">Your application will be created and ready to use.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">2</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Generate API Keys</h4>
                            <p class="text-sm text-secondary-600">Create API keys to authenticate your requests.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">3</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Start Building</h4>
                            <p class="text-sm text-secondary-600">Use your API keys to make authenticated requests.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
