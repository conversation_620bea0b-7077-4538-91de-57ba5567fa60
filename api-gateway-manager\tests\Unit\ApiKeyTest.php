<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\ApiKey;
use App\Models\App;
use App\Models\User;
use App\Models\RequestLog;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApiKeyTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_key_generation()
    {
        $key = ApiKey::generateKey();

        $this->assertStringStartsWith('agm_', $key);
        $this->assertEquals(36, strlen($key)); // agm_ (4) + 32 random chars
        $this->assertMatchesRegularExpression('/^agm_[a-zA-Z0-9]{32}$/', $key);
    }

    public function test_api_key_hashing()
    {
        $key = 'agm_test123456789012345678901234';
        $hash = ApiKey::hashKey($key);

        $this->assertEquals(64, strlen($hash)); // SHA256 produces 64 char hex string
        $this->assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $hash);

        // Same key should produce same hash
        $this->assertEquals($hash, ApiKey::hashKey($key));
    }

    public function test_api_key_prefix_generation()
    {
        $key = 'agm_test123456789012345678901234';
        $prefix = ApiKey::getKeyPrefix($key);

        $this->assertEquals('agm_test', $prefix);
        $this->assertEquals(8, strlen($prefix));
    }

    public function test_api_key_belongs_to_app()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $this->assertInstanceOf(App::class, $apiKey->app);
        $this->assertEquals($app->id, $apiKey->app->id);
    }

    public function test_api_key_has_many_request_logs()
    {
        $apiKey = ApiKey::factory()->create();
        $requestLog = RequestLog::factory()->create(['api_key_id' => $apiKey->id]);

        $this->assertTrue($apiKey->requestLogs()->exists());
        $this->assertEquals(1, $apiKey->requestLogs()->count());
        $this->assertEquals($requestLog->id, $apiKey->requestLogs()->first()->id);
    }

    public function test_api_key_is_expired_method()
    {
        // Test non-expired key
        $apiKey = ApiKey::factory()->create(['expires_at' => now()->addDay()]);
        $this->assertFalse($apiKey->isExpired());

        // Test expired key
        $apiKey = ApiKey::factory()->create(['expires_at' => now()->subDay()]);
        $this->assertTrue($apiKey->isExpired());

        // Test key with no expiration
        $apiKey = ApiKey::factory()->create(['expires_at' => null]);
        $this->assertFalse($apiKey->isExpired());
    }

    public function test_api_key_mark_as_used_method()
    {
        $apiKey = ApiKey::factory()->create(['last_used_at' => null]);

        $this->assertNull($apiKey->last_used_at);

        $apiKey->markAsUsed();
        $apiKey->refresh();

        $this->assertNotNull($apiKey->last_used_at);
        $this->assertTrue($apiKey->last_used_at->isToday());
    }

    public function test_api_key_factory_creates_valid_key()
    {
        $apiKey = ApiKey::factory()->create();

        $this->assertNotNull($apiKey->name);
        $this->assertNotNull($apiKey->key_hash);
        $this->assertNotNull($apiKey->key_prefix);
        $this->assertTrue($apiKey->is_active);
        $this->assertNull($apiKey->last_used_at);
        $this->assertNull($apiKey->expires_at);
        $this->assertInstanceOf(App::class, $apiKey->app);
    }

    public function test_api_key_factory_can_create_inactive_key()
    {
        $apiKey = ApiKey::factory()->inactive()->create();

        $this->assertFalse($apiKey->is_active);
    }

    public function test_api_key_factory_can_create_expired_key()
    {
        $apiKey = ApiKey::factory()->expired()->create();

        $this->assertNotNull($apiKey->expires_at);
        $this->assertTrue($apiKey->expires_at->isPast());
        $this->assertTrue($apiKey->isExpired());
    }

    public function test_api_key_casts_attributes_correctly()
    {
        $apiKey = ApiKey::factory()->create([
            'is_active' => true,
            'expires_at' => now(),
            'last_used_at' => now(),
        ]);

        $this->assertIsBool($apiKey->is_active);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $apiKey->expires_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $apiKey->last_used_at);
    }

    public function test_api_key_fillable_attributes()
    {
        $app = App::factory()->create();
        $data = [
            'app_id' => $app->id,
            'name' => 'Test API Key',
            'key_hash' => 'test_hash',
            'key_prefix' => 'test_pre',
            'is_active' => false,
            'expires_at' => now()->addDay(),
        ];

        $apiKey = ApiKey::create($data);

        $this->assertEquals('Test API Key', $apiKey->name);
        $this->assertEquals('test_hash', $apiKey->key_hash);
        $this->assertEquals('test_pre', $apiKey->key_prefix);
        $this->assertFalse($apiKey->is_active);
        $this->assertEquals($app->id, $apiKey->app_id);
    }

    public function test_api_key_prefix_extraction()
    {
        $key = 'agm_test123456789012345678901234';
        $prefix = ApiKey::getKeyPrefix($key);

        $this->assertEquals('agm_test', $prefix);
        $this->assertEquals(8, strlen($prefix));
    }

    public function test_api_key_expiry_detection()
    {
        $apiKey = ApiKey::factory()->create([
            'expires_at' => now()->subDay(),
        ]);

        $this->assertTrue($apiKey->isExpired());

        $apiKey->expires_at = now()->addDay();
        $apiKey->save();

        $this->assertFalse($apiKey->isExpired());

        $apiKey->expires_at = null;
        $apiKey->save();

        $this->assertFalse($apiKey->isExpired()); // null means no expiry
    }

    public function test_api_key_usage_marking()
    {
        $apiKey = ApiKey::factory()->create([
            'last_used_at' => null,
        ]);

        $this->assertNull($apiKey->last_used_at);

        $apiKey->markAsUsed();
        $apiKey->refresh();

        $this->assertNotNull($apiKey->last_used_at);
        $this->assertTrue($apiKey->last_used_at->isToday());
    }


}
