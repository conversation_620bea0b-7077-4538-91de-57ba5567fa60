<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\ApiKey;
use App\Models\App;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ApiKeyTest extends TestCase
{
    use RefreshDatabase;

    public function test_api_key_generation()
    {
        $key = ApiKey::generateKey();

        $this->assertStringStartsWith('agm_', $key);
        $this->assertEquals(36, strlen($key)); // agm_ (4) + 32 random chars
        $this->assertMatchesRegularExpression('/^agm_[a-zA-Z0-9]{32}$/', $key);
    }

    public function test_api_key_hashing()
    {
        $key = 'agm_test123456789012345678901234';
        $hash = ApiKey::hashKey($key);

        $this->assertEquals(64, strlen($hash)); // SHA256 produces 64 char hex string
        $this->assertMatchesRegularExpression('/^[a-f0-9]{64}$/', $hash);

        // Same key should produce same hash
        $this->assertEquals($hash, ApiKey::hashKey($key));
    }

    public function test_api_key_prefix_extraction()
    {
        $key = 'agm_test123456789012345678901234';
        $prefix = ApiKey::getKeyPrefix($key);

        $this->assertEquals('agm_test', $prefix);
        $this->assertEquals(8, strlen($prefix));
    }

    public function test_api_key_expiry_detection()
    {
        $apiKey = ApiKey::factory()->create([
            'expires_at' => now()->subDay(),
        ]);

        $this->assertTrue($apiKey->isExpired());

        $apiKey->expires_at = now()->addDay();
        $apiKey->save();

        $this->assertFalse($apiKey->isExpired());

        $apiKey->expires_at = null;
        $apiKey->save();

        $this->assertFalse($apiKey->isExpired()); // null means no expiry
    }

    public function test_api_key_usage_marking()
    {
        $apiKey = ApiKey::factory()->create([
            'last_used_at' => null,
        ]);

        $this->assertNull($apiKey->last_used_at);

        $apiKey->markAsUsed();
        $apiKey->refresh();

        $this->assertNotNull($apiKey->last_used_at);
        $this->assertTrue($apiKey->last_used_at->isToday());
    }

    public function test_api_key_belongs_to_app()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $this->assertInstanceOf(App::class, $apiKey->app);
        $this->assertEquals($app->id, $apiKey->app->id);
    }

    public function test_api_key_has_rate_limits_relationship()
    {
        $apiKey = ApiKey::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $apiKey->rateLimits);
    }

    public function test_api_key_has_request_logs_relationship()
    {
        $apiKey = ApiKey::factory()->create();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $apiKey->requestLogs);
    }

    public function test_api_key_casts()
    {
        $apiKey = ApiKey::factory()->create([
            'is_active' => '1',
            'last_used_at' => '2023-01-01 12:00:00',
            'expires_at' => '2024-01-01 12:00:00',
        ]);

        $this->assertIsBool($apiKey->is_active);
        $this->assertInstanceOf(\Carbon\Carbon::class, $apiKey->last_used_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $apiKey->expires_at);
    }

    public function test_api_key_fillable_attributes()
    {
        $apiKey = new ApiKey();
        $fillable = $apiKey->getFillable();

        $expectedFillable = [
            'app_id',
            'name',
            'key_hash',
            'key_prefix',
            'is_active',
            'last_used_at',
            'expires_at',
        ];

        $this->assertEquals($expectedFillable, $fillable);
    }
}
