<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_proxies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique(); // URL-friendly identifier
            $table->text('description')->nullable();
            $table->string('proxy_path'); // The path on our gateway (e.g., /api/v1/users)
            $table->string('target_url'); // The backend service URL
            $table->json('allowed_methods')->default('["GET"]'); // HTTP methods allowed
            $table->json('headers_to_add')->nullable(); // Headers to add to requests
            $table->json('headers_to_remove')->nullable(); // Headers to remove from requests
            $table->boolean('requires_auth')->default(true);
            $table->boolean('is_active')->default(true);
            $table->integer('timeout')->default(30); // Request timeout in seconds
            $table->timestamps();

            $table->index(['slug', 'is_active']);
            $table->index('proxy_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_proxies');
    }
};
