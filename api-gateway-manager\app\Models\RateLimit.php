<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class RateLimit extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_key_id',
        'api_proxy_id',
        'requests_per_minute',
        'requests_per_hour',
        'requests_per_day',
        'current_minute_count',
        'current_hour_count',
        'current_day_count',
        'minute_reset_at',
        'hour_reset_at',
        'day_reset_at',
    ];

    protected function casts(): array
    {
        return [
            'requests_per_minute' => 'integer',
            'requests_per_hour' => 'integer',
            'requests_per_day' => 'integer',
            'current_minute_count' => 'integer',
            'current_hour_count' => 'integer',
            'current_day_count' => 'integer',
            'minute_reset_at' => 'datetime',
            'hour_reset_at' => 'datetime',
            'day_reset_at' => 'datetime',
        ];
    }

    /**
     * Get the API key that owns this rate limit
     */
    public function apiKey()
    {
        return $this->belongsTo(ApiKey::class);
    }

    /**
     * Get the API proxy that owns this rate limit
     */
    public function apiProxy()
    {
        return $this->belongsTo(ApiProxy::class);
    }

    /**
     * Check if the rate limit is exceeded
     */
    public function isExceeded(): bool
    {
        $this->resetCountersIfNeeded();

        return $this->current_minute_count >= $this->requests_per_minute ||
               $this->current_hour_count >= $this->requests_per_hour ||
               $this->current_day_count >= $this->requests_per_day;
    }

    /**
     * Increment the counters
     */
    public function incrementCounters(): void
    {
        $this->resetCountersIfNeeded();

        $this->increment('current_minute_count');
        $this->increment('current_hour_count');
        $this->increment('current_day_count');
    }

    /**
     * Reset counters if time windows have passed
     */
    private function resetCountersIfNeeded(): void
    {
        $now = Carbon::now();

        // Reset minute counter
        if (!$this->minute_reset_at || $now->greaterThan($this->minute_reset_at)) {
            $this->current_minute_count = 0;
            $this->minute_reset_at = $now->copy()->addMinute();
        }

        // Reset hour counter
        if (!$this->hour_reset_at || $now->greaterThan($this->hour_reset_at)) {
            $this->current_hour_count = 0;
            $this->hour_reset_at = $now->copy()->addHour();
        }

        // Reset day counter
        if (!$this->day_reset_at || $now->greaterThan($this->day_reset_at)) {
            $this->current_day_count = 0;
            $this->day_reset_at = $now->copy()->addDay();
        }

        $this->save();
    }

    /**
     * Get or create rate limit for API key and proxy
     */
    public static function getOrCreate(int $apiKeyId, int $apiProxyId): self
    {
        return static::firstOrCreate(
            [
                'api_key_id' => $apiKeyId,
                'api_proxy_id' => $apiProxyId,
            ],
            [
                'requests_per_minute' => 60,
                'requests_per_hour' => 1000,
                'requests_per_day' => 10000,
                'current_minute_count' => 0,
                'current_hour_count' => 0,
                'current_day_count' => 0,
                'minute_reset_at' => Carbon::now()->addMinute(),
                'hour_reset_at' => Carbon::now()->addHour(),
                'day_reset_at' => Carbon::now()->addDay(),
            ]
        );
    }
}
