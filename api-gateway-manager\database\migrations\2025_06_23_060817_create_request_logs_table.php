<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('request_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('api_key_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('api_proxy_id')->constrained()->onDelete('cascade');
            $table->string('ip_address', 45); // Support IPv6
            $table->string('method', 10);
            $table->string('path');
            $table->text('query_string')->nullable();
            $table->json('request_headers')->nullable();
            $table->integer('response_status');
            $table->json('response_headers')->nullable();
            $table->integer('response_time_ms'); // Response time in milliseconds
            $table->integer('response_size_bytes')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('requested_at');
            $table->timestamps();

            $table->index(['api_key_id', 'requested_at']);
            $table->index(['api_proxy_id', 'requested_at']);
            $table->index(['ip_address', 'requested_at']);
            $table->index('response_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('request_logs');
    }
};
