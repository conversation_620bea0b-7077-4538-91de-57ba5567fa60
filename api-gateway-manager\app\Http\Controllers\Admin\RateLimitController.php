<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RateLimit;
use App\Models\ApiKey;
use App\Models\ApiProxy;

class RateLimitController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = RateLimit::with(['apiKey.app.user', 'apiProxy']);

        // Filter by API key
        if ($request->filled('api_key_id')) {
            $query->where('api_key_id', $request->api_key_id);
        }

        // Filter by API proxy
        if ($request->filled('api_proxy_id')) {
            $query->where('api_proxy_id', $request->api_proxy_id);
        }

        $rateLimits = $query->orderBy('created_at', 'desc')->paginate(15);

        $apiKeys = ApiKey::with('app')->get();
        $apiProxies = ApiProxy::all();

        return view('admin.rate-limits.index', compact('rateLimits', 'apiKeys', 'apiProxies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $apiKeys = ApiKey::with('app')->get();
        $apiProxies = ApiProxy::all();

        return view('admin.rate-limits.create', compact('apiKeys', 'apiProxies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'api_key_id' => 'required|exists:api_keys,id',
            'api_proxy_id' => 'required|exists:api_proxies,id',
            'requests_per_minute' => 'required|integer|min:1|max:10000',
            'requests_per_hour' => 'required|integer|min:1|max:100000',
            'requests_per_day' => 'required|integer|min:1|max:1000000',
        ]);

        // Check if rate limit already exists
        $existing = RateLimit::where('api_key_id', $request->api_key_id)
            ->where('api_proxy_id', $request->api_proxy_id)
            ->first();

        if ($existing) {
            return back()->withErrors(['api_key_id' => 'Rate limit already exists for this API key and proxy combination.']);
        }

        RateLimit::create([
            'api_key_id' => $request->api_key_id,
            'api_proxy_id' => $request->api_proxy_id,
            'requests_per_minute' => $request->requests_per_minute,
            'requests_per_hour' => $request->requests_per_hour,
            'requests_per_day' => $request->requests_per_day,
            'current_minute_count' => 0,
            'current_hour_count' => 0,
            'current_day_count' => 0,
            'minute_reset_at' => now()->addMinute(),
            'hour_reset_at' => now()->addHour(),
            'day_reset_at' => now()->addDay(),
        ]);

        return redirect()->route('admin.rate-limits.index')
            ->with('success', 'Rate limit created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(RateLimit $rateLimit)
    {
        $rateLimit->load(['apiKey.app.user', 'apiProxy']);
        return view('admin.rate-limits.show', compact('rateLimit'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RateLimit $rateLimit)
    {
        $rateLimit->load(['apiKey.app', 'apiProxy']);
        $apiKeys = ApiKey::with('app')->get();
        $apiProxies = ApiProxy::all();

        return view('admin.rate-limits.edit', compact('rateLimit', 'apiKeys', 'apiProxies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RateLimit $rateLimit)
    {
        $request->validate([
            'requests_per_minute' => 'required|integer|min:1|max:10000',
            'requests_per_hour' => 'required|integer|min:1|max:100000',
            'requests_per_day' => 'required|integer|min:1|max:1000000',
        ]);

        $rateLimit->update([
            'requests_per_minute' => $request->requests_per_minute,
            'requests_per_hour' => $request->requests_per_hour,
            'requests_per_day' => $request->requests_per_day,
        ]);

        return redirect()->route('admin.rate-limits.show', $rateLimit)
            ->with('success', 'Rate limit updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RateLimit $rateLimit)
    {
        $rateLimit->delete();

        return redirect()->route('admin.rate-limits.index')
            ->with('success', 'Rate limit deleted successfully!');
    }

    /**
     * Reset rate limit counters
     */
    public function reset(RateLimit $rateLimit)
    {
        $rateLimit->update([
            'current_minute_count' => 0,
            'current_hour_count' => 0,
            'current_day_count' => 0,
            'minute_reset_at' => now()->addMinute(),
            'hour_reset_at' => now()->addHour(),
            'day_reset_at' => now()->addDay(),
        ]);

        return redirect()->route('admin.rate-limits.show', $rateLimit)
            ->with('success', 'Rate limit counters reset successfully!');
    }
}
