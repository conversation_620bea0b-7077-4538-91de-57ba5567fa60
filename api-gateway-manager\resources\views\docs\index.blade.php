@extends('layouts.app')

@section('title', 'API Documentation - ' . config('app.name'))
@section('meta_description', 'Complete API documentation for ' . config('app.name') . '. Learn how to integrate with our APIs, authentication, and best practices.')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-secondary-900 sm:text-4xl">
            API Documentation
        </h1>
        <p class="mt-4 text-lg text-secondary-600 max-w-3xl mx-auto">
            Complete guide to integrating with {{ config('app.name') }}. 
            Learn about authentication, endpoints, rate limits, and best practices.
        </p>
    </div>

    <!-- Quick Start -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-3 mb-12">
        <!-- Getting Started -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Quick Start</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Get up and running with our API in minutes
                </p>
                <a href="#quick-start" class="btn-outline btn-sm">
                    Get Started
                </a>
            </div>
        </div>

        <!-- API Reference -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-success-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">API Reference</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Detailed documentation for all endpoints
                </p>
                <a href="#api-reference" class="btn-outline btn-sm">
                    Browse APIs
                </a>
            </div>
        </div>

        <!-- Interactive Docs -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="h-12 w-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="h-6 w-6 text-warning-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-secondary-900 mb-2">Interactive Docs</h3>
                <p class="text-secondary-600 text-sm mb-4">
                    Test APIs directly in your browser
                </p>
                <a href="{{ route('docs.swagger') }}" class="btn-outline btn-sm">
                    Try APIs
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Start Section -->
    <div id="quick-start" class="mb-16">
        <h2 class="text-2xl font-bold text-secondary-900 mb-8">Quick Start Guide</h2>
        
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- Authentication -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">1. Authentication</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary-600 mb-4">
                        All API requests require authentication using an API key in the request header.
                    </p>
                    <div class="bg-secondary-900 rounded-lg p-4 overflow-x-auto">
                        <code class="text-green-400 text-sm">
                            <span class="text-secondary-400"># Include your API key in the header</span><br>
                            curl -H "X-API-Key: agm_your_api_key_here" \<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ config('app.url') }}/api/example
                        </code>
                    </div>
                    <div class="mt-4">
                        @auth
                            <a href="{{ route('developer.apps.index') }}" class="btn-primary btn-sm">
                                Get API Key
                            </a>
                        @else
                            <a href="{{ route('register') }}" class="btn-primary btn-sm">
                                Sign Up for API Key
                            </a>
                        @endauth
                    </div>
                </div>
            </div>

            <!-- Rate Limits -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-secondary-900">2. Rate Limits</h3>
                </div>
                <div class="card-body">
                    <p class="text-secondary-600 mb-4">
                        API requests are rate limited to ensure fair usage. Check response headers for limit information.
                    </p>
                    <div class="bg-secondary-900 rounded-lg p-4 overflow-x-auto">
                        <code class="text-green-400 text-sm">
                            <span class="text-secondary-400"># Rate limit headers in response</span><br>
                            X-RateLimit-Limit: 1000<br>
                            X-RateLimit-Remaining: 999<br>
                            X-RateLimit-Reset: 1640995200
                        </code>
                    </div>
                    <div class="mt-4 space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-secondary-600">Free Tier:</span>
                            <span class="font-medium">10,000 requests/month</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-secondary-600">Pro Tier:</span>
                            <span class="font-medium">1M requests/month</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available APIs -->
    <div id="api-reference" class="mb-16">
        <h2 class="text-2xl font-bold text-secondary-900 mb-8">Available APIs</h2>
        
        @if(isset($apiProxies) && $apiProxies->count() > 0)
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                @foreach($apiProxies as $proxy)
                <div class="card hover:shadow-lg transition-shadow">
                    <div class="card-body">
                        <div class="flex items-start justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-secondary-900">{{ $proxy->name }}</h3>
                                <p class="text-sm text-secondary-600 mt-1">{{ $proxy->description ?: 'No description available.' }}</p>
                            </div>
                            <span class="badge-success">Active</span>
                        </div>
                        
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-secondary-700">Endpoint:</span>
                                <code class="ml-2 text-sm bg-secondary-100 px-2 py-1 rounded">{{ $proxy->proxy_path }}</code>
                            </div>
                            
                            <div>
                                <span class="text-sm font-medium text-secondary-700">Methods:</span>
                                <div class="ml-2 mt-1 flex flex-wrap gap-1">
                                    @foreach($proxy->allowed_methods as $method)
                                        <span class="badge-secondary text-xs">{{ $method }}</span>
                                    @endforeach
                                </div>
                            </div>
                            
                            <div>
                                <span class="text-sm font-medium text-secondary-700">Authentication:</span>
                                <span class="ml-2 text-sm {{ $proxy->requires_auth ? 'text-warning-600' : 'text-success-600' }}">
                                    {{ $proxy->requires_auth ? 'Required' : 'Not Required' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex gap-2">
                            <a href="{{ route('docs.show', $proxy) }}" class="btn-outline btn-sm flex-1">
                                View Details
                            </a>
                            <a href="{{ route('docs.swagger') }}#{{ $proxy->proxy_path }}" class="btn-primary btn-sm flex-1">
                                Try API
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="card">
                <div class="card-body text-center py-12">
                    <svg class="h-16 w-16 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <h3 class="text-lg font-semibold text-secondary-900 mb-2">No APIs Available</h3>
                    <p class="text-secondary-600 mb-6">
                        APIs will appear here once they are configured by administrators.
                    </p>
                    <a href="{{ route('portal.support') }}" class="btn-outline">
                        Contact Support
                    </a>
                </div>
            </div>
        @endif
    </div>

    <!-- SDKs and Libraries -->
    <div class="mb-16">
        <h2 class="text-2xl font-bold text-secondary-900 mb-8">SDKs & Libraries</h2>
        
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div class="card text-center">
                <div class="card-body">
                    <div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-blue-600 font-bold">JS</span>
                    </div>
                    <h4 class="font-semibold text-secondary-900 mb-2">JavaScript</h4>
                    <p class="text-sm text-secondary-600 mb-3">Node.js and browser SDK</p>
                    <a href="#" class="btn-outline btn-sm">Coming Soon</a>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-green-600 font-bold">PY</span>
                    </div>
                    <h4 class="font-semibold text-secondary-900 mb-2">Python</h4>
                    <p class="text-sm text-secondary-600 mb-3">Python SDK and CLI</p>
                    <a href="#" class="btn-outline btn-sm">Coming Soon</a>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-red-600 font-bold">GO</span>
                    </div>
                    <h4 class="font-semibold text-secondary-900 mb-2">Go</h4>
                    <p class="text-sm text-secondary-600 mb-3">Go SDK and examples</p>
                    <a href="#" class="btn-outline btn-sm">Coming Soon</a>
                </div>
            </div>
            
            <div class="card text-center">
                <div class="card-body">
                    <div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-purple-600 font-bold">PHP</span>
                    </div>
                    <h4 class="font-semibold text-secondary-900 mb-2">PHP</h4>
                    <p class="text-sm text-secondary-600 mb-3">PHP SDK and Composer package</p>
                    <a href="#" class="btn-outline btn-sm">Coming Soon</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Support -->
    <div class="card">
        <div class="card-body text-center">
            <h2 class="text-2xl font-bold text-secondary-900 mb-4">Need Help?</h2>
            <p class="text-secondary-600 mb-6 max-w-2xl mx-auto">
                Can't find what you're looking for? Our support team is here to help you get the most out of our APIs.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('portal.support') }}" class="btn-primary">
                    Contact Support
                </a>
                <a href="{{ route('portal.getting-started') }}" class="btn-outline">
                    Getting Started Guide
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
