<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'line',
    'data' => [],
    'options' => [],
    'height' => '400',
    'responsive' => true,
    'id' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'line',
    'data' => [],
    'options' => [],
    'height' => '400',
    'responsive' => true,
    'id' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $chartId = $id ?? 'chart-' . uniqid();
    $defaultOptions = [
        'responsive' => $responsive,
        'maintainAspectRatio' => false,
        'plugins' => [
            'legend' => [
                'display' => true,
                'position' => 'top'
            ]
        ],
        'scales' => [
            'y' => [
                'beginAtZero' => true,
                'grid' => [
                    'color' => 'rgba(0, 0, 0, 0.1)'
                ]
            ],
            'x' => [
                'grid' => [
                    'color' => 'rgba(0, 0, 0, 0.1)'
                ]
            ]
        ]
    ];
    
    $chartOptions = array_merge_recursive($defaultOptions, $options);
?>

<div class="chart-container" style="position: relative; height: <?php echo e($height); ?>px;">
    <canvas id="<?php echo e($chartId); ?>" class="chart-canvas"></canvas>
</div>

<?php if (! $__env->hasRenderedOnce('7e80d994-d1e8-4da4-9f1f-52eb40e6425a')): $__env->markAsRenderedOnce('7e80d994-d1e8-4da4-9f1f-52eb40e6425a'); ?>
<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
<?php $__env->stopPush(); ?>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('<?php echo e($chartId); ?>').getContext('2d');
    
    const chartData = <?php echo json_encode($data, 15, 512) ?>;
    const chartOptions = <?php echo json_encode($chartOptions, 15, 512) ?>;
    
    // Mobile-specific adjustments
    if (window.innerWidth < 768) {
        chartOptions.plugins.legend.position = 'bottom';
        chartOptions.plugins.legend.labels = {
            ...chartOptions.plugins.legend.labels,
            boxWidth: 12,
            padding: 10,
            font: {
                size: 12
            }
        };
        
        if (chartOptions.scales) {
            if (chartOptions.scales.x && chartOptions.scales.x.ticks) {
                chartOptions.scales.x.ticks.maxRotation = 45;
                chartOptions.scales.x.ticks.font = { size: 10 };
            }
            if (chartOptions.scales.y && chartOptions.scales.y.ticks) {
                chartOptions.scales.y.ticks.font = { size: 10 };
            }
        }
    }
    
    const chart = new Chart(ctx, {
        type: '<?php echo e($type); ?>',
        data: chartData,
        options: chartOptions
    });
    
    // Store chart instance for potential updates
    window.charts = window.charts || {};
    window.charts['<?php echo e($chartId); ?>'] = chart;
    
    // Handle window resize for mobile
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (chart) {
                chart.resize();
            }
        }, 250);
    });
});

// Helper function to update chart data
function updateChart(chartId, newData) {
    if (window.charts && window.charts[chartId]) {
        const chart = window.charts[chartId];
        chart.data = newData;
        chart.update();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/components/chart.blade.php ENDPATH**/ ?>