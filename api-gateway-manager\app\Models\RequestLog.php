<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RequestLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'api_key_id',
        'api_proxy_id',
        'ip_address',
        'method',
        'path',
        'query_string',
        'request_headers',
        'response_status',
        'response_headers',
        'response_time_ms',
        'response_size_bytes',
        'error_message',
        'requested_at',
    ];

    protected function casts(): array
    {
        return [
            'request_headers' => 'array',
            'response_headers' => 'array',
            'response_status' => 'integer',
            'response_time_ms' => 'integer',
            'response_size_bytes' => 'integer',
            'requested_at' => 'datetime',
        ];
    }

    /**
     * Get the API key that made this request
     */
    public function apiKey()
    {
        return $this->belongsTo(ApiKey::class);
    }

    /**
     * Get the API proxy that handled this request
     */
    public function apiProxy()
    {
        return $this->belongsTo(ApiProxy::class);
    }

    /**
     * Scope to get logs from the last N days
     */
    public function scopeLastDays($query, int $days = 30)
    {
        return $query->where('requested_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get logs by status code
     */
    public function scopeByStatus($query, int $status)
    {
        return $query->where('response_status', $status);
    }

    /**
     * Scope to get error logs (4xx and 5xx)
     */
    public function scopeErrors($query)
    {
        return $query->where('response_status', '>=', 400);
    }

    /**
     * Scope to get successful logs (2xx and 3xx)
     */
    public function scopeSuccessful($query)
    {
        return $query->where('response_status', '<', 400);
    }

    /**
     * Create a new request log entry
     */
    public static function createLog(array $data): self
    {
        return static::create(array_merge($data, [
            'requested_at' => now(),
        ]));
    }

    /**
     * Get average response time for a period
     */
    public static function getAverageResponseTime(int $days = 7): float
    {
        return static::lastDays($days)
            ->avg('response_time_ms') ?? 0;
    }

    /**
     * Get request count by status for a period
     */
    public static function getStatusCounts(int $days = 7): array
    {
        return static::lastDays($days)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN response_status < 300 THEN 1 ELSE 0 END) as success,
                SUM(CASE WHEN response_status >= 400 AND response_status < 500 THEN 1 ELSE 0 END) as client_errors,
                SUM(CASE WHEN response_status >= 500 THEN 1 ELSE 0 END) as server_errors
            ')
            ->first()
            ->toArray();
    }
}
