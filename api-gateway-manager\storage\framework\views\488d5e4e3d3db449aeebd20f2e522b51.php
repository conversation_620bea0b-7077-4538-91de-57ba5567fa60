<?php $__env->startSection('sidebar'); ?>
<!-- Sidebar Logo -->
<div class="flex items-center justify-center h-16 px-4 border-b border-secondary-200">
    <div class="flex items-center">
        <div class="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-sm">AG</span>
        </div>
        <span class="ml-2 text-lg font-semibold text-secondary-900">Developer</span>
    </div>
</div>

<!-- Sidebar Navigation -->
<nav class="flex-1 px-4 py-6 space-y-2">
    <a href="<?php echo e(route('developer.dashboard')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.dashboard') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
        Dashboard
    </a>

    <a href="<?php echo e(route('developer.apps.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.apps.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        My Apps
    </a>

    <a href="<?php echo e(route('developer.api-keys.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.api-keys.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
        </svg>
        API Keys
    </a>

    <a href="<?php echo e(route('developer.analytics.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.analytics.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Analytics
    </a>

    <a href="<?php echo e(route('developer.documentation')); ?>"
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.documentation') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        Documentation
    </a>

    <a href="<?php echo e(route('developer.support')); ?>"
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.support') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Support
    </a>
</nav>

<!-- Sidebar Footer -->
<div class="px-4 py-4 border-t border-secondary-200">
    <div class="flex items-center">
        <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
            <span class="text-sm font-medium text-white">
                <?php echo e(substr(auth()->user()->name, 0, 1)); ?>

            </span>
        </div>
        <div class="ml-3 flex-1 min-w-0">
            <p class="text-sm font-medium text-secondary-900 truncate">
                <?php echo e(auth()->user()->name); ?>

            </p>
            <p class="text-xs text-secondary-500 truncate">
                Developer
            </p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('mobile-menu'); ?>
<!-- Mobile Navigation -->
<nav class="space-y-2">
    <a href="<?php echo e(route('developer.dashboard')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.dashboard') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
        </svg>
        Dashboard
    </a>

    <a href="<?php echo e(route('developer.apps.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.apps.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        My Apps
    </a>

    <a href="<?php echo e(route('developer.api-keys.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.api-keys.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
        </svg>
        API Keys
    </a>

    <a href="<?php echo e(route('developer.analytics.index')); ?>" 
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.analytics.*') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        Analytics
    </a>

    <div class="border-t border-secondary-200 my-4"></div>

    <a href="<?php echo e(route('developer.documentation')); ?>"
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.documentation') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
        Documentation
    </a>

    <a href="<?php echo e(route('developer.support')); ?>"
       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('developer.support') ? 'bg-primary-100 text-primary-700' : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'); ?>">
        <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        Support
    </a>
</nav>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-title', $pageTitle ?? 'Developer Dashboard'); ?>

<?php $__env->startSection('header-actions'); ?>
<!-- Quick Actions -->
<div class="hidden sm:flex items-center space-x-3">
    <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-primary btn-sm">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        New App
    </a>
</div>

<!-- Mobile Quick Action -->
<div class="sm:hidden">
    <a href="<?php echo e(route('developer.apps.create')); ?>" class="btn-primary btn-sm">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/layouts/developer.blade.php ENDPATH**/ ?>