<?php
    $pageTitle = 'Create New App';
?>

<?php $__env->startSection('title', 'Create New App - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="<?php echo e(route('developer.apps.index')); ?>" class="text-secondary-400 hover:text-secondary-600 mr-4">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
                Create New Application
            </h1>
        </div>
        <p class="text-secondary-600">
            Create a new application to organize your API keys and track usage.
        </p>
    </div>

    <!-- Form -->
    <div class="max-w-2xl">
        <form method="POST" action="<?php echo e(route('developer.apps.store')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>

            <!-- App Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-secondary-700 mb-2">
                    Application Name <span class="text-danger-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required
                       value="<?php echo e(old('name')); ?>"
                       class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-danger-300 focus:border-danger-500 focus:ring-danger-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="Enter a descriptive name for your app">
                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-2 text-sm text-danger-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <p class="mt-2 text-sm text-secondary-500">
                    Choose a clear, descriptive name that identifies your application.
                </p>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-secondary-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          class="form-textarea <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-danger-300 focus:border-danger-500 focus:ring-danger-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                          placeholder="Describe what your application does and how it will use the API..."><?php echo e(old('description')); ?></textarea>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-2 text-sm text-danger-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <p class="mt-2 text-sm text-secondary-500">
                    Provide a brief description of your application and its purpose.
                </p>
            </div>

            <!-- Callback URL -->
            <div>
                <label for="callback_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Callback URL
                </label>
                <input type="url" 
                       id="callback_url" 
                       name="callback_url"
                       value="<?php echo e(old('callback_url')); ?>"
                       class="form-input <?php $__errorArgs = ['callback_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-danger-300 focus:border-danger-500 focus:ring-danger-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="https://your-app.com/callback">
                <?php $__errorArgs = ['callback_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-2 text-sm text-danger-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. URL where users will be redirected after authentication (if applicable).
                </p>
            </div>

            <!-- Website URL -->
            <div>
                <label for="website_url" class="block text-sm font-medium text-secondary-700 mb-2">
                    Website URL
                </label>
                <input type="url" 
                       id="website_url" 
                       name="website_url"
                       value="<?php echo e(old('website_url')); ?>"
                       class="form-input <?php $__errorArgs = ['website_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-danger-300 focus:border-danger-500 focus:ring-danger-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                       placeholder="https://your-app.com">
                <?php $__errorArgs = ['website_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <p class="mt-2 text-sm text-danger-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                <p class="mt-2 text-sm text-secondary-500">
                    Optional. Your application's website or homepage URL.
                </p>
            </div>

            <!-- App Status -->
            <div>
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           value="1"
                           <?php echo e(old('is_active', true) ? 'checked' : ''); ?>

                           class="form-checkbox">
                    <label for="is_active" class="ml-2 block text-sm text-secondary-700">
                        Active application
                    </label>
                </div>
                <p class="mt-2 text-sm text-secondary-500">
                    Active applications can generate and use API keys. Inactive applications cannot make API calls.
                </p>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-secondary-200">
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Application
                </button>
                <a href="<?php echo e(route('developer.apps.index')); ?>" class="btn-outline">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Help Section -->
    <div class="mt-12 max-w-2xl">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold text-secondary-900 mb-4">
                    What happens next?
                </h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">1</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Application Created</h4>
                            <p class="text-sm text-secondary-600">Your application will be created and ready to use.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">2</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Generate API Keys</h4>
                            <p class="text-sm text-secondary-600">Create API keys to authenticate your requests.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-primary-600 font-semibold text-sm">3</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-secondary-900">Start Building</h4>
                            <p class="text-sm text-secondary-600">Use your API keys to make authenticated requests.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.developer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\augment-projects\myApigee\api-gateway-manager\resources\views/developer/apps/create.blade.php ENDPATH**/ ?>