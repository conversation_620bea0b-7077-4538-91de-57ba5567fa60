<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ApiProxy;

class ApiDocumentationController extends Controller
{
    /**
     * Show the public API documentation
     */
    public function index()
    {
        $apiProxies = ApiProxy::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('docs.index', compact('apiProxies'));
    }

    /**
     * Show documentation for a specific API proxy
     */
    public function show(ApiProxy $apiProxy)
    {
        if (!$apiProxy->is_active) {
            abort(404);
        }

        return view('docs.show', compact('apiProxy'));
    }

    /**
     * Generate OpenAPI/Swagger specification
     */
    public function openapi()
    {
        $apiProxies = ApiProxy::where('is_active', true)->get();

        $spec = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => config('app.name') . ' API',
                'description' => 'API Gateway providing access to various backend services',
                'version' => '1.0.0',
                'contact' => [
                    'name' => 'API Support',
                    'email' => 'support@' . parse_url(config('app.url'), PHP_URL_HOST),
                ],
            ],
            'servers' => [
                [
                    'url' => config('app.url') . '/api',
                    'description' => 'Production server',
                ],
            ],
            'components' => [
                'securitySchemes' => [
                    'ApiKeyAuth' => [
                        'type' => 'apiKey',
                        'in' => 'header',
                        'name' => 'X-API-Key',
                    ],
                ],
                'responses' => [
                    'UnauthorizedError' => [
                        'description' => 'API key is missing or invalid',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'error' => ['type' => 'string'],
                                        'message' => ['type' => 'string'],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'RateLimitError' => [
                        'description' => 'Rate limit exceeded',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'error' => ['type' => 'string'],
                                        'message' => ['type' => 'string'],
                                        'limits' => ['type' => 'object'],
                                        'current' => ['type' => 'object'],
                                        'reset_times' => ['type' => 'object'],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'paths' => [],
        ];

        // Generate paths for each API proxy
        foreach ($apiProxies as $proxy) {
            $pathKey = $proxy->proxy_path;

            $spec['paths'][$pathKey] = [];

            foreach ($proxy->allowed_methods as $method) {
                $methodLower = strtolower($method);

                $spec['paths'][$pathKey][$methodLower] = [
                    'summary' => $proxy->name,
                    'description' => $proxy->description ?: "Access to {$proxy->name} API",
                    'tags' => [$proxy->name],
                    'responses' => [
                        '200' => [
                            'description' => 'Successful response',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'description' => 'Response from the backend service',
                                    ],
                                ],
                            ],
                        ],
                        '401' => ['$ref' => '#/components/responses/UnauthorizedError'],
                        '429' => ['$ref' => '#/components/responses/RateLimitError'],
                    ],
                ];

                if ($proxy->requires_auth) {
                    $spec['paths'][$pathKey][$methodLower]['security'] = [
                        ['ApiKeyAuth' => []],
                    ];
                }

                // Add parameters for POST/PUT/PATCH methods
                if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
                    $spec['paths'][$pathKey][$methodLower]['requestBody'] = [
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'description' => 'Request payload',
                                ],
                            ],
                        ],
                    ];
                }
            }
        }

        return response()->json($spec, 200, [
            'Content-Type' => 'application/json',
        ]);
    }

    /**
     * Show Swagger UI
     */
    public function swagger()
    {
        return view('docs.swagger');
    }
}
