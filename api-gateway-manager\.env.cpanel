APP_NAME="API Gateway Manager"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://yourdomain.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=single
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration for cPanel
# Update these with your cPanel database credentials
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

# Session Configuration
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Broadcasting (not needed for basic setup)
BROADCAST_CONNECTION=log

# Filesystem
FILESYSTEM_DISK=local

# Queue Configuration (use database for cPanel)
QUEUE_CONNECTION=database

# Cache Configuration (use database for cPanel)
CACHE_STORE=database

# Mail Configuration (update with your cPanel email settings)
MAIL_MAILER=smtp
MAIL_HOST=mail.yourdomain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS (not typically used in cPanel hosting)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Vite
VITE_APP_NAME="${APP_NAME}"

# API Gateway Specific Settings
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_DEFAULT_PER_MINUTE=60
API_RATE_LIMIT_DEFAULT_PER_HOUR=1000
API_RATE_LIMIT_DEFAULT_PER_DAY=10000

# Request Logging
REQUEST_LOGGING_ENABLED=true
REQUEST_LOGGING_RETENTION_DAYS=30

# Security Settings
SECURE_HEADERS_ENABLED=true
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS="*"
CORS_ALLOWED_METHODS="GET,POST,PUT,PATCH,DELETE,OPTIONS"
CORS_ALLOWED_HEADERS="Content-Type,X-API-Key,Authorization,X-Requested-With"
