<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create a sample developer user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Developer User',
                'password' => Hash::make('password'),
                'role' => 'developer',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
    }
}
