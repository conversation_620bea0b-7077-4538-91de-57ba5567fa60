@extends('layouts.developer')

@php
    $pageTitle = 'Analytics';
@endphp

@section('title', 'Analytics - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">
            Analytics Dashboard
        </h1>
        <p class="mt-2 text-secondary-600">
            Monitor your API usage, performance, and trends.
        </p>
    </div>

    <!-- Time Range Filter -->
    <div class="card mb-6">
        <div class="card-body">
            <form method="GET" class="flex flex-col sm:flex-row sm:items-end sm:space-x-4 space-y-4 sm:space-y-0">
                <div class="flex-1">
                    <label for="date_range" class="block text-sm font-medium text-secondary-700 mb-1">Time Range</label>
                    <select name="date_range" id="date_range" class="form-select" onchange="this.form.submit()">
                        <option value="7d" {{ request('date_range', '7d') === '7d' ? 'selected' : '' }}>Last 7 days</option>
                        <option value="30d" {{ request('date_range') === '30d' ? 'selected' : '' }}>Last 30 days</option>
                        <option value="90d" {{ request('date_range') === '90d' ? 'selected' : '' }}>Last 90 days</option>
                        <option value="1y" {{ request('date_range') === '1y' ? 'selected' : '' }}>Last year</option>
                    </select>
                </div>
                
                <div class="sm:w-48">
                    <label for="app_filter" class="block text-sm font-medium text-secondary-700 mb-1">Application</label>
                    <select name="app_filter" id="app_filter" class="form-select" onchange="this.form.submit()">
                        <option value="">All Apps</option>
                        @if(isset($userApps))
                            @foreach($userApps as $app)
                                <option value="{{ $app->id }}" {{ request('app_filter') == $app->id ? 'selected' : '' }}>
                                    {{ $app->name }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-2 gap-4 sm:grid-cols-4 mb-8">
        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-primary-600">{{ number_format($metrics['total_requests'] ?? 0) }}</div>
                <div class="text-sm text-secondary-600 mt-1">Total Requests</div>
                @if(isset($metrics['requests_change']))
                <div class="text-xs mt-1 {{ $metrics['requests_change'] >= 0 ? 'text-success-600' : 'text-danger-600' }}">
                    {{ $metrics['requests_change'] >= 0 ? '+' : '' }}{{ number_format($metrics['requests_change'], 1) }}%
                </div>
                @endif
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-success-600">{{ number_format($metrics['success_rate'] ?? 0, 1) }}%</div>
                <div class="text-sm text-secondary-600 mt-1">Success Rate</div>
                @if(isset($metrics['success_change']))
                <div class="text-xs mt-1 {{ $metrics['success_change'] >= 0 ? 'text-success-600' : 'text-danger-600' }}">
                    {{ $metrics['success_change'] >= 0 ? '+' : '' }}{{ number_format($metrics['success_change'], 1) }}%
                </div>
                @endif
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-warning-600">{{ number_format($metrics['avg_response_time'] ?? 0) }}ms</div>
                <div class="text-sm text-secondary-600 mt-1">Avg Response</div>
                @if(isset($metrics['response_change']))
                <div class="text-xs mt-1 {{ $metrics['response_change'] <= 0 ? 'text-success-600' : 'text-danger-600' }}">
                    {{ $metrics['response_change'] >= 0 ? '+' : '' }}{{ number_format($metrics['response_change'], 1) }}%
                </div>
                @endif
            </div>
        </div>

        <div class="card">
            <div class="card-body text-center">
                <div class="text-2xl font-bold text-danger-600">{{ number_format($metrics['error_count'] ?? 0) }}</div>
                <div class="text-sm text-secondary-600 mt-1">Errors</div>
                @if(isset($metrics['error_change']))
                <div class="text-xs mt-1 {{ $metrics['error_change'] <= 0 ? 'text-success-600' : 'text-danger-600' }}">
                    {{ $metrics['error_change'] >= 0 ? '+' : '' }}{{ number_format($metrics['error_change'], 1) }}%
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <!-- Requests Over Time -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Requests Over Time</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="line"
                    :data="$requestsChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Requests'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ]"
                    height="300"
                    id="requests-chart" />
            </div>
        </div>

        <!-- Response Time Trend -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Response Time Trend</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="line"
                    :data="$responseTimeChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => ['display' => false]
                        ],
                        'scales' => [
                            'y' => [
                                'beginAtZero' => true,
                                'title' => [
                                    'display' => true,
                                    'text' => 'Response Time (ms)'
                                ]
                            ],
                            'x' => [
                                'title' => [
                                    'display' => true,
                                    'text' => 'Date'
                                ]
                            ]
                        ]
                    ]"
                    height="300"
                    id="response-time-chart" />
            </div>
        </div>
    </div>

    <!-- Status Codes Distribution -->
    <div class="grid grid-cols-1 gap-8 lg:grid-cols-2 mb-8">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Status Codes Distribution</h3>
            </div>
            <div class="card-body">
                <x-chart 
                    type="doughnut"
                    :data="$statusCodesChart ?? []"
                    :options="[
                        'plugins' => [
                            'legend' => [
                                'position' => 'bottom'
                            ]
                        ]
                    ]"
                    height="300"
                    id="status-codes-chart" />
            </div>
        </div>

        <!-- Top Endpoints -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Top Endpoints</h3>
            </div>
            <div class="card-body">
                @if(isset($topEndpoints) && count($topEndpoints) > 0)
                    <div class="space-y-4">
                        @foreach($topEndpoints as $endpoint)
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-secondary-900 truncate">
                                    {{ $endpoint['method'] }} {{ $endpoint['path'] }}
                                </div>
                                <div class="text-xs text-secondary-500">
                                    {{ number_format($endpoint['requests']) }} requests
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <div class="w-20 bg-secondary-200 rounded-full h-2">
                                    <div class="bg-primary-600 h-2 rounded-full" 
                                         style="width: {{ $endpoint['percentage'] }}%"></div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        <h3 class="text-sm font-medium text-secondary-900 mb-2">No data available</h3>
                        <p class="text-sm text-secondary-500">Start making API calls to see analytics</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-secondary-900">Recent Activity</h3>
                <a href="{{ route('developer.analytics.logs') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    View all logs
                </a>
            </div>
        </div>
        <div class="card-body">
            @if(isset($recentLogs) && $recentLogs->count() > 0)
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead class="table-header">
                            <tr>
                                <th class="table-header-cell">Time</th>
                                <th class="table-header-cell">Method</th>
                                <th class="table-header-cell">Endpoint</th>
                                <th class="table-header-cell">Status</th>
                                <th class="table-header-cell">Response Time</th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            @foreach($recentLogs as $log)
                            <tr>
                                <td class="table-cell">
                                    <span class="text-xs text-secondary-500">
                                        {{ $log->requested_at->format('H:i:s') }}
                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-{{ $log->method === 'GET' ? 'success' : ($log->method === 'POST' ? 'primary' : 'warning') }} text-xs">
                                        {{ $log->method }}
                                    </span>
                                </td>
                                <td class="table-cell">
                                    <code class="text-xs">{{ $log->path }}</code>
                                </td>
                                <td class="table-cell">
                                    <span class="badge-{{ $log->response_status >= 200 && $log->response_status < 300 ? 'success' : ($log->response_status >= 400 ? 'danger' : 'warning') }} text-xs">
                                        {{ $log->response_status }}
                                    </span>
                                </td>
                                <td class="table-cell">
                                    <span class="text-sm">{{ $log->response_time_ms }}ms</span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="h-12 w-12 text-secondary-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="text-sm font-medium text-secondary-900 mb-2">No activity yet</h3>
                    <p class="text-sm text-secondary-500">API requests will appear here once you start making calls</p>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-refresh analytics data every 30 seconds
let refreshInterval;

function startAutoRefresh() {
    refreshInterval = setInterval(function() {
        // Only refresh if the page is visible
        if (!document.hidden) {
            refreshAnalytics();
        }
    }, 30000);
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
}

async function refreshAnalytics() {
    try {
        const params = new URLSearchParams(window.location.search);
        const response = await fetch(window.location.pathname + '?' + params.toString(), {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Update metrics
            if (data.metrics) {
                updateMetrics(data.metrics);
            }
            
            // Update charts
            if (data.charts) {
                updateCharts(data.charts);
            }
        }
    } catch (error) {
        console.error('Failed to refresh analytics:', error);
    }
}

function updateMetrics(metrics) {
    // Update metric values
    Object.keys(metrics).forEach(key => {
        const element = document.querySelector(`[data-metric="${key}"]`);
        if (element) {
            element.textContent = metrics[key];
        }
    });
}

function updateCharts(charts) {
    // Update chart data
    if (charts.requests && window.charts['requests-chart']) {
        updateChart('requests-chart', charts.requests);
    }
    
    if (charts.responseTime && window.charts['response-time-chart']) {
        updateChart('response-time-chart', charts.responseTime);
    }
    
    if (charts.statusCodes && window.charts['status-codes-chart']) {
        updateChart('status-codes-chart', charts.statusCodes);
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
    }
});

// Stop auto-refresh when leaving page
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});
</script>
@endpush
@endsection
