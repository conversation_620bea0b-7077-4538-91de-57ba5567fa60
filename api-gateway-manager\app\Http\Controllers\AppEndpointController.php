<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\App;
use App\Models\ApiProxy;

class AppEndpointController extends Controller
{
    /**
     * Display a listing of endpoints for an app.
     */
    public function index(App $app)
    {
        $this->authorize('view', $app);
        
        $endpoints = $app->apiProxies()->orderBy('created_at', 'desc')->paginate(10);
        
        return view('developer.apps.endpoints.index', compact('app', 'endpoints'));
    }

    /**
     * Show the form for creating a new endpoint.
     */
    public function create(App $app)
    {
        $this->authorize('update', $app);
        
        return view('developer.apps.endpoints.create', compact('app'));
    }

    /**
     * Store a newly created endpoint.
     */
    public function store(Request $request, App $app)
    {
        $this->authorize('update', $app);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'proxy_path' => 'required|string|max:255|unique:api_proxies,proxy_path',
            'target_url' => 'required|string|max:255',
            'allowed_methods' => 'required|array|min:1',
            'allowed_methods.*' => 'in:GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS',
            'headers_to_add' => 'nullable|array',
            'headers_to_add.*.name' => 'required_with:headers_to_add|string|max:100',
            'headers_to_add.*.value' => 'required_with:headers_to_add|string|max:500',
            'headers_to_remove' => 'nullable|array',
            'headers_to_remove.*' => 'string|max:100',
            'requires_auth' => 'boolean',
            'timeout' => 'integer|min:1|max:300',
        ]);

        // Process headers to add
        $headersToAdd = [];
        if ($request->has('headers_to_add')) {
            foreach ($request->headers_to_add as $header) {
                if (!empty($header['name']) && !empty($header['value'])) {
                    $headersToAdd[$header['name']] = $header['value'];
                }
            }
        }

        // Merge app's custom headers with endpoint headers
        if ($app->custom_headers) {
            $headersToAdd = array_merge($app->custom_headers, $headersToAdd);
        }

        // Add authorization token if set
        if ($app->authorization_token) {
            $headersToAdd['Authorization'] = $app->authorization_token;
        }

        $endpoint = $app->apiProxies()->create([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'proxy_path' => $request->proxy_path,
            'target_url' => $this->buildTargetUrl($app, $request->target_url),
            'allowed_methods' => $request->allowed_methods,
            'headers_to_add' => !empty($headersToAdd) ? $headersToAdd : null,
            'headers_to_remove' => $request->headers_to_remove ?: null,
            'requires_auth' => $request->boolean('requires_auth'),
            'is_active' => true,
            'timeout' => $request->timeout ?: 30,
        ]);

        return redirect()->route('developer.apps.endpoints.show', [$app, $endpoint])
            ->with('success', 'Endpoint created successfully!');
    }

    /**
     * Display the specified endpoint.
     */
    public function show(App $app, ApiProxy $endpoint)
    {
        $this->authorize('view', $app);
        
        if ($endpoint->app_id !== $app->id) {
            abort(404);
        }
        
        return view('developer.apps.endpoints.show', compact('app', 'endpoint'));
    }

    /**
     * Show the form for editing the specified endpoint.
     */
    public function edit(App $app, ApiProxy $endpoint)
    {
        $this->authorize('update', $app);
        
        if ($endpoint->app_id !== $app->id) {
            abort(404);
        }
        
        return view('developer.apps.endpoints.edit', compact('app', 'endpoint'));
    }

    /**
     * Update the specified endpoint.
     */
    public function update(Request $request, App $app, ApiProxy $endpoint)
    {
        $this->authorize('update', $app);
        
        if ($endpoint->app_id !== $app->id) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'proxy_path' => 'required|string|max:255|unique:api_proxies,proxy_path,' . $endpoint->id,
            'target_url' => 'required|string|max:255',
            'allowed_methods' => 'required|array|min:1',
            'allowed_methods.*' => 'in:GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS',
            'headers_to_add' => 'nullable|array',
            'headers_to_add.*.name' => 'required_with:headers_to_add|string|max:100',
            'headers_to_add.*.value' => 'required_with:headers_to_add|string|max:500',
            'headers_to_remove' => 'nullable|array',
            'headers_to_remove.*' => 'string|max:100',
            'requires_auth' => 'boolean',
            'is_active' => 'boolean',
            'timeout' => 'integer|min:1|max:300',
        ]);

        // Process headers to add
        $headersToAdd = [];
        if ($request->has('headers_to_add')) {
            foreach ($request->headers_to_add as $header) {
                if (!empty($header['name']) && !empty($header['value'])) {
                    $headersToAdd[$header['name']] = $header['value'];
                }
            }
        }

        // Merge app's custom headers with endpoint headers
        if ($app->custom_headers) {
            $headersToAdd = array_merge($app->custom_headers, $headersToAdd);
        }

        // Add authorization token if set
        if ($app->authorization_token) {
            $headersToAdd['Authorization'] = $app->authorization_token;
        }

        $endpoint->update([
            'name' => $request->name,
            'slug' => Str::slug($request->name),
            'description' => $request->description,
            'proxy_path' => $request->proxy_path,
            'target_url' => $this->buildTargetUrl($app, $request->target_url),
            'allowed_methods' => $request->allowed_methods,
            'headers_to_add' => !empty($headersToAdd) ? $headersToAdd : null,
            'headers_to_remove' => $request->headers_to_remove ?: null,
            'requires_auth' => $request->boolean('requires_auth'),
            'is_active' => $request->boolean('is_active'),
            'timeout' => $request->timeout ?: 30,
        ]);

        return redirect()->route('developer.apps.endpoints.show', [$app, $endpoint])
            ->with('success', 'Endpoint updated successfully!');
    }

    /**
     * Remove the specified endpoint.
     */
    public function destroy(App $app, ApiProxy $endpoint)
    {
        $this->authorize('update', $app);
        
        if ($endpoint->app_id !== $app->id) {
            abort(404);
        }

        $endpoint->delete();

        return redirect()->route('developer.apps.endpoints.index', $app)
            ->with('success', 'Endpoint deleted successfully!');
    }

    /**
     * Test the endpoint
     */
    public function test(Request $request, App $app, ApiProxy $endpoint)
    {
        $this->authorize('view', $app);
        
        if ($endpoint->app_id !== $app->id) {
            abort(404);
        }

        $request->validate([
            'method' => 'required|in:GET,POST,PUT,PATCH,DELETE',
            'test_data' => 'nullable|json',
        ]);

        // Here you could implement actual endpoint testing
        // For now, we'll just return a success response
        
        return response()->json([
            'success' => true,
            'message' => 'Endpoint test completed successfully',
            'endpoint' => $endpoint->proxy_path,
            'method' => $request->method,
        ]);
    }

    /**
     * Build the target URL combining app base URL with endpoint URL
     */
    private function buildTargetUrl(App $app, string $targetUrl): string
    {
        if ($app->base_url && !str_starts_with($targetUrl, 'http')) {
            return rtrim($app->base_url, '/') . '/' . ltrim($targetUrl, '/');
        }
        
        return rtrim($targetUrl, '/');
    }
}
