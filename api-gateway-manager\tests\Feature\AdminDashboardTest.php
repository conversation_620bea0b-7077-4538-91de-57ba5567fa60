<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\RequestLog;
use App\Models\ApiProxy;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_dashboard()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    public function test_developer_cannot_access_admin_dashboard()
    {
        $developer = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($developer)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_admin_dashboard()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_admin_dashboard_shows_correct_statistics()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create test data
        $users = User::factory()->count(5)->create(['role' => 'developer']);
        $apps = App::factory()->count(3)->create(['user_id' => $users->first()->id]);
        $apiKeys = ApiKey::factory()->count(4)->create(['app_id' => $apps->first()->id]);
        $apiProxies = ApiProxy::factory()->count(2)->create();
        
        RequestLog::factory()->count(10)->create(['api_key_id' => $apiKeys->first()->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(6, $stats['total_users']); // 5 developers + 1 admin
        $this->assertEquals(3, $stats['total_apps']);
        $this->assertEquals(4, $stats['total_api_keys']);
        $this->assertEquals(2, $stats['total_api_proxies']);
        $this->assertEquals(10, $stats['total_requests']);
    }

    public function test_admin_dashboard_shows_recent_activity()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        RequestLog::factory()->count(15)->create(['api_key_id' => $apiKey->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recent_activity');
        
        $recentActivity = $response->viewData('recent_activity');
        $this->assertCount(10, $recentActivity); // Should limit to 10
    }

    public function test_admin_dashboard_shows_recent_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        User::factory()->count(8)->create(['role' => 'developer']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recent_users');
        
        $recentUsers = $response->viewData('recent_users');
        $this->assertCount(5, $recentUsers); // Should limit to 5
    }

    public function test_admin_dashboard_calculates_success_rate()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        // Create 8 successful and 2 failed requests
        RequestLog::factory()->count(8)->successful()->create(['api_key_id' => $apiKey->id]);
        RequestLog::factory()->count(2)->failed()->create(['api_key_id' => $apiKey->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        
        $stats = $response->viewData('stats');
        $this->assertEquals(80.0, $stats['success_rate']); // 8/10 * 100 = 80%
    }

    public function test_admin_dashboard_handles_no_data()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        
        $stats = $response->viewData('stats');
        $this->assertEquals(1, $stats['total_users']); // Just the admin
        $this->assertEquals(0, $stats['total_apps']);
        $this->assertEquals(0, $stats['total_api_keys']);
        $this->assertEquals(0, $stats['total_requests']);
        $this->assertEquals(100.0, $stats['success_rate']); // Default when no requests
    }

    public function test_admin_dashboard_shows_system_health()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('system_health');
        
        $systemHealth = $response->viewData('system_health');
        $this->assertArrayHasKey('database', $systemHealth);
        $this->assertArrayHasKey('cache', $systemHealth);
        $this->assertArrayHasKey('queue', $systemHealth);
    }

    public function test_admin_dashboard_shows_top_apps()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        
        $app1 = App::factory()->create(['user_id' => $user->id, 'name' => 'Popular App']);
        $app2 = App::factory()->create(['user_id' => $user->id, 'name' => 'Less Popular App']);
        
        $apiKey1 = ApiKey::factory()->create(['app_id' => $app1->id]);
        $apiKey2 = ApiKey::factory()->create(['app_id' => $app2->id]);
        
        // Make app1 more popular
        RequestLog::factory()->count(10)->create(['api_key_id' => $apiKey1->id]);
        RequestLog::factory()->count(3)->create(['api_key_id' => $apiKey2->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('top_apps');
        
        $topApps = $response->viewData('top_apps');
        $this->assertEquals('Popular App', $topApps->first()->name);
    }

    public function test_admin_dashboard_shows_error_rate_trends()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        // Create requests with different timestamps
        RequestLog::factory()->count(5)->successful()->create([
            'api_key_id' => $apiKey->id,
            'requested_at' => now()->subHours(2)
        ]);
        RequestLog::factory()->count(2)->failed()->create([
            'api_key_id' => $apiKey->id,
            'requested_at' => now()->subHour()
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('hourly_stats');
    }

    public function test_admin_dashboard_performance()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create a large dataset to test performance
        $users = User::factory()->count(100)->create(['role' => 'developer']);
        $apps = App::factory()->count(50)->create(['user_id' => $users->random()->id]);
        $apiKeys = ApiKey::factory()->count(200)->create(['app_id' => $apps->random()->id]);
        
        // Don't create too many request logs to keep test fast
        RequestLog::factory()->count(100)->create(['api_key_id' => $apiKeys->random()->id]);

        $startTime = microtime(true);
        $response = $this->actingAs($admin)->get('/admin/dashboard');
        $endTime = microtime(true);

        $response->assertStatus(200);
        
        // Dashboard should load in reasonable time (less than 2 seconds)
        $this->assertLessThan(2.0, $endTime - $startTime);
    }
}
