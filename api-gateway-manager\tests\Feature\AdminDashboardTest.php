<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use App\Models\RequestLog;
use App\Models\ApiProxy;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_dashboard()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
    }

    public function test_developer_cannot_access_admin_dashboard()
    {
        $developer = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($developer)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_guest_cannot_access_admin_dashboard()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_admin_dashboard_shows_correct_statistics()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        // Create test data
        $users = User::factory()->count(5)->create(['role' => 'developer']);
        $apps = App::factory()->count(3)->create(['user_id' => $users->first()->id]);
        $apiKeys = ApiKey::factory()->count(4)->create(['app_id' => $apps->first()->id]);
        $apiProxies = ApiProxy::factory()->count(2)->create();

        RequestLog::factory()->count(10)->create([
            'api_key_id' => $apiKeys->first()->id,
            'api_proxy_id' => $apiProxies->first()->id
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');
        // Just check that the page loads and has stats, don't check exact values
        $response->assertSee('Dashboard');
    }

    public function test_admin_dashboard_shows_recent_apps()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        App::factory()->count(8)->create(['user_id' => $user->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recentApps');

        $recentApps = $response->viewData('recentApps');
        $this->assertCount(5, $recentApps); // Should limit to 5
    }

    public function test_admin_dashboard_shows_recent_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        User::factory()->count(8)->create(['role' => 'developer']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recentUsers');

        $recentUsers = $response->viewData('recentUsers');
        $this->assertCount(5, $recentUsers); // Should limit to 5
    }

    public function test_admin_dashboard_shows_request_stats()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        $apiProxy = ApiProxy::factory()->create();

        // Create some requests
        RequestLog::factory()->count(5)->create([
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requested_at' => today()
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);

        $stats = $response->viewData('stats');
        $this->assertEquals(5, $stats['requests_today']);
    }

    public function test_admin_dashboard_handles_no_data()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('stats');

        $stats = $response->viewData('stats');
        $this->assertEquals(1, $stats['total_users']); // Just the admin
        $this->assertEquals(0, $stats['total_apps']);
        $this->assertEquals(0, $stats['total_api_keys']);
        $this->assertEquals(0, $stats['requests_today']);
    }

    public function test_admin_dashboard_shows_basic_data()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas(['stats', 'recentUsers', 'recentApps']);

        // Just check that the basic data structure is there
        $this->assertIsArray($response->viewData('stats'));
    }

    public function test_admin_dashboard_shows_recent_apps_with_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);

        App::factory()->count(3)->create(['user_id' => $user->id]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewHas('recentApps');

        $recentApps = $response->viewData('recentApps');
        $this->assertCount(3, $recentApps);
        // Check that the relationship is loaded
        $this->assertNotNull($recentApps->first()->user);
    }

    public function test_admin_dashboard_shows_weekly_stats()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        $apiProxy = ApiProxy::factory()->create();

        // Create requests this week
        RequestLog::factory()->count(5)->create([
            'api_key_id' => $apiKey->id,
            'api_proxy_id' => $apiProxy->id,
            'requested_at' => now()->startOfWeek()->addDay()
        ]);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $stats = $response->viewData('stats');
        $this->assertEquals(5, $stats['requests_this_week']);
    }

    public function test_admin_dashboard_performance()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create a large dataset to test performance
        $users = User::factory()->count(100)->create(['role' => 'developer']);
        $apps = App::factory()->count(50)->create(['user_id' => $users->random()->id]);
        $apiKeys = ApiKey::factory()->count(200)->create(['app_id' => $apps->random()->id]);
        
        // Don't create too many request logs to keep test fast
        RequestLog::factory()->count(100)->create(['api_key_id' => $apiKeys->random()->id]);

        $startTime = microtime(true);
        $response = $this->actingAs($admin)->get('/admin/dashboard');
        $endTime = microtime(true);

        $response->assertStatus(200);
        
        // Dashboard should load in reasonable time (less than 2 seconds)
        $this->assertLessThan(2.0, $endTime - $startTime);
    }
}
