<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;

class DeveloperApiKeyTest extends TestCase
{
    use RefreshDatabase;

    public function test_developer_can_view_api_keys_index()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'name' => 'Test Key']);

        $response = $this->actingAs($user)->get('/developer/api-keys');

        $response->assertStatus(200);
        $response->assertViewIs('developer.api-keys.index');
        $response->assertSee('Test Key');
    }

    public function test_developer_can_create_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get('/developer/api-keys/create');

        $response->assertStatus(200);
        $response->assertViewIs('developer.api-keys.create');
        $response->assertSee($app->name);
    }

    public function test_developer_can_store_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->post('/developer/api-keys', [
            'app_id' => $app->id,
            'name' => 'New API Key',
            'expires_at' => now()->addYear()->format('Y-m-d H:i:s'),
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('api_keys', [
            'app_id' => $app->id,
            'name' => 'New API Key',
            'is_active' => true,
        ]);
    }

    public function test_developer_cannot_create_api_key_for_other_users_app()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);

        $response = $this->actingAs($user1)->post('/developer/api-keys', [
            'app_id' => $app->id,
            'name' => 'Unauthorized Key',
        ]);

        $response->assertStatus(404); // Validation error
        $this->assertDatabaseMissing('api_keys', [
            'name' => 'Unauthorized Key',
        ]);
    }

    public function test_developer_can_view_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'name' => 'View Key']);

        $response = $this->actingAs($user)->get("/developer/api-keys/{$apiKey->id}");

        $response->assertStatus(200);
        $response->assertViewIs('developer.api-keys.show');
        $response->assertSee('View Key');
    }

    public function test_developer_cannot_view_other_users_api_key()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user1)->get("/developer/api-keys/{$apiKey->id}");

        $response->assertStatus(404);
    }

    public function test_developer_can_edit_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user)->get("/developer/api-keys/{$apiKey->id}/edit");

        $response->assertStatus(200);
        $response->assertViewIs('developer.api-keys.edit');
    }

    public function test_developer_can_update_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id, 'name' => 'Old Name']);

        $response = $this->actingAs($user)->put("/developer/api-keys/{$apiKey->id}", [
            'name' => 'Updated Name',
            'is_active' => '0',
            'expires_at' => now()->addMonth()->format('Y-m-d H:i:s'),
        ]);

        $response->assertRedirect("/developer/api-keys/{$apiKey->id}");
        $this->assertDatabaseHas('api_keys', [
            'id' => $apiKey->id,
            'name' => 'Updated Name',
            'is_active' => false,
        ]);
    }

    public function test_developer_can_delete_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user)->delete("/developer/api-keys/{$apiKey->id}");

        $response->assertRedirect('/developer/api-keys');
        $this->assertDatabaseMissing('api_keys', ['id' => $apiKey->id]);
    }

    public function test_developer_can_regenerate_api_key()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        $originalHash = $apiKey->key_hash;

        $response = $this->actingAs($user)->post("/developer/api-keys/{$apiKey->id}/regenerate");

        $response->assertRedirect("/developer/api-keys/{$apiKey->id}");
        $apiKey->refresh();
        $this->assertNotEquals($originalHash, $apiKey->key_hash);
    }

    public function test_api_key_creation_validates_required_fields()
    {
        $user = User::factory()->create(['role' => 'developer']);

        $response = $this->actingAs($user)->post('/developer/api-keys', []);

        $response->assertSessionHasErrors(['app_id', 'name']);
    }

    public function test_api_key_creation_validates_app_ownership()
    {
        $user1 = User::factory()->create(['role' => 'developer']);
        $user2 = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user2->id]);

        $response = $this->actingAs($user1)->post('/developer/api-keys', [
            'app_id' => $app->id,
            'name' => 'Test Key',
        ]);

        $response->assertStatus(404);
    }

    public function test_api_key_update_validates_expiration_date()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);

        $response = $this->actingAs($user)->put("/developer/api-keys/{$apiKey->id}", [
            'name' => 'Updated Name',
            'expires_at' => now()->subDay()->format('Y-m-d H:i:s'), // Past date
        ]);

        $response->assertSessionHasErrors(['expires_at']);
    }

    public function test_api_key_creation_shows_new_key_once()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->post('/developer/api-keys', [
            'app_id' => $app->id,
            'name' => 'New Key',
        ]);

        $apiKey = ApiKey::where('name', 'New Key')->first();
        $response->assertRedirect("/developer/api-keys/{$apiKey->id}");
        $response->assertSessionHas('new_api_key');
    }

    public function test_api_key_index_shows_pagination()
    {
        $user = User::factory()->create(['role' => 'developer']);
        $app = App::factory()->create(['user_id' => $user->id]);
        
        // Create more than one page of API keys
        ApiKey::factory()->count(30)->create(['app_id' => $app->id]);

        $response = $this->actingAs($user)->get('/developer/api-keys');

        $response->assertStatus(200);
        $response->assertViewHas('apiKeys');
        
        $apiKeys = $response->viewData('apiKeys');
        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $apiKeys);
    }
}
