<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Console\Scheduling\Schedule;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Global middleware
        $middleware->append(\App\Http\Middleware\SecurityHeadersMiddleware::class);

        // Middleware aliases
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'developer' => \App\Http\Middleware\DeveloperMiddleware::class,
            'api.key' => \App\Http\Middleware\ApiKeyMiddleware::class,
            'rate.limit' => \App\Http\Middleware\RateLimitMiddleware::class,
            'validate.api' => \App\Http\Middleware\ValidateApiRequestMiddleware::class,
        ]);
    })
    ->withSchedule(function (Schedule $schedule): void {
        // Clean up old request logs daily at 2 AM
        $schedule->command('app:cleanup-logs --days=30')->dailyAt('02:00');

        // Clear expired sessions weekly on Sunday at 3 AM
        $schedule->command('session:gc')->weekly()->sundays()->at('03:00');
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
