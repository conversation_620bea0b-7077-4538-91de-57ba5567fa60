@extends('layouts.developer')

@php
    $pageTitle = 'Create API Key';
@endphp

@section('title', 'Create API Key - ' . config('app.name'))

@section('content')
<div class="px-4 py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-secondary-900 sm:text-3xl">Create API Key</h1>
        <p class="mt-2 text-secondary-600">Generate a new API key for your application</p>
    </div>

    <div class="max-w-3xl">

    <div class="card">
        <div class="card-body">
            <form action="{{ route('developer.api-keys.store') }}" method="POST">
                @csrf

                <div class="form-group">
                    <label for="app_id" class="form-label">Application *</label>
                    <select name="app_id" id="app_id" class="form-input" required>
                        <option value="">Select an application</option>
                        @foreach($apps as $app)
                            <option value="{{ $app->id }}" {{ (old('app_id', request('app_id')) == $app->id) ? 'selected' : '' }}>
                                {{ $app->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('app_id')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="name" class="form-label">API Key Name *</label>
                    <input type="text" name="name" id="name" class="form-input" value="{{ old('name') }}" required>
                    <p class="form-help">A descriptive name to help you identify this API key</p>
                    @error('name')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="expires_at" class="form-label">Expiration Date</label>
                    <input type="datetime-local" name="expires_at" id="expires_at" class="form-input" value="{{ old('expires_at') }}">
                    <p class="form-help">Leave empty for no expiration</p>
                    @error('expires_at')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800">Important Security Information</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Your API key will be shown only once after creation</li>
                                    <li>Store it securely and never share it publicly</li>
                                    <li>Use environment variables to store API keys in your applications</li>
                                    <li>You can regenerate or revoke this key at any time</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4">
                    <a href="{{ route('developer.api-keys.index') }}" class="btn-outline">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                        </svg>
                        Create API Key
                    </button>
                </div>
            </form>
        </div>
    </div>
    </div>
</div>
@endsection
