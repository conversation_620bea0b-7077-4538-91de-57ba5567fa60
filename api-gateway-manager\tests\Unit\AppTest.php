<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\App;
use App\Models\ApiKey;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AppTest extends TestCase
{
    use RefreshDatabase;

    public function test_app_belongs_to_user()
    {
        $user = User::factory()->create();
        $app = App::factory()->create(['user_id' => $user->id]);
        
        $this->assertInstanceOf(User::class, $app->user);
        $this->assertEquals($user->id, $app->user->id);
    }

    public function test_app_has_many_api_keys()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        $this->assertTrue($app->apiKeys()->exists());
        $this->assertEquals(1, $app->apiKeys()->count());
        $this->assertEquals($apiKey->id, $app->apiKeys()->first()->id);
    }

    public function test_app_can_have_multiple_api_keys()
    {
        $app = App::factory()->create();
        ApiKey::factory()->count(3)->create(['app_id' => $app->id]);
        
        $this->assertEquals(3, $app->apiKeys()->count());
    }

    public function test_app_factory_creates_valid_app()
    {
        $app = App::factory()->create();
        
        $this->assertNotNull($app->name);
        $this->assertNotNull($app->description);
        $this->assertNotNull($app->callback_url);
        $this->assertTrue($app->is_active);
        $this->assertInstanceOf(User::class, $app->user);
    }

    public function test_app_factory_can_create_inactive_app()
    {
        $app = App::factory()->inactive()->create();
        
        $this->assertFalse($app->is_active);
    }

    public function test_app_casts_attributes_correctly()
    {
        $app = App::factory()->create([
            'is_active' => true,
            'created_at' => now(),
        ]);
        
        $this->assertIsBool($app->is_active);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $app->created_at);
    }

    public function test_app_fillable_attributes()
    {
        $user = User::factory()->create();
        $data = [
            'user_id' => $user->id,
            'name' => 'Test App',
            'description' => 'Test Description',
            'callback_url' => 'https://example.com/callback',
            'is_active' => false,
        ];
        
        $app = App::create($data);
        
        $this->assertEquals('Test App', $app->name);
        $this->assertEquals('Test Description', $app->description);
        $this->assertEquals('https://example.com/callback', $app->callback_url);
        $this->assertFalse($app->is_active);
        $this->assertEquals($user->id, $app->user_id);
    }

    public function test_app_scope_active()
    {
        App::factory()->create(['is_active' => true]);
        App::factory()->create(['is_active' => false]);
        
        $activeApps = App::active()->get();
        
        $this->assertEquals(1, $activeApps->count());
        $this->assertTrue($activeApps->first()->is_active);
    }

    public function test_app_scope_inactive()
    {
        App::factory()->create(['is_active' => true]);
        App::factory()->create(['is_active' => false]);
        
        $inactiveApps = App::inactive()->get();
        
        $this->assertEquals(1, $inactiveApps->count());
        $this->assertFalse($inactiveApps->first()->is_active);
    }

    public function test_app_deletion_cascades_to_api_keys()
    {
        $app = App::factory()->create();
        $apiKey = ApiKey::factory()->create(['app_id' => $app->id]);
        
        $this->assertDatabaseHas('api_keys', ['id' => $apiKey->id]);
        
        $app->delete();
        
        $this->assertDatabaseMissing('api_keys', ['id' => $apiKey->id]);
    }

    public function test_app_has_timestamps()
    {
        $app = App::factory()->create();
        
        $this->assertNotNull($app->created_at);
        $this->assertNotNull($app->updated_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $app->created_at);
        $this->assertInstanceOf(\Illuminate\Support\Carbon::class, $app->updated_at);
    }
}
