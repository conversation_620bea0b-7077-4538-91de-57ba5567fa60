<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\ApiProxy;

class ApiDocumentationTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_view_api_documentation_index()
    {
        $activeProxy = ApiProxy::factory()->create([
            'name' => 'Active API',
            'is_active' => true
        ]);
        $inactiveProxy = ApiProxy::factory()->create([
            'name' => 'Inactive API',
            'is_active' => false
        ]);

        $response = $this->get('/docs');

        $response->assertStatus(200);
        $response->assertViewIs('docs.index');
        $response->assertSee('Active API');
        $response->assertDontSee('Inactive API');
    }

    public function test_can_view_specific_api_proxy_documentation()
    {
        $apiProxy = ApiProxy::factory()->create([
            'name' => 'Test API',
            'description' => 'Test API Description',
            'is_active' => true
        ]);

        $response = $this->get("/docs/{$apiProxy->id}");

        $response->assertStatus(200);
        $response->assertViewIs('docs.show');
        $response->assertSee('Test API');
        $response->assertSee('Test API Description');
    }

    public function test_cannot_view_inactive_api_proxy_documentation()
    {
        $apiProxy = ApiProxy::factory()->create([
            'name' => 'Inactive API',
            'is_active' => false
        ]);

        $response = $this->get("/docs/{$apiProxy->id}");

        $response->assertStatus(404);
    }

    public function test_can_access_openapi_specification()
    {
        ApiProxy::factory()->count(3)->create(['is_active' => true]);

        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
        
        $data = $response->json();
        $this->assertArrayHasKey('openapi', $data);
        $this->assertArrayHasKey('info', $data);
        $this->assertArrayHasKey('paths', $data);
    }

    public function test_openapi_specification_includes_active_proxies_only()
    {
        $activeProxy = ApiProxy::factory()->create([
            'proxy_path' => '/active-api',
            'is_active' => true
        ]);
        $inactiveProxy = ApiProxy::factory()->create([
            'proxy_path' => '/inactive-api',
            'is_active' => false
        ]);

        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertArrayHasKey('/active-api', $data['paths']);
        $this->assertArrayNotHasKey('/inactive-api', $data['paths']);
    }

    public function test_openapi_specification_includes_correct_methods()
    {
        $apiProxy = ApiProxy::factory()->create([
            'proxy_path' => '/test-api',
            'allowed_methods' => ['GET', 'POST'],
            'is_active' => true
        ]);

        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertArrayHasKey('get', $data['paths']['/test-api']);
        $this->assertArrayHasKey('post', $data['paths']['/test-api']);
        $this->assertArrayNotHasKey('put', $data['paths']['/test-api']);
        $this->assertArrayNotHasKey('delete', $data['paths']['/test-api']);
    }

    public function test_openapi_specification_includes_authentication_when_required()
    {
        $authProxy = ApiProxy::factory()->create([
            'proxy_path' => '/auth-api',
            'requires_auth' => true,
            'is_active' => true
        ]);
        $noAuthProxy = ApiProxy::factory()->create([
            'proxy_path' => '/no-auth-api',
            'requires_auth' => false,
            'is_active' => true
        ]);

        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Auth required API should have security requirements
        $this->assertArrayHasKey('security', $data['paths']['/auth-api']['get']);
        
        // No auth API should not have security requirements
        $this->assertArrayNotHasKey('security', $data['paths']['/no-auth-api']['get']);
    }

    public function test_openapi_specification_has_correct_structure()
    {
        ApiProxy::factory()->create(['is_active' => true]);

        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        
        $data = $response->json();
        
        // Check required OpenAPI fields
        $this->assertEquals('3.0.0', $data['openapi']);
        $this->assertArrayHasKey('title', $data['info']);
        $this->assertArrayHasKey('version', $data['info']);
        $this->assertArrayHasKey('description', $data['info']);
        
        // Check security schemes
        $this->assertArrayHasKey('components', $data);
        $this->assertArrayHasKey('securitySchemes', $data['components']);
        $this->assertArrayHasKey('ApiKeyAuth', $data['components']['securitySchemes']);
    }

    public function test_documentation_index_shows_no_apis_message_when_empty()
    {
        // No active APIs
        ApiProxy::factory()->create(['is_active' => false]);

        $response = $this->get('/docs');

        $response->assertStatus(200);
        $response->assertSee('No active APIs available');
    }

    public function test_documentation_index_orders_apis_by_name()
    {
        ApiProxy::factory()->create(['name' => 'Z API', 'is_active' => true]);
        ApiProxy::factory()->create(['name' => 'A API', 'is_active' => true]);
        ApiProxy::factory()->create(['name' => 'M API', 'is_active' => true]);

        $response = $this->get('/docs');

        $response->assertStatus(200);
        
        $apiProxies = $response->viewData('apiProxies');
        $names = $apiProxies->pluck('name')->toArray();
        
        $this->assertEquals(['A API', 'M API', 'Z API'], $names);
    }

    public function test_api_proxy_documentation_shows_all_details()
    {
        $apiProxy = ApiProxy::factory()->create([
            'name' => 'Detailed API',
            'description' => 'A very detailed API',
            'proxy_path' => '/detailed',
            'target_url' => 'https://api.example.com',
            'allowed_methods' => ['GET', 'POST', 'PUT'],
            'requires_auth' => true,
            'timeout' => 30,
            'is_active' => true
        ]);

        $response = $this->get("/docs/{$apiProxy->id}");

        $response->assertStatus(200);
        $response->assertSee('Detailed API');
        $response->assertSee('A very detailed API');
        $response->assertSee('/detailed');
        $response->assertSee('https://api.example.com');
        $response->assertSee('GET');
        $response->assertSee('POST');
        $response->assertSee('PUT');
        $response->assertSee('Authentication Required');
        $response->assertSee('30 seconds');
    }

    public function test_openapi_specification_handles_empty_database()
    {
        // No API proxies at all
        $response = $this->get('/docs/openapi.json');

        $response->assertStatus(200);
        
        $data = $response->json();
        $this->assertArrayHasKey('paths', $data);
        $this->assertEmpty($data['paths']);
    }
}
