@extends('layouts.app')

@section('title', '500 - Internal Server Error')

@section('content')
<div class="min-h-screen flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Illustration -->
        <div class="mx-auto">
            <svg class="h-32 w-32 sm:h-40 sm:w-40 text-warning-600 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
        </div>

        <!-- <PERSON>rror Code -->
        <div>
            <h1 class="text-6xl sm:text-8xl font-bold text-secondary-900">500</h1>
            <h2 class="mt-4 text-2xl sm:text-3xl font-bold text-secondary-900">Internal Server Error</h2>
            <p class="mt-4 text-lg text-secondary-600">
                Something went wrong on our end. We're working to fix this issue. Please try again in a few moments.
            </p>
        </div>

        <!-- Actions -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button onclick="window.location.reload()" class="w-full sm:w-auto btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Try Again
            </button>
            
            <button onclick="history.back()" class="w-full sm:w-auto btn-outline">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
        </div>

        <!-- Status Information -->
        <div class="pt-8 border-t border-secondary-200">
            <div class="bg-warning-50 border border-warning-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-warning-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div class="text-left">
                        <h3 class="text-sm font-medium text-warning-800">System Status</h3>
                        <p class="text-sm text-warning-700 mt-1">
                            Our team has been notified and is investigating the issue.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error ID for Support -->
        <div class="pt-4">
            <div class="bg-secondary-50 border border-secondary-200 rounded-lg p-3">
                <p class="text-xs text-secondary-600">
                    Error ID: <code class="font-mono">{{ uniqid('err_') }}</code>
                </p>
                <p class="text-xs text-secondary-500 mt-1">
                    Please include this ID when contacting support
                </p>
            </div>
        </div>

        <!-- Alternative Actions -->
        <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            @auth
                @if(auth()->user()->role === 'admin')
                    <a href="{{ route('admin.dashboard') }}" class="w-full sm:w-auto btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Admin Dashboard
                    </a>
                @else
                    <a href="{{ route('developer.dashboard') }}" class="w-full sm:w-auto btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        Dashboard
                    </a>
                @endif
            @else
                <a href="{{ route('portal.index') }}" class="w-full sm:w-auto btn-secondary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Home
                </a>
            @endauth
        </div>

        <!-- Support Links -->
        <div class="pt-6">
            <p class="text-sm text-secondary-500 mb-4">Need immediate assistance?</p>
            <div class="flex flex-col sm:flex-row sm:justify-center space-y-2 sm:space-y-0 sm:space-x-6">
                <a href="{{ route('portal.support') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    🆘 Emergency Support
                </a>
                <a href="https://status.{{ parse_url(config('app.url'), PHP_URL_HOST) }}" target="_blank" class="text-sm text-primary-600 hover:text-primary-500">
                    📊 System Status
                </a>
                <a href="{{ route('docs.index') }}" class="text-sm text-primary-600 hover:text-primary-500">
                    📚 Documentation
                </a>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Track 500 errors for monitoring
if (typeof gtag !== 'undefined') {
    gtag('event', 'server_error', {
        'page_location': window.location.href,
        'page_referrer': document.referrer,
        'error_id': '{{ uniqid("err_") }}',
        'timestamp': new Date().toISOString()
    });
}

// Auto-retry mechanism
let retryCount = 0;
const maxRetries = 3;
const retryDelay = 5000; // 5 seconds

function autoRetry() {
    if (retryCount < maxRetries) {
        retryCount++;
        console.log(`Auto-retry attempt ${retryCount}/${maxRetries}`);
        
        setTimeout(() => {
            // Check if the server is responding
            fetch(window.location.href, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        autoRetry();
                    }
                })
                .catch(() => {
                    autoRetry();
                });
        }, retryDelay * retryCount);
    }
}

// Start auto-retry after 10 seconds
setTimeout(autoRetry, 10000);

// Show retry countdown
let countdownElement = document.createElement('div');
countdownElement.className = 'mt-4 text-sm text-secondary-500';
countdownElement.innerHTML = 'Automatic retry in <span id="countdown">10</span> seconds...';
document.querySelector('.pt-8').appendChild(countdownElement);

let countdown = 10;
const countdownInterval = setInterval(() => {
    countdown--;
    const countdownSpan = document.getElementById('countdown');
    if (countdownSpan) {
        countdownSpan.textContent = countdown;
    }
    
    if (countdown <= 0) {
        clearInterval(countdownInterval);
        countdownElement.textContent = 'Retrying...';
    }
}, 1000);
</script>
@endpush
@endsection
