<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Api<PERSON>ey extends Model
{
    use HasFactory;

    protected $fillable = [
        'app_id',
        'name',
        'key_hash',
        'key_prefix',
        'is_active',
        'last_used_at',
        'expires_at',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'last_used_at' => 'datetime',
            'expires_at' => 'datetime',
        ];
    }

    /**
     * Get the app that owns this API key
     */
    public function app()
    {
        return $this->belongsTo(App::class);
    }

    /**
     * Get the rate limits for this API key
     */
    public function rateLimits()
    {
        return $this->hasMany(RateLimit::class);
    }

    /**
     * Get the request logs for this API key
     */
    public function requestLogs()
    {
        return $this->hasMany(RequestLog::class);
    }

    /**
     * Generate a new API key
     */
    public static function generateKey(): string
    {
        return 'agm_' . Str::random(32);
    }

    /**
     * Hash an API key for storage
     */
    public static function hashKey(string $key): string
    {
        return hash('sha256', $key);
    }

    /**
     * Get the prefix from a key
     */
    public static function getKeyPrefix(string $key): string
    {
        return substr($key, 0, 8);
    }

    /**
     * Check if the key is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Update last used timestamp
     */
    public function markAsUsed(): void
    {
        $this->update(['last_used_at' => now()]);
    }
}
