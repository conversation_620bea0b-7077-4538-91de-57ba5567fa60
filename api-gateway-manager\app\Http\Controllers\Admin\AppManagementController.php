<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\App;
use App\Models\User;

class AppManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = App::with(['user', 'apiKeys'])->withCount(['apiKeys', 'apiKeys as active_api_keys_count' => function($query) {
            $query->where('is_active', true);
        }]);

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $apps = $query->orderBy('created_at', 'desc')->paginate(15);
        $users = User::where('role', 'developer')->get();

        return view('admin.apps.index', compact('apps', 'users'));
    }

    /**
     * Display the specified resource.
     */
    public function show(App $app)
    {
        $app->load(['user', 'apiKeys' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        // Get recent activity for this app
        $recentLogs = \App\Models\RequestLog::whereHas('apiKey', function($query) use ($app) {
            $query->where('app_id', $app->id);
        })->with(['apiKey', 'apiProxy'])
          ->orderBy('requested_at', 'desc')
          ->take(20)
          ->get();

        // Get usage statistics
        $stats = [
            'total_requests' => \App\Models\RequestLog::whereHas('apiKey', function($query) use ($app) {
                $query->where('app_id', $app->id);
            })->count(),
            'requests_today' => \App\Models\RequestLog::whereHas('apiKey', function($query) use ($app) {
                $query->where('app_id', $app->id);
            })->whereDate('requested_at', today())->count(),
            'requests_this_week' => \App\Models\RequestLog::whereHas('apiKey', function($query) use ($app) {
                $query->where('app_id', $app->id);
            })->where('requested_at', '>=', now()->startOfWeek())->count(),
            'avg_response_time' => \App\Models\RequestLog::whereHas('apiKey', function($query) use ($app) {
                $query->where('app_id', $app->id);
            })->avg('response_time_ms'),
        ];

        return view('admin.apps.show', compact('app', 'recentLogs', 'stats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(App $app)
    {
        $users = User::where('role', 'developer')->get();
        return view('admin.apps.edit', compact('app', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, App $app)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'callback_url' => 'nullable|url|max:255',
            'user_id' => 'required|exists:users,id',
            'is_active' => 'boolean',
        ]);

        $app->update([
            'name' => $request->name,
            'description' => $request->description,
            'callback_url' => $request->callback_url,
            'user_id' => $request->user_id,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('admin.apps.show', $app)
            ->with('success', 'App updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(App $app)
    {
        $app->delete();

        return redirect()->route('admin.apps.index')
            ->with('success', 'App deleted successfully!');
    }

    /**
     * Toggle app active status
     */
    public function toggleStatus(App $app)
    {
        $app->update(['is_active' => !$app->is_active]);

        $status = $app->is_active ? 'activated' : 'deactivated';
        return redirect()->route('admin.apps.index')
            ->with('success', "App {$status} successfully!");
    }

    /**
     * View app's API keys
     */
    public function apiKeys(App $app)
    {
        $app->load(['user', 'apiKeys' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return view('admin.apps.api-keys', compact('app'));
    }

    /**
     * Deactivate all API keys for an app
     */
    public function deactivateApiKeys(App $app)
    {
        $app->apiKeys()->update(['is_active' => false]);

        return redirect()->route('admin.apps.api-keys', $app)
            ->with('success', 'All API keys for this app have been deactivated.');
    }
}
